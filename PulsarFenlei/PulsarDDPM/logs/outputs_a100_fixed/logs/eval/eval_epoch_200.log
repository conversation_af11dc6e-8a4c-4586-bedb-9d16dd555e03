2025-05-27 16:11:06,645 - evaluation.metrics - INFO - Generating 995 samples for evaluation with batch size 50
2025-05-27 16:11:06,645 - evaluation.metrics - INFO - Using external sampler: PulsarAdaptiveSampler
2025-05-27 16:11:06,645 - evaluation.metrics - INFO - Will generate 995 samples in 20 batches of size 50
2025-05-27 16:11:06,646 - evaluation.metrics - INFO - Using external sampler for batch 1/20
2025-05-27 16:11:08,197 - evaluation.metrics - INFO - Using external sampler for batch 2/20
2025-05-27 16:11:08,302 - evaluation.metrics - INFO - Using external sampler for batch 3/20
2025-05-27 16:11:08,522 - evaluation.metrics - INFO - Using external sampler for batch 4/20
2025-05-27 16:11:08,747 - evaluation.metrics - INFO - Using external sampler for batch 5/20
2025-05-27 16:11:08,959 - evaluation.metrics - INFO - Generated 250/995 samples
2025-05-27 16:11:08,959 - evaluation.metrics - INFO - Using external sampler for batch 6/20
2025-05-27 16:11:09,159 - evaluation.metrics - INFO - Using external sampler for batch 7/20
2025-05-27 16:11:09,379 - evaluation.metrics - INFO - Using external sampler for batch 8/20
2025-05-27 16:11:09,665 - evaluation.metrics - INFO - Using external sampler for batch 9/20
2025-05-27 16:11:09,850 - evaluation.metrics - INFO - Using external sampler for batch 10/20
2025-05-27 16:11:10,058 - evaluation.metrics - INFO - Generated 500/995 samples
2025-05-27 16:11:10,059 - evaluation.metrics - INFO - Using external sampler for batch 11/20
2025-05-27 16:11:10,258 - evaluation.metrics - INFO - Using external sampler for batch 12/20
2025-05-27 16:11:10,463 - evaluation.metrics - INFO - Using external sampler for batch 13/20
2025-05-27 16:11:10,654 - evaluation.metrics - INFO - Using external sampler for batch 14/20
2025-05-27 16:11:10,872 - evaluation.metrics - INFO - Using external sampler for batch 15/20
2025-05-27 16:11:11,063 - evaluation.metrics - INFO - Generated 750/995 samples
2025-05-27 16:11:11,063 - evaluation.metrics - INFO - Using external sampler for batch 16/20
2025-05-27 16:11:11,271 - evaluation.metrics - INFO - Using external sampler for batch 17/20
2025-05-27 16:11:11,465 - evaluation.metrics - INFO - Using external sampler for batch 18/20
2025-05-27 16:11:11,666 - evaluation.metrics - INFO - Using external sampler for batch 19/20
2025-05-27 16:11:11,873 - evaluation.metrics - INFO - Using external sampler for batch 20/20
2025-05-27 16:11:12,065 - evaluation.metrics - INFO - Generated 995/995 samples
2025-05-27 16:11:12,378 - evaluation.metrics - INFO - Successfully generated 995/995 samples
2025-05-27 16:11:12,381 - evaluation.metrics - INFO - Final samples already in valid range: [-0.823753, 0.853172]
2025-05-27 16:11:12,581 - evaluation.metrics - INFO - Generated 995 samples in 5.94 seconds
2025-05-27 16:11:12,581 - evaluation.metrics - INFO - Sample range: [-0.8238, 0.8532]
2025-05-27 16:11:15,037 - evaluation.metrics - INFO - Starting model evaluation...
2025-05-27 16:11:15,037 - evaluation.metrics - INFO - Preprocessing real images for evaluation...
2025-05-27 16:11:15,037 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:11:15,037 - evaluation.metrics - INFO - real images value range: [-1.0000, 1.0000]
2025-05-27 16:11:15,038 - evaluation.metrics - INFO - real images channel variances: [0.15041910111904144, 0.16885530948638916, 0.13878194987773895]
2025-05-27 16:11:15,038 - evaluation.metrics - INFO - Preprocessed real images: shape=torch.Size([995, 3, 32, 32]), range=[-1.0000, 1.0000]
2025-05-27 16:11:15,038 - evaluation.metrics - INFO - Preprocessing generated images for evaluation...
2025-05-27 16:11:15,038 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:11:15,040 - evaluation.metrics - INFO - generated images value range: [-0.8238, 0.8532]
2025-05-27 16:11:15,078 - evaluation.metrics - INFO - generated images channel variances: [0.0026227342896163464, 0.05612324923276901, 0.00016557314665988088]
2025-05-27 16:11:15,079 - evaluation.metrics - INFO - Preprocessed generated images: shape=torch.Size([995, 3, 32, 32]), range=[-0.8238, 0.8532]
2025-05-27 16:11:15,079 - evaluation.metrics - INFO - Verifying local Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:15,079 - evaluation.metrics - INFO - Verifying Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:16,601 - evaluation.metrics - INFO - State dict contains 484 parameters
2025-05-27 16:11:16,601 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:16,601 - evaluation.metrics - INFO - Feature extraction mode: both
2025-05-27 16:11:16,602 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:11:21,604 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:22,422 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:11:22,897 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:22,900 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:11:22,900 - evaluation.metrics - INFO - Inception-v3 model loaded in 6.30 seconds
2025-05-27 16:11:22,900 - evaluation.metrics - INFO - Supporting both 2048-dim pool3 features (FID) and 1000-dim logits (IS)
2025-05-27 16:11:22,970 - evaluation.metrics - INFO - Inception model verification successful
2025-05-27 16:11:22,974 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 16:11:22,974 - evaluation.metrics - INFO - Loading Inception model for FID calculation (2048-dim features)...
2025-05-27 16:11:22,975 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:22,975 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 16:11:22,975 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:11:27,303 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:27,652 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:11:27,678 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:27,678 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:11:27,679 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.70 seconds
2025-05-27 16:11:27,679 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 16:11:27,679 - evaluation.metrics - INFO - Loading Inception model for IS calculation (1000-dim features)...
2025-05-27 16:11:27,680 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:27,680 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 16:11:27,680 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:11:33,386 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:33,676 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:11:34,828 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:11:34,830 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:11:34,830 - evaluation.metrics - INFO - Inception-v3 model loaded in 7.15 seconds
2025-05-27 16:11:34,830 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 16:11:34,832 - evaluation.metrics - INFO - Calculating FID with 2048-dim pool3 features...
2025-05-27 16:11:34,832 - evaluation.metrics - INFO - Using precomputed FID baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 16:11:34,832 - evaluation.metrics - INFO - Calculating FID using precomputed baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 16:11:34,836 - evaluation.metrics - INFO - Loaded baseline: 199 samples, 2048D features
2025-05-27 16:11:34,838 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:11:36,452 - evaluation.metrics - INFO - Computing FID score with baseline...
2025-05-27 16:12:36,223 - evaluation.metrics - INFO - FID calculation completed in 61.39 seconds
2025-05-27 16:12:36,223 - evaluation.metrics - INFO - FID score: 267.5613
2025-05-27 16:12:36,226 - evaluation.metrics - INFO - Calculating IS with 1000-dim classification features...
2025-05-27 16:12:36,226 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:12:36,228 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:12:36,769 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:12:36,772 - evaluation.metrics - INFO - Inception Score calculation completed in 0.55 seconds
2025-05-27 16:12:36,772 - evaluation.metrics - INFO - Inception Score: 1.3015 ± 0.0356
2025-05-27 16:12:36,772 - evaluation.metrics - INFO - Evaluation completed in 81.74 seconds
2025-05-27 16:12:36,772 - evaluation.metrics - INFO - Results: FID = 267.5613, IS = 1.3015 ± 0.0356
2025-05-27 16:12:36,778 - evaluation.metrics - INFO - Starting channel-specific evaluation with simplified approach...
2025-05-27 16:12:36,778 - evaluation.metrics - INFO - Will evaluate 3 channels
2025-05-27 16:12:36,778 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 16:12:36,778 - evaluation.metrics - INFO - Loading Inception models with differential configuration...
2025-05-27 16:12:36,778 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:12:36,778 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 16:12:36,778 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:12:41,407 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:12:41,811 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:12:41,837 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:12:41,838 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:12:41,838 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.06 seconds
2025-05-27 16:12:41,838 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 16:12:41,838 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:12:41,839 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 16:12:41,839 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:12:46,882 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:12:47,245 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:12:47,275 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:12:47,275 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:12:47,275 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.44 seconds
2025-05-27 16:12:47,275 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 16:12:47,280 - evaluation.metrics - INFO - Evaluating Period-DM surface (channel 0)...
2025-05-27 16:12:47,283 - evaluation.metrics - INFO - Channel 0 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.6113, -0.0442]
2025-05-27 16:12:47,286 - evaluation.metrics - INFO - Evaluating Period-DM surface using channel replication method
2025-05-27 16:12:47,286 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:12:47,286 - evaluation.metrics - INFO - Calculating FID for Period-DM surface with 2048-dim features...
2025-05-27 16:12:47,287 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:12:47,288 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:12:47,288 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:12:47,290 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:12:47,990 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:12:49,224 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:14:15,920 - evaluation.metrics - INFO - FID calculation completed in 88.63 seconds
2025-05-27 16:14:15,921 - evaluation.metrics - INFO - FID score: 343.7116
2025-05-27 16:14:15,921 - evaluation.metrics - INFO - Calculating IS for Period-DM surface with 1000-dim features...
2025-05-27 16:14:15,922 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:14:15,932 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:14:16,474 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:14:16,477 - evaluation.metrics - INFO - Inception Score calculation completed in 0.56 seconds
2025-05-27 16:14:16,477 - evaluation.metrics - INFO - Inception Score: 1.4827 ± 0.0508
2025-05-27 16:14:16,477 - evaluation.metrics - INFO - Period-DM surface results: FID = 343.7116, IS = 1.4827 ± 0.0508
2025-05-27 16:14:16,478 - evaluation.metrics - INFO - Evaluating Phase-Subband surface (channel 1)...
2025-05-27 16:14:16,480 - evaluation.metrics - INFO - Channel 1 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.8238, 0.8532]
2025-05-27 16:14:16,487 - evaluation.metrics - INFO - Evaluating Phase-Subband surface using channel replication method
2025-05-27 16:14:16,487 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:14:16,487 - evaluation.metrics - INFO - Calculating FID for Phase-Subband surface with 2048-dim features...
2025-05-27 16:14:16,487 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:14:16,489 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:14:16,489 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:14:16,491 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:14:17,183 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:14:18,356 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:15:22,315 - evaluation.metrics - INFO - FID calculation completed in 65.83 seconds
2025-05-27 16:15:22,317 - evaluation.metrics - INFO - FID score: 340.9023
2025-05-27 16:15:22,317 - evaluation.metrics - INFO - Calculating IS for Phase-Subband surface with 1000-dim features...
2025-05-27 16:15:22,318 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:15:22,326 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:15:22,865 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:15:22,868 - evaluation.metrics - INFO - Inception Score calculation completed in 0.55 seconds
2025-05-27 16:15:22,869 - evaluation.metrics - INFO - Inception Score: 1.2304 ± 0.0146
2025-05-27 16:15:22,869 - evaluation.metrics - INFO - Phase-Subband surface results: FID = 340.9023, IS = 1.2304 ± 0.0146
2025-05-27 16:15:22,869 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface (channel 2)...
2025-05-27 16:15:22,871 - evaluation.metrics - INFO - Channel 2 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.2631, -0.1027]
2025-05-27 16:15:22,882 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface using channel replication method
2025-05-27 16:15:22,885 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:15:22,885 - evaluation.metrics - INFO - Calculating FID for Phase-Subintegration surface with 2048-dim features...
2025-05-27 16:15:22,885 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:15:22,887 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:15:22,887 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:15:22,980 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:15:23,548 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:15:24,554 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:16:31,917 - evaluation.metrics - INFO - FID calculation completed in 69.03 seconds
2025-05-27 16:16:31,918 - evaluation.metrics - INFO - FID score: 387.5698
2025-05-27 16:16:31,918 - evaluation.metrics - INFO - Calculating IS for Phase-Subintegration surface with 1000-dim features...
2025-05-27 16:16:31,918 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:16:31,922 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:16:32,460 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:16:32,463 - evaluation.metrics - INFO - Inception Score calculation completed in 0.54 seconds
2025-05-27 16:16:32,463 - evaluation.metrics - INFO - Inception Score: 1.0543 ± 0.0033
2025-05-27 16:16:32,463 - evaluation.metrics - INFO - Phase-Subintegration surface results: FID = 387.5698, IS = 1.0543 ± 0.0033
2025-05-27 16:16:32,463 - evaluation.metrics - INFO - Average results: FID = 357.3946, IS = 1.2558
2025-05-27 16:16:32,463 - evaluation.metrics - INFO - Channel-specific evaluation completed in 235.69 seconds
