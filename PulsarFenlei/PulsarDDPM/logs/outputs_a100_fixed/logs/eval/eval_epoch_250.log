2025-05-27 16:26:08,690 - evaluation.metrics - INFO - Generating 995 samples for evaluation with batch size 50
2025-05-27 16:26:08,690 - evaluation.metrics - INFO - Using external sampler: PulsarAdaptiveSampler
2025-05-27 16:26:08,690 - evaluation.metrics - INFO - Will generate 995 samples in 20 batches of size 50
2025-05-27 16:26:08,691 - evaluation.metrics - INFO - Using external sampler for batch 1/20
2025-05-27 16:26:10,356 - evaluation.metrics - INFO - Using external sampler for batch 2/20
2025-05-27 16:26:10,462 - evaluation.metrics - INFO - Using external sampler for batch 3/20
2025-05-27 16:26:10,657 - evaluation.metrics - INFO - Using external sampler for batch 4/20
2025-05-27 16:26:10,860 - evaluation.metrics - INFO - Using external sampler for batch 5/20
2025-05-27 16:26:11,061 - evaluation.metrics - INFO - Generated 250/995 samples
2025-05-27 16:26:11,061 - evaluation.metrics - INFO - Using external sampler for batch 6/20
2025-05-27 16:26:11,258 - evaluation.metrics - INFO - Using external sampler for batch 7/20
2025-05-27 16:26:11,468 - evaluation.metrics - INFO - Using external sampler for batch 8/20
2025-05-27 16:26:11,664 - evaluation.metrics - INFO - Using external sampler for batch 9/20
2025-05-27 16:26:11,868 - evaluation.metrics - INFO - Using external sampler for batch 10/20
2025-05-27 16:26:12,063 - evaluation.metrics - INFO - Generated 500/995 samples
2025-05-27 16:26:12,063 - evaluation.metrics - INFO - Using external sampler for batch 11/20
2025-05-27 16:26:12,267 - evaluation.metrics - INFO - Using external sampler for batch 12/20
2025-05-27 16:26:12,508 - evaluation.metrics - INFO - Using external sampler for batch 13/20
2025-05-27 16:26:12,749 - evaluation.metrics - INFO - Using external sampler for batch 14/20
2025-05-27 16:26:12,979 - evaluation.metrics - INFO - Using external sampler for batch 15/20
2025-05-27 16:26:13,212 - evaluation.metrics - INFO - Generated 750/995 samples
2025-05-27 16:26:13,212 - evaluation.metrics - INFO - Using external sampler for batch 16/20
2025-05-27 16:26:13,443 - evaluation.metrics - INFO - Using external sampler for batch 17/20
2025-05-27 16:26:13,663 - evaluation.metrics - INFO - Using external sampler for batch 18/20
2025-05-27 16:26:13,866 - evaluation.metrics - INFO - Using external sampler for batch 19/20
2025-05-27 16:26:14,056 - evaluation.metrics - INFO - Using external sampler for batch 20/20
2025-05-27 16:26:14,260 - evaluation.metrics - INFO - Generated 995/995 samples
2025-05-27 16:26:14,384 - evaluation.metrics - INFO - Successfully generated 995/995 samples
2025-05-27 16:26:14,385 - evaluation.metrics - INFO - Final samples already in valid range: [-0.842261, 0.906895]
2025-05-27 16:26:14,582 - evaluation.metrics - INFO - Generated 995 samples in 5.89 seconds
2025-05-27 16:26:14,583 - evaluation.metrics - INFO - Sample range: [-0.8423, 0.9069]
2025-05-27 16:26:16,289 - evaluation.metrics - INFO - Starting model evaluation...
2025-05-27 16:26:16,289 - evaluation.metrics - INFO - Preprocessing real images for evaluation...
2025-05-27 16:26:16,289 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:26:16,290 - evaluation.metrics - INFO - real images value range: [-1.0000, 1.0000]
2025-05-27 16:26:16,291 - evaluation.metrics - INFO - real images channel variances: [0.1497703641653061, 0.1689123958349228, 0.1391705870628357]
2025-05-27 16:26:16,291 - evaluation.metrics - INFO - Preprocessed real images: shape=torch.Size([995, 3, 32, 32]), range=[-1.0000, 1.0000]
2025-05-27 16:26:16,291 - evaluation.metrics - INFO - Preprocessing generated images for evaluation...
2025-05-27 16:26:16,292 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:26:16,294 - evaluation.metrics - INFO - generated images value range: [-0.8423, 0.9069]
2025-05-27 16:26:16,295 - evaluation.metrics - INFO - generated images channel variances: [0.0020425934344530106, 0.06265820562839508, 0.0001987393043236807]
2025-05-27 16:26:16,295 - evaluation.metrics - INFO - Preprocessed generated images: shape=torch.Size([995, 3, 32, 32]), range=[-0.8423, 0.9069]
2025-05-27 16:26:16,295 - evaluation.metrics - INFO - Verifying local Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:16,295 - evaluation.metrics - INFO - Verifying Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:16,893 - evaluation.metrics - INFO - State dict contains 484 parameters
2025-05-27 16:26:16,893 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:16,894 - evaluation.metrics - INFO - Feature extraction mode: both
2025-05-27 16:26:16,894 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:26:21,403 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:22,139 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:26:22,446 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:22,448 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:26:22,448 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.55 seconds
2025-05-27 16:26:22,448 - evaluation.metrics - INFO - Supporting both 2048-dim pool3 features (FID) and 1000-dim logits (IS)
2025-05-27 16:26:22,510 - evaluation.metrics - INFO - Inception model verification successful
2025-05-27 16:26:22,513 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 16:26:22,514 - evaluation.metrics - INFO - Loading Inception model for FID calculation (2048-dim features)...
2025-05-27 16:26:22,514 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:22,514 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 16:26:22,514 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:26:28,608 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:28,934 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:26:28,959 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:28,960 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:26:28,960 - evaluation.metrics - INFO - Inception-v3 model loaded in 6.45 seconds
2025-05-27 16:26:28,960 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 16:26:28,961 - evaluation.metrics - INFO - Loading Inception model for IS calculation (1000-dim features)...
2025-05-27 16:26:28,961 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:28,961 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 16:26:28,961 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:26:35,308 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:35,638 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:26:35,757 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:26:35,760 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:26:35,760 - evaluation.metrics - INFO - Inception-v3 model loaded in 6.80 seconds
2025-05-27 16:26:35,760 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 16:26:35,762 - evaluation.metrics - INFO - Calculating FID with 2048-dim pool3 features...
2025-05-27 16:26:35,762 - evaluation.metrics - INFO - Using precomputed FID baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 16:26:35,763 - evaluation.metrics - INFO - Calculating FID using precomputed baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 16:26:35,772 - evaluation.metrics - INFO - Loaded baseline: 199 samples, 2048D features
2025-05-27 16:26:35,775 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:26:36,752 - evaluation.metrics - INFO - Computing FID score with baseline...
2025-05-27 16:27:43,626 - evaluation.metrics - INFO - FID calculation completed in 67.86 seconds
2025-05-27 16:27:43,626 - evaluation.metrics - INFO - FID score: 277.9557
2025-05-27 16:27:43,629 - evaluation.metrics - INFO - Calculating IS with 1000-dim classification features...
2025-05-27 16:27:43,629 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:27:43,631 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:27:44,749 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:27:44,752 - evaluation.metrics - INFO - Inception Score calculation completed in 1.12 seconds
2025-05-27 16:27:44,752 - evaluation.metrics - INFO - Inception Score: 1.1859 ± 0.0305
2025-05-27 16:27:44,752 - evaluation.metrics - INFO - Evaluation completed in 88.46 seconds
2025-05-27 16:27:44,752 - evaluation.metrics - INFO - Results: FID = 277.9557, IS = 1.1859 ± 0.0305
2025-05-27 16:27:44,757 - evaluation.metrics - INFO - Starting channel-specific evaluation with simplified approach...
2025-05-27 16:27:44,757 - evaluation.metrics - INFO - Will evaluate 3 channels
2025-05-27 16:27:44,758 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 16:27:44,758 - evaluation.metrics - INFO - Loading Inception models with differential configuration...
2025-05-27 16:27:44,758 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:27:44,758 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 16:27:44,758 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:27:49,693 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:27:50,058 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:27:50,085 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:27:50,086 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:27:50,086 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.33 seconds
2025-05-27 16:27:50,086 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 16:27:50,087 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:27:50,087 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 16:27:50,087 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:27:54,695 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:27:55,019 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:27:55,044 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:27:55,045 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:27:55,045 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.96 seconds
2025-05-27 16:27:55,045 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 16:27:55,051 - evaluation.metrics - INFO - Evaluating Period-DM surface (channel 0)...
2025-05-27 16:27:55,053 - evaluation.metrics - INFO - Channel 0 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.5991, -0.0823]
2025-05-27 16:27:55,060 - evaluation.metrics - INFO - Evaluating Period-DM surface using channel replication method
2025-05-27 16:27:55,060 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:27:55,060 - evaluation.metrics - INFO - Calculating FID for Period-DM surface with 2048-dim features...
2025-05-27 16:27:55,060 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:27:55,061 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:27:55,061 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:27:55,063 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:27:55,725 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:27:56,856 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:29:09,197 - evaluation.metrics - INFO - FID calculation completed in 74.14 seconds
2025-05-27 16:29:09,198 - evaluation.metrics - INFO - FID score: 337.9731
2025-05-27 16:29:09,198 - evaluation.metrics - INFO - Calculating IS for Period-DM surface with 1000-dim features...
2025-05-27 16:29:09,198 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:29:09,281 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:29:09,843 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:29:09,846 - evaluation.metrics - INFO - Inception Score calculation completed in 0.65 seconds
2025-05-27 16:29:09,846 - evaluation.metrics - INFO - Inception Score: 1.2119 ± 0.0719
2025-05-27 16:29:09,846 - evaluation.metrics - INFO - Period-DM surface results: FID = 337.9731, IS = 1.2119 ± 0.0719
2025-05-27 16:29:09,846 - evaluation.metrics - INFO - Evaluating Phase-Subband surface (channel 1)...
2025-05-27 16:29:09,849 - evaluation.metrics - INFO - Channel 1 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.8423, 0.9069]
2025-05-27 16:29:09,854 - evaluation.metrics - INFO - Evaluating Phase-Subband surface using channel replication method
2025-05-27 16:29:09,854 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:29:09,854 - evaluation.metrics - INFO - Calculating FID for Phase-Subband surface with 2048-dim features...
2025-05-27 16:29:09,854 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:29:09,855 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:29:09,855 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:29:09,857 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:29:10,476 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:29:11,652 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:30:21,414 - evaluation.metrics - INFO - FID calculation completed in 71.56 seconds
2025-05-27 16:30:21,415 - evaluation.metrics - INFO - FID score: 370.3937
2025-05-27 16:30:21,416 - evaluation.metrics - INFO - Calculating IS for Phase-Subband surface with 1000-dim features...
2025-05-27 16:30:21,416 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:30:21,426 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:30:21,972 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:30:21,975 - evaluation.metrics - INFO - Inception Score calculation completed in 0.56 seconds
2025-05-27 16:30:21,975 - evaluation.metrics - INFO - Inception Score: 1.1099 ± 0.0181
2025-05-27 16:30:21,975 - evaluation.metrics - INFO - Phase-Subband surface results: FID = 370.3937, IS = 1.1099 ± 0.0181
2025-05-27 16:30:21,975 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface (channel 2)...
2025-05-27 16:30:21,977 - evaluation.metrics - INFO - Channel 2 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.2871, -0.0846]
2025-05-27 16:30:21,982 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface using channel replication method
2025-05-27 16:30:21,985 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:30:21,985 - evaluation.metrics - INFO - Calculating FID for Phase-Subintegration surface with 2048-dim features...
2025-05-27 16:30:21,985 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:30:21,987 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:30:21,987 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:30:22,080 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:30:22,686 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:30:23,810 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:31:29,715 - evaluation.metrics - INFO - FID calculation completed in 67.73 seconds
2025-05-27 16:31:29,715 - evaluation.metrics - INFO - FID score: 427.2202
2025-05-27 16:31:29,716 - evaluation.metrics - INFO - Calculating IS for Phase-Subintegration surface with 1000-dim features...
2025-05-27 16:31:29,716 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:31:29,723 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:31:30,240 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:31:30,243 - evaluation.metrics - INFO - Inception Score calculation completed in 0.53 seconds
2025-05-27 16:31:30,243 - evaluation.metrics - INFO - Inception Score: 1.0149 ± 0.0025
2025-05-27 16:31:30,243 - evaluation.metrics - INFO - Phase-Subintegration surface results: FID = 427.2202, IS = 1.0149 ± 0.0025
2025-05-27 16:31:30,244 - evaluation.metrics - INFO - Average results: FID = 378.5290, IS = 1.1123
2025-05-27 16:31:30,244 - evaluation.metrics - INFO - Channel-specific evaluation completed in 225.49 seconds
