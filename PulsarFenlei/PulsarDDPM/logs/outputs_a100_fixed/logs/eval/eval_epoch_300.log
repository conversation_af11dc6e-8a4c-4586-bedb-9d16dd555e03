2025-05-27 16:41:01,221 - evaluation.metrics - INFO - Generating 995 samples for evaluation with batch size 50
2025-05-27 16:41:01,221 - evaluation.metrics - INFO - Using external sampler: PulsarAdaptiveSampler
2025-05-27 16:41:01,221 - evaluation.metrics - INFO - Will generate 995 samples in 20 batches of size 50
2025-05-27 16:41:01,222 - evaluation.metrics - INFO - Using external sampler for batch 1/20
2025-05-27 16:41:02,095 - evaluation.metrics - INFO - Using external sampler for batch 2/20
2025-05-27 16:41:02,202 - evaluation.metrics - INFO - Using external sampler for batch 3/20
2025-05-27 16:41:02,472 - evaluation.metrics - INFO - Using external sampler for batch 4/20
2025-05-27 16:41:02,661 - evaluation.metrics - INFO - Using external sampler for batch 5/20
2025-05-27 16:41:02,860 - evaluation.metrics - INFO - Generated 250/995 samples
2025-05-27 16:41:02,860 - evaluation.metrics - INFO - Using external sampler for batch 6/20
2025-05-27 16:41:03,058 - evaluation.metrics - INFO - Using external sampler for batch 7/20
2025-05-27 16:41:03,258 - evaluation.metrics - INFO - Using external sampler for batch 8/20
2025-05-27 16:41:03,471 - evaluation.metrics - INFO - Using external sampler for batch 9/20
2025-05-27 16:41:03,666 - evaluation.metrics - INFO - Using external sampler for batch 10/20
2025-05-27 16:41:03,855 - evaluation.metrics - INFO - Generated 500/995 samples
2025-05-27 16:41:03,856 - evaluation.metrics - INFO - Using external sampler for batch 11/20
2025-05-27 16:41:04,079 - evaluation.metrics - INFO - Using external sampler for batch 12/20
2025-05-27 16:41:04,357 - evaluation.metrics - INFO - Using external sampler for batch 13/20
2025-05-27 16:41:04,579 - evaluation.metrics - INFO - Using external sampler for batch 14/20
2025-05-27 16:41:04,840 - evaluation.metrics - INFO - Using external sampler for batch 15/20
2025-05-27 16:41:05,054 - evaluation.metrics - INFO - Generated 750/995 samples
2025-05-27 16:41:05,054 - evaluation.metrics - INFO - Using external sampler for batch 16/20
2025-05-27 16:41:05,251 - evaluation.metrics - INFO - Using external sampler for batch 17/20
2025-05-27 16:41:05,457 - evaluation.metrics - INFO - Using external sampler for batch 18/20
2025-05-27 16:41:05,663 - evaluation.metrics - INFO - Using external sampler for batch 19/20
2025-05-27 16:41:05,849 - evaluation.metrics - INFO - Using external sampler for batch 20/20
2025-05-27 16:41:06,059 - evaluation.metrics - INFO - Generated 995/995 samples
2025-05-27 16:41:06,279 - evaluation.metrics - INFO - Successfully generated 995/995 samples
2025-05-27 16:41:06,280 - evaluation.metrics - INFO - Final samples already in valid range: [-0.794748, 0.940020]
2025-05-27 16:41:06,478 - evaluation.metrics - INFO - Generated 995 samples in 5.26 seconds
2025-05-27 16:41:06,479 - evaluation.metrics - INFO - Sample range: [-0.7947, 0.9400]
2025-05-27 16:41:08,209 - evaluation.metrics - INFO - Starting model evaluation...
2025-05-27 16:41:08,209 - evaluation.metrics - INFO - Preprocessing real images for evaluation...
2025-05-27 16:41:08,209 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:41:08,210 - evaluation.metrics - INFO - real images value range: [-1.0000, 1.0000]
2025-05-27 16:41:08,211 - evaluation.metrics - INFO - real images channel variances: [0.15015985071659088, 0.16890999674797058, 0.14086958765983582]
2025-05-27 16:41:08,211 - evaluation.metrics - INFO - Preprocessed real images: shape=torch.Size([995, 3, 32, 32]), range=[-1.0000, 1.0000]
2025-05-27 16:41:08,211 - evaluation.metrics - INFO - Preprocessing generated images for evaluation...
2025-05-27 16:41:08,211 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:41:08,214 - evaluation.metrics - INFO - generated images value range: [-0.7947, 0.9400]
2025-05-27 16:41:08,215 - evaluation.metrics - INFO - generated images channel variances: [0.00160461466293782, 0.07374633103609085, 0.00011527379683684558]
2025-05-27 16:41:08,215 - evaluation.metrics - INFO - Preprocessed generated images: shape=torch.Size([995, 3, 32, 32]), range=[-0.7947, 0.9400]
2025-05-27 16:41:08,215 - evaluation.metrics - INFO - Verifying local Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:08,215 - evaluation.metrics - INFO - Verifying Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:08,921 - evaluation.metrics - INFO - State dict contains 484 parameters
2025-05-27 16:41:08,921 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:08,922 - evaluation.metrics - INFO - Feature extraction mode: both
2025-05-27 16:41:08,922 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:41:13,992 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:16,667 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:41:17,145 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:17,148 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:41:17,148 - evaluation.metrics - INFO - Inception-v3 model loaded in 8.23 seconds
2025-05-27 16:41:17,148 - evaluation.metrics - INFO - Supporting both 2048-dim pool3 features (FID) and 1000-dim logits (IS)
2025-05-27 16:41:17,203 - evaluation.metrics - INFO - Inception model verification successful
2025-05-27 16:41:17,207 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 16:41:17,207 - evaluation.metrics - INFO - Loading Inception model for FID calculation (2048-dim features)...
2025-05-27 16:41:17,207 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:17,207 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 16:41:17,207 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:41:21,783 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:22,071 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:41:22,097 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:22,098 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:41:22,098 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.89 seconds
2025-05-27 16:41:22,098 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 16:41:22,099 - evaluation.metrics - INFO - Loading Inception model for IS calculation (1000-dim features)...
2025-05-27 16:41:22,099 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:22,099 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 16:41:22,099 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:41:28,006 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:28,336 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:41:28,392 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:41:28,393 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:41:28,393 - evaluation.metrics - INFO - Inception-v3 model loaded in 6.29 seconds
2025-05-27 16:41:28,393 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 16:41:28,395 - evaluation.metrics - INFO - Calculating FID with 2048-dim pool3 features...
2025-05-27 16:41:28,395 - evaluation.metrics - INFO - Using precomputed FID baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 16:41:28,395 - evaluation.metrics - INFO - Calculating FID using precomputed baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 16:41:28,403 - evaluation.metrics - INFO - Loaded baseline: 199 samples, 2048D features
2025-05-27 16:41:28,405 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:41:30,652 - evaluation.metrics - INFO - Computing FID score with baseline...
2025-05-27 16:42:34,727 - evaluation.metrics - INFO - FID calculation completed in 66.33 seconds
2025-05-27 16:42:34,728 - evaluation.metrics - INFO - FID score: 354.6842
2025-05-27 16:42:34,730 - evaluation.metrics - INFO - Calculating IS with 1000-dim classification features...
2025-05-27 16:42:34,730 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:42:34,732 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:42:36,131 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:42:36,134 - evaluation.metrics - INFO - Inception Score calculation completed in 1.40 seconds
2025-05-27 16:42:36,134 - evaluation.metrics - INFO - Inception Score: 1.0613 ± 0.0082
2025-05-27 16:42:36,135 - evaluation.metrics - INFO - Evaluation completed in 87.93 seconds
2025-05-27 16:42:36,135 - evaluation.metrics - INFO - Results: FID = 354.6842, IS = 1.0613 ± 0.0082
2025-05-27 16:42:36,140 - evaluation.metrics - INFO - Starting channel-specific evaluation with simplified approach...
2025-05-27 16:42:36,140 - evaluation.metrics - INFO - Will evaluate 3 channels
2025-05-27 16:42:36,140 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 16:42:36,140 - evaluation.metrics - INFO - Loading Inception models with differential configuration...
2025-05-27 16:42:36,140 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:42:36,140 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 16:42:36,140 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:42:41,303 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:42:41,649 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:42:41,675 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:42:41,676 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:42:41,676 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.54 seconds
2025-05-27 16:42:41,676 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 16:42:41,677 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:42:41,677 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 16:42:41,677 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:42:47,207 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:42:47,546 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:42:47,572 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:42:47,572 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:42:47,572 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.90 seconds
2025-05-27 16:42:47,572 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 16:42:47,576 - evaluation.metrics - INFO - Evaluating Period-DM surface (channel 0)...
2025-05-27 16:42:47,579 - evaluation.metrics - INFO - Channel 0 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.5375, -0.1994]
2025-05-27 16:42:47,581 - evaluation.metrics - INFO - Evaluating Period-DM surface using channel replication method
2025-05-27 16:42:47,581 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:42:47,581 - evaluation.metrics - INFO - Calculating FID for Period-DM surface with 2048-dim features...
2025-05-27 16:42:47,581 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:42:47,582 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:42:47,582 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:42:47,584 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:42:48,277 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:42:49,450 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:43:58,415 - evaluation.metrics - INFO - FID calculation completed in 70.83 seconds
2025-05-27 16:43:58,415 - evaluation.metrics - INFO - FID score: 387.4654
2025-05-27 16:43:58,416 - evaluation.metrics - INFO - Calculating IS for Period-DM surface with 1000-dim features...
2025-05-27 16:43:58,416 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:43:58,423 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:43:58,964 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:43:58,968 - evaluation.metrics - INFO - Inception Score calculation completed in 0.55 seconds
2025-05-27 16:43:58,968 - evaluation.metrics - INFO - Inception Score: 1.0245 ± 0.0068
2025-05-27 16:43:58,968 - evaluation.metrics - INFO - Period-DM surface results: FID = 387.4654, IS = 1.0245 ± 0.0068
2025-05-27 16:43:58,968 - evaluation.metrics - INFO - Evaluating Phase-Subband surface (channel 1)...
2025-05-27 16:43:58,970 - evaluation.metrics - INFO - Channel 1 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.7947, 0.9400]
2025-05-27 16:43:58,979 - evaluation.metrics - INFO - Evaluating Phase-Subband surface using channel replication method
2025-05-27 16:43:58,979 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:43:58,979 - evaluation.metrics - INFO - Calculating FID for Phase-Subband surface with 2048-dim features...
2025-05-27 16:43:58,979 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:43:58,980 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:43:58,980 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:43:58,983 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:43:59,591 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:44:00,727 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:45:10,717 - evaluation.metrics - INFO - FID calculation completed in 71.74 seconds
2025-05-27 16:45:10,717 - evaluation.metrics - INFO - FID score: 380.1160
2025-05-27 16:45:10,718 - evaluation.metrics - INFO - Calculating IS for Phase-Subband surface with 1000-dim features...
2025-05-27 16:45:10,718 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:45:10,725 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:45:11,300 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:45:11,304 - evaluation.metrics - INFO - Inception Score calculation completed in 0.59 seconds
2025-05-27 16:45:11,304 - evaluation.metrics - INFO - Inception Score: 1.0940 ± 0.0108
2025-05-27 16:45:11,304 - evaluation.metrics - INFO - Phase-Subband surface results: FID = 380.1160, IS = 1.0940 ± 0.0108
2025-05-27 16:45:11,304 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface (channel 2)...
2025-05-27 16:45:11,306 - evaluation.metrics - INFO - Channel 2 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.2308, -0.0837]
2025-05-27 16:45:11,311 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface using channel replication method
2025-05-27 16:45:11,313 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:45:11,313 - evaluation.metrics - INFO - Calculating FID for Phase-Subintegration surface with 2048-dim features...
2025-05-27 16:45:11,313 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:45:11,315 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:45:11,315 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:45:11,379 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:45:11,987 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:45:13,113 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:46:25,015 - evaluation.metrics - INFO - FID calculation completed in 73.70 seconds
2025-05-27 16:46:25,015 - evaluation.metrics - INFO - FID score: 441.6567
2025-05-27 16:46:25,016 - evaluation.metrics - INFO - Calculating IS for Phase-Subintegration surface with 1000-dim features...
2025-05-27 16:46:25,016 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:46:25,023 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:46:25,567 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:46:25,571 - evaluation.metrics - INFO - Inception Score calculation completed in 0.55 seconds
2025-05-27 16:46:25,571 - evaluation.metrics - INFO - Inception Score: 1.0075 ± 0.0008
2025-05-27 16:46:25,571 - evaluation.metrics - INFO - Phase-Subintegration surface results: FID = 441.6567, IS = 1.0075 ± 0.0008
2025-05-27 16:46:25,571 - evaluation.metrics - INFO - Average results: FID = 403.0794, IS = 1.0420
2025-05-27 16:46:25,571 - evaluation.metrics - INFO - Channel-specific evaluation completed in 229.43 seconds
