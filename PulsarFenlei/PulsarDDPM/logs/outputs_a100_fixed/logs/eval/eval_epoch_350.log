2025-05-27 16:53:49,319 - evaluation.metrics - INFO - Generating 995 samples for evaluation with batch size 50
2025-05-27 16:53:49,319 - evaluation.metrics - INFO - Using external sampler: PulsarAdaptiveSampler
2025-05-27 16:53:49,319 - evaluation.metrics - INFO - Will generate 995 samples in 20 batches of size 50
2025-05-27 16:53:49,320 - evaluation.metrics - INFO - Using external sampler for batch 1/20
2025-05-27 16:53:51,179 - evaluation.metrics - INFO - Using external sampler for batch 2/20
2025-05-27 16:53:51,284 - evaluation.metrics - INFO - Using external sampler for batch 3/20
2025-05-27 16:53:51,479 - evaluation.metrics - INFO - Using external sampler for batch 4/20
2025-05-27 16:53:51,761 - evaluation.metrics - INFO - Using external sampler for batch 5/20
2025-05-27 16:53:51,967 - evaluation.metrics - INFO - Generated 250/995 samples
2025-05-27 16:53:51,967 - evaluation.metrics - INFO - Using external sampler for batch 6/20
2025-05-27 16:53:52,154 - evaluation.metrics - INFO - Using external sampler for batch 7/20
2025-05-27 16:53:52,354 - evaluation.metrics - INFO - Using external sampler for batch 8/20
2025-05-27 16:53:52,566 - evaluation.metrics - INFO - Using external sampler for batch 9/20
2025-05-27 16:53:52,772 - evaluation.metrics - INFO - Using external sampler for batch 10/20
2025-05-27 16:53:52,961 - evaluation.metrics - INFO - Generated 500/995 samples
2025-05-27 16:53:52,962 - evaluation.metrics - INFO - Using external sampler for batch 11/20
2025-05-27 16:53:53,166 - evaluation.metrics - INFO - Using external sampler for batch 12/20
2025-05-27 16:53:53,363 - evaluation.metrics - INFO - Using external sampler for batch 13/20
2025-05-27 16:53:53,582 - evaluation.metrics - INFO - Using external sampler for batch 14/20
2025-05-27 16:53:53,785 - evaluation.metrics - INFO - Using external sampler for batch 15/20
2025-05-27 16:53:53,996 - evaluation.metrics - INFO - Generated 750/995 samples
2025-05-27 16:53:53,996 - evaluation.metrics - INFO - Using external sampler for batch 16/20
2025-05-27 16:53:54,227 - evaluation.metrics - INFO - Using external sampler for batch 17/20
2025-05-27 16:53:54,458 - evaluation.metrics - INFO - Using external sampler for batch 18/20
2025-05-27 16:53:54,658 - evaluation.metrics - INFO - Using external sampler for batch 19/20
2025-05-27 16:53:54,858 - evaluation.metrics - INFO - Using external sampler for batch 20/20
2025-05-27 16:53:55,062 - evaluation.metrics - INFO - Generated 995/995 samples
2025-05-27 16:53:55,186 - evaluation.metrics - INFO - Successfully generated 995/995 samples
2025-05-27 16:53:55,187 - evaluation.metrics - INFO - Final samples already in valid range: [-0.930508, 0.844429]
2025-05-27 16:53:55,380 - evaluation.metrics - INFO - Generated 995 samples in 6.06 seconds
2025-05-27 16:53:55,381 - evaluation.metrics - INFO - Sample range: [-0.9305, 0.8444]
2025-05-27 16:53:57,198 - evaluation.metrics - INFO - Starting model evaluation...
2025-05-27 16:53:57,199 - evaluation.metrics - INFO - Preprocessing real images for evaluation...
2025-05-27 16:53:57,199 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:53:57,200 - evaluation.metrics - INFO - real images value range: [-1.0000, 1.0000]
2025-05-27 16:53:57,200 - evaluation.metrics - INFO - real images channel variances: [0.15115566551685333, 0.16892065107822418, 0.14041979610919952]
2025-05-27 16:53:57,201 - evaluation.metrics - INFO - Preprocessed real images: shape=torch.Size([995, 3, 32, 32]), range=[-1.0000, 1.0000]
2025-05-27 16:53:57,201 - evaluation.metrics - INFO - Preprocessing generated images for evaluation...
2025-05-27 16:53:57,201 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:53:57,278 - evaluation.metrics - INFO - generated images value range: [-0.9305, 0.8444]
2025-05-27 16:53:57,279 - evaluation.metrics - INFO - generated images channel variances: [0.00410107709467411, 0.07545480877161026, 0.000164134064107202]
2025-05-27 16:53:57,279 - evaluation.metrics - INFO - Preprocessed generated images: shape=torch.Size([995, 3, 32, 32]), range=[-0.9305, 0.8444]
2025-05-27 16:53:57,280 - evaluation.metrics - INFO - Verifying local Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:53:57,280 - evaluation.metrics - INFO - Verifying Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:53:57,757 - evaluation.metrics - INFO - State dict contains 484 parameters
2025-05-27 16:53:57,757 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:53:57,758 - evaluation.metrics - INFO - Feature extraction mode: both
2025-05-27 16:53:57,758 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:54:02,102 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:54:03,003 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:54:05,948 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:54:05,950 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:54:05,951 - evaluation.metrics - INFO - Inception-v3 model loaded in 8.19 seconds
2025-05-27 16:54:05,951 - evaluation.metrics - INFO - Supporting both 2048-dim pool3 features (FID) and 1000-dim logits (IS)
2025-05-27 16:54:06,012 - evaluation.metrics - INFO - Inception model verification successful
2025-05-27 16:54:06,016 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 16:54:06,016 - evaluation.metrics - INFO - Loading Inception model for FID calculation (2048-dim features)...
2025-05-27 16:54:06,016 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:54:06,016 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 16:54:06,016 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:54:10,504 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:54:10,836 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:54:10,863 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:54:10,864 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:54:10,864 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.85 seconds
2025-05-27 16:54:10,864 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 16:54:10,865 - evaluation.metrics - INFO - Loading Inception model for IS calculation (1000-dim features)...
2025-05-27 16:54:10,865 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:54:10,865 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 16:54:10,865 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:54:16,599 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:54:16,925 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:54:16,961 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:54:16,962 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:54:16,962 - evaluation.metrics - INFO - Inception-v3 model loaded in 6.10 seconds
2025-05-27 16:54:16,962 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 16:54:16,963 - evaluation.metrics - INFO - Calculating FID with 2048-dim pool3 features...
2025-05-27 16:54:16,963 - evaluation.metrics - INFO - Using precomputed FID baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 16:54:16,963 - evaluation.metrics - INFO - Calculating FID using precomputed baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 16:54:16,970 - evaluation.metrics - INFO - Loaded baseline: 199 samples, 2048D features
2025-05-27 16:54:16,971 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:54:17,958 - evaluation.metrics - INFO - Computing FID score with baseline...
2025-05-27 16:55:22,825 - evaluation.metrics - INFO - FID calculation completed in 65.86 seconds
2025-05-27 16:55:22,825 - evaluation.metrics - INFO - FID score: 364.9170
2025-05-27 16:55:22,829 - evaluation.metrics - INFO - Calculating IS with 1000-dim classification features...
2025-05-27 16:55:22,829 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:55:22,832 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:55:23,386 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:55:23,389 - evaluation.metrics - INFO - Inception Score calculation completed in 0.56 seconds
2025-05-27 16:55:23,389 - evaluation.metrics - INFO - Inception Score: 1.0287 ± 0.0067
2025-05-27 16:55:23,389 - evaluation.metrics - INFO - Evaluation completed in 86.19 seconds
2025-05-27 16:55:23,389 - evaluation.metrics - INFO - Results: FID = 364.9170, IS = 1.0287 ± 0.0067
2025-05-27 16:55:23,396 - evaluation.metrics - INFO - Starting channel-specific evaluation with simplified approach...
2025-05-27 16:55:23,396 - evaluation.metrics - INFO - Will evaluate 3 channels
2025-05-27 16:55:23,396 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 16:55:23,396 - evaluation.metrics - INFO - Loading Inception models with differential configuration...
2025-05-27 16:55:23,396 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:55:23,396 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 16:55:23,396 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:55:29,612 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:55:29,960 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:55:29,987 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:55:29,987 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:55:29,987 - evaluation.metrics - INFO - Inception-v3 model loaded in 6.59 seconds
2025-05-27 16:55:29,987 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 16:55:29,988 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:55:29,989 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 16:55:29,989 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 16:55:36,101 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:55:36,441 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 16:55:36,467 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 16:55:36,467 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 16:55:36,468 - evaluation.metrics - INFO - Inception-v3 model loaded in 6.48 seconds
2025-05-27 16:55:36,468 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 16:55:36,471 - evaluation.metrics - INFO - Evaluating Period-DM surface (channel 0)...
2025-05-27 16:55:36,474 - evaluation.metrics - INFO - Channel 0 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.5801, -0.2154]
2025-05-27 16:55:36,478 - evaluation.metrics - INFO - Evaluating Period-DM surface using channel replication method
2025-05-27 16:55:36,478 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:55:36,481 - evaluation.metrics - INFO - Calculating FID for Period-DM surface with 2048-dim features...
2025-05-27 16:55:36,481 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:55:36,482 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:55:36,482 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:55:36,483 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:55:37,095 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:55:38,250 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:56:45,005 - evaluation.metrics - INFO - FID calculation completed in 68.52 seconds
2025-05-27 16:56:45,006 - evaluation.metrics - INFO - FID score: 399.3133
2025-05-27 16:56:45,007 - evaluation.metrics - INFO - Calculating IS for Period-DM surface with 1000-dim features...
2025-05-27 16:56:45,007 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:56:45,017 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:56:45,547 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:56:45,550 - evaluation.metrics - INFO - Inception Score calculation completed in 0.54 seconds
2025-05-27 16:56:45,550 - evaluation.metrics - INFO - Inception Score: 1.0342 ± 0.0142
2025-05-27 16:56:45,550 - evaluation.metrics - INFO - Period-DM surface results: FID = 399.3133, IS = 1.0342 ± 0.0142
2025-05-27 16:56:45,550 - evaluation.metrics - INFO - Evaluating Phase-Subband surface (channel 1)...
2025-05-27 16:56:45,553 - evaluation.metrics - INFO - Channel 1 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.9305, 0.8444]
2025-05-27 16:56:45,558 - evaluation.metrics - INFO - Evaluating Phase-Subband surface using channel replication method
2025-05-27 16:56:45,558 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:56:45,558 - evaluation.metrics - INFO - Calculating FID for Phase-Subband surface with 2048-dim features...
2025-05-27 16:56:45,558 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:56:45,560 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:56:45,560 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:56:45,562 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:56:46,177 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:56:47,353 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:57:45,184 - evaluation.metrics - INFO - FID calculation completed in 59.63 seconds
2025-05-27 16:57:45,185 - evaluation.metrics - INFO - FID score: 393.3803
2025-05-27 16:57:45,186 - evaluation.metrics - INFO - Calculating IS for Phase-Subband surface with 1000-dim features...
2025-05-27 16:57:45,187 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:57:45,198 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:57:45,715 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:57:45,718 - evaluation.metrics - INFO - Inception Score calculation completed in 0.53 seconds
2025-05-27 16:57:45,718 - evaluation.metrics - INFO - Inception Score: 1.0607 ± 0.0158
2025-05-27 16:57:45,718 - evaluation.metrics - INFO - Phase-Subband surface results: FID = 393.3803, IS = 1.0607 ± 0.0158
2025-05-27 16:57:45,718 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface (channel 2)...
2025-05-27 16:57:45,720 - evaluation.metrics - INFO - Channel 2 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.2443, -0.0884]
2025-05-27 16:57:45,723 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface using channel replication method
2025-05-27 16:57:45,726 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:57:45,726 - evaluation.metrics - INFO - Calculating FID for Phase-Subintegration surface with 2048-dim features...
2025-05-27 16:57:45,726 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:57:45,728 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:57:45,728 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:57:45,730 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:57:46,374 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:57:47,525 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:58:58,620 - evaluation.metrics - INFO - FID calculation completed in 72.89 seconds
2025-05-27 16:58:58,621 - evaluation.metrics - INFO - FID score: 446.2858
2025-05-27 16:58:58,621 - evaluation.metrics - INFO - Calculating IS for Phase-Subintegration surface with 1000-dim features...
2025-05-27 16:58:58,621 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:58:58,625 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:58:59,170 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:58:59,174 - evaluation.metrics - INFO - Inception Score calculation completed in 0.55 seconds
2025-05-27 16:58:59,174 - evaluation.metrics - INFO - Inception Score: 1.0109 ± 0.0068
2025-05-27 16:58:59,174 - evaluation.metrics - INFO - Phase-Subintegration surface results: FID = 446.2858, IS = 1.0109 ± 0.0068
2025-05-27 16:58:59,174 - evaluation.metrics - INFO - Average results: FID = 412.9932, IS = 1.0352
2025-05-27 16:58:59,174 - evaluation.metrics - INFO - Channel-specific evaluation completed in 215.78 seconds
