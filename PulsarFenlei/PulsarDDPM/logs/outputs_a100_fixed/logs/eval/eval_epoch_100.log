2025-05-27 15:40:43,909 - evaluation.metrics - INFO - Generating 995 samples for evaluation with batch size 50
2025-05-27 15:40:43,909 - evaluation.metrics - INFO - Using external sampler: PulsarAdaptiveSampler
2025-05-27 15:40:43,910 - evaluation.metrics - INFO - Will generate 995 samples in 20 batches of size 50
2025-05-27 15:40:43,910 - evaluation.metrics - INFO - Using external sampler for batch 1/20
2025-05-27 15:40:46,849 - evaluation.metrics - INFO - Using external sampler for batch 2/20
2025-05-27 15:40:46,952 - evaluation.metrics - INFO - Using external sampler for batch 3/20
2025-05-27 15:40:47,158 - evaluation.metrics - INFO - Using external sampler for batch 4/20
2025-05-27 15:40:47,358 - evaluation.metrics - INFO - Using external sampler for batch 5/20
2025-05-27 15:40:47,558 - evaluation.metrics - INFO - Generated 250/995 samples
2025-05-27 15:40:47,558 - evaluation.metrics - INFO - Using external sampler for batch 6/20
2025-05-27 15:40:47,758 - evaluation.metrics - INFO - Using external sampler for batch 7/20
2025-05-27 15:40:47,966 - evaluation.metrics - INFO - Using external sampler for batch 8/20
2025-05-27 15:40:48,155 - evaluation.metrics - INFO - Using external sampler for batch 9/20
2025-05-27 15:40:48,380 - evaluation.metrics - INFO - Using external sampler for batch 10/20
2025-05-27 15:40:48,650 - evaluation.metrics - INFO - Generated 500/995 samples
2025-05-27 15:40:48,651 - evaluation.metrics - INFO - Using external sampler for batch 11/20
2025-05-27 15:40:48,856 - evaluation.metrics - INFO - Using external sampler for batch 12/20
2025-05-27 15:40:49,054 - evaluation.metrics - INFO - Using external sampler for batch 13/20
2025-05-27 15:40:49,251 - evaluation.metrics - INFO - Using external sampler for batch 14/20
2025-05-27 15:40:49,462 - evaluation.metrics - INFO - Using external sampler for batch 15/20
2025-05-27 15:40:49,665 - evaluation.metrics - INFO - Generated 750/995 samples
2025-05-27 15:40:49,665 - evaluation.metrics - INFO - Using external sampler for batch 16/20
2025-05-27 15:40:49,862 - evaluation.metrics - INFO - Using external sampler for batch 17/20
2025-05-27 15:40:50,079 - evaluation.metrics - INFO - Using external sampler for batch 18/20
2025-05-27 15:40:50,331 - evaluation.metrics - INFO - Using external sampler for batch 19/20
2025-05-27 15:40:50,552 - evaluation.metrics - INFO - Using external sampler for batch 20/20
2025-05-27 15:40:50,762 - evaluation.metrics - INFO - Generated 995/995 samples
2025-05-27 15:40:50,780 - evaluation.metrics - INFO - Successfully generated 995/995 samples
2025-05-27 15:40:50,780 - evaluation.metrics - INFO - Final samples already in valid range: [-0.837376, 0.700062]
2025-05-27 15:40:50,785 - evaluation.metrics - INFO - Generated 995 samples in 6.88 seconds
2025-05-27 15:40:50,785 - evaluation.metrics - INFO - Sample range: [-0.8374, 0.7001]
2025-05-27 15:40:52,584 - evaluation.metrics - INFO - Starting model evaluation...
2025-05-27 15:40:52,584 - evaluation.metrics - INFO - Preprocessing real images for evaluation...
2025-05-27 15:40:52,584 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:40:52,585 - evaluation.metrics - INFO - real images value range: [-1.0000, 1.0000]
2025-05-27 15:40:52,586 - evaluation.metrics - INFO - real images channel variances: [0.15050531923770905, 0.16914154589176178, 0.13924521207809448]
2025-05-27 15:40:52,586 - evaluation.metrics - INFO - Preprocessed real images: shape=torch.Size([995, 3, 32, 32]), range=[-1.0000, 1.0000]
2025-05-27 15:40:52,586 - evaluation.metrics - INFO - Preprocessing generated images for evaluation...
2025-05-27 15:40:52,587 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:40:52,680 - evaluation.metrics - INFO - generated images value range: [-0.8374, 0.7001]
2025-05-27 15:40:52,681 - evaluation.metrics - INFO - generated images channel variances: [0.001857337192632258, 0.031707730144262314, 0.0002748061087913811]
2025-05-27 15:40:52,681 - evaluation.metrics - INFO - Preprocessed generated images: shape=torch.Size([995, 3, 32, 32]), range=[-0.8374, 0.7001]
2025-05-27 15:40:52,682 - evaluation.metrics - INFO - Verifying local Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:40:52,682 - evaluation.metrics - INFO - Verifying Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:40:53,321 - evaluation.metrics - INFO - State dict contains 484 parameters
2025-05-27 15:40:53,322 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:40:53,322 - evaluation.metrics - INFO - Feature extraction mode: both
2025-05-27 15:40:53,322 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:40:59,306 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:41:01,491 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:41:01,909 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:41:01,912 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:41:01,912 - evaluation.metrics - INFO - Inception-v3 model loaded in 8.59 seconds
2025-05-27 15:41:01,912 - evaluation.metrics - INFO - Supporting both 2048-dim pool3 features (FID) and 1000-dim logits (IS)
2025-05-27 15:41:01,970 - evaluation.metrics - INFO - Inception model verification successful
2025-05-27 15:41:01,976 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 15:41:01,976 - evaluation.metrics - INFO - Loading Inception model for FID calculation (2048-dim features)...
2025-05-27 15:41:01,976 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:41:01,976 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 15:41:01,976 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:41:06,999 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:41:07,347 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:41:07,374 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:41:07,374 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:41:07,374 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.40 seconds
2025-05-27 15:41:07,374 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 15:41:07,375 - evaluation.metrics - INFO - Loading Inception model for IS calculation (1000-dim features)...
2025-05-27 15:41:07,375 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:41:07,375 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 15:41:07,375 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:41:12,400 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:41:12,736 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:41:12,798 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:41:12,800 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:41:12,800 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.43 seconds
2025-05-27 15:41:12,801 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 15:41:12,802 - evaluation.metrics - INFO - Calculating FID with 2048-dim pool3 features...
2025-05-27 15:41:12,802 - evaluation.metrics - INFO - Using precomputed FID baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 15:41:12,803 - evaluation.metrics - INFO - Calculating FID using precomputed baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 15:41:12,817 - evaluation.metrics - INFO - Loaded baseline: 199 samples, 2048D features
2025-05-27 15:41:12,820 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:41:13,715 - evaluation.metrics - INFO - Computing FID score with baseline...
2025-05-27 15:42:15,614 - evaluation.metrics - INFO - FID calculation completed in 62.81 seconds
2025-05-27 15:42:15,614 - evaluation.metrics - INFO - FID score: 282.0493
2025-05-27 15:42:15,617 - evaluation.metrics - INFO - Calculating IS with 1000-dim classification features...
2025-05-27 15:42:15,618 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:42:15,620 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:42:16,543 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:42:16,546 - evaluation.metrics - INFO - Inception Score calculation completed in 0.93 seconds
2025-05-27 15:42:16,546 - evaluation.metrics - INFO - Inception Score: 1.8701 ± 0.1049
2025-05-27 15:42:16,546 - evaluation.metrics - INFO - Evaluation completed in 83.96 seconds
2025-05-27 15:42:16,546 - evaluation.metrics - INFO - Results: FID = 282.0493, IS = 1.8701 ± 0.1049
2025-05-27 15:42:16,556 - evaluation.metrics - INFO - Starting channel-specific evaluation with simplified approach...
2025-05-27 15:42:16,556 - evaluation.metrics - INFO - Will evaluate 3 channels
2025-05-27 15:42:16,556 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 15:42:16,556 - evaluation.metrics - INFO - Loading Inception models with differential configuration...
2025-05-27 15:42:16,556 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:42:16,556 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 15:42:16,556 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:42:21,501 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:42:21,855 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:42:21,881 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:42:21,881 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:42:21,881 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.33 seconds
2025-05-27 15:42:21,881 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 15:42:21,882 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:42:21,882 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 15:42:21,882 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:42:26,400 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:42:26,736 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:42:26,761 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:42:26,762 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:42:26,762 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.88 seconds
2025-05-27 15:42:26,762 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 15:42:26,766 - evaluation.metrics - INFO - Evaluating Period-DM surface (channel 0)...
2025-05-27 15:42:26,768 - evaluation.metrics - INFO - Channel 0 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.7601, -0.1109]
2025-05-27 15:42:26,770 - evaluation.metrics - INFO - Evaluating Period-DM surface using channel replication method
2025-05-27 15:42:26,770 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 15:42:26,770 - evaluation.metrics - INFO - Calculating FID for Period-DM surface with 2048-dim features...
2025-05-27 15:42:26,770 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 15:42:26,771 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:42:26,771 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:42:26,773 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 15:42:27,404 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:42:28,557 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 15:44:07,702 - evaluation.metrics - INFO - FID calculation completed in 100.93 seconds
2025-05-27 15:44:07,703 - evaluation.metrics - INFO - FID score: 359.2363
2025-05-27 15:44:07,704 - evaluation.metrics - INFO - Calculating IS for Period-DM surface with 1000-dim features...
2025-05-27 15:44:07,704 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:44:07,714 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:44:08,252 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:44:08,255 - evaluation.metrics - INFO - Inception Score calculation completed in 0.55 seconds
2025-05-27 15:44:08,255 - evaluation.metrics - INFO - Inception Score: 1.9826 ± 0.1201
2025-05-27 15:44:08,256 - evaluation.metrics - INFO - Period-DM surface results: FID = 359.2363, IS = 1.9826 ± 0.1201
2025-05-27 15:44:08,256 - evaluation.metrics - INFO - Evaluating Phase-Subband surface (channel 1)...
2025-05-27 15:44:08,258 - evaluation.metrics - INFO - Channel 1 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.8374, 0.7001]
2025-05-27 15:44:08,281 - evaluation.metrics - INFO - Evaluating Phase-Subband surface using channel replication method
2025-05-27 15:44:08,281 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 15:44:08,281 - evaluation.metrics - INFO - Calculating FID for Phase-Subband surface with 2048-dim features...
2025-05-27 15:44:08,281 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 15:44:08,284 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:44:08,284 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:44:08,286 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 15:44:08,990 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:44:10,149 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 15:45:32,210 - evaluation.metrics - INFO - FID calculation completed in 83.93 seconds
2025-05-27 15:45:32,210 - evaluation.metrics - INFO - FID score: 303.1403
2025-05-27 15:45:32,211 - evaluation.metrics - INFO - Calculating IS for Phase-Subband surface with 1000-dim features...
2025-05-27 15:45:32,211 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:45:32,220 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:45:32,753 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:45:32,757 - evaluation.metrics - INFO - Inception Score calculation completed in 0.55 seconds
2025-05-27 15:45:32,757 - evaluation.metrics - INFO - Inception Score: 2.0328 ± 0.1278
2025-05-27 15:45:32,757 - evaluation.metrics - INFO - Phase-Subband surface results: FID = 303.1403, IS = 2.0328 ± 0.1278
2025-05-27 15:45:32,757 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface (channel 2)...
2025-05-27 15:45:32,759 - evaluation.metrics - INFO - Channel 2 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.3355, -0.0894]
2025-05-27 15:45:32,764 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface using channel replication method
2025-05-27 15:45:32,766 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 15:45:32,766 - evaluation.metrics - INFO - Calculating FID for Phase-Subintegration surface with 2048-dim features...
2025-05-27 15:45:32,766 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 15:45:32,768 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:45:32,768 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:45:32,780 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 15:45:33,386 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:45:34,482 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 15:46:55,404 - evaluation.metrics - INFO - FID calculation completed in 82.64 seconds
2025-05-27 15:46:55,404 - evaluation.metrics - INFO - FID score: 300.8172
2025-05-27 15:46:55,406 - evaluation.metrics - INFO - Calculating IS for Phase-Subintegration surface with 1000-dim features...
2025-05-27 15:46:55,406 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:46:55,415 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:46:55,961 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:46:55,964 - evaluation.metrics - INFO - Inception Score calculation completed in 0.56 seconds
2025-05-27 15:46:55,964 - evaluation.metrics - INFO - Inception Score: 1.7191 ± 0.0486
2025-05-27 15:46:55,964 - evaluation.metrics - INFO - Phase-Subintegration surface results: FID = 300.8172, IS = 1.7191 ± 0.0486
2025-05-27 15:46:55,965 - evaluation.metrics - INFO - Average results: FID = 321.0646, IS = 1.9115
2025-05-27 15:46:55,965 - evaluation.metrics - INFO - Channel-specific evaluation completed in 279.41 seconds
