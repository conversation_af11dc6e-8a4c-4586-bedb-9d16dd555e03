2025-05-27 15:56:34,606 - evaluation.metrics - INFO - Generating 995 samples for evaluation with batch size 50
2025-05-27 15:56:34,606 - evaluation.metrics - INFO - Using external sampler: PulsarAdaptiveSampler
2025-05-27 15:56:34,606 - evaluation.metrics - INFO - Will generate 995 samples in 20 batches of size 50
2025-05-27 15:56:34,607 - evaluation.metrics - INFO - Using external sampler for batch 1/20
2025-05-27 15:56:36,597 - evaluation.metrics - INFO - Using external sampler for batch 2/20
2025-05-27 15:56:36,701 - evaluation.metrics - INFO - Using external sampler for batch 3/20
2025-05-27 15:56:36,930 - evaluation.metrics - INFO - Using external sampler for batch 4/20
2025-05-27 15:56:37,153 - evaluation.metrics - INFO - Using external sampler for batch 5/20
2025-05-27 15:56:37,363 - evaluation.metrics - INFO - Generated 250/995 samples
2025-05-27 15:56:37,363 - evaluation.metrics - INFO - Using external sampler for batch 6/20
2025-05-27 15:56:37,563 - evaluation.metrics - INFO - Using external sampler for batch 7/20
2025-05-27 15:56:37,770 - evaluation.metrics - INFO - Using external sampler for batch 8/20
2025-05-27 15:56:37,965 - evaluation.metrics - INFO - Using external sampler for batch 9/20
2025-05-27 15:56:38,162 - evaluation.metrics - INFO - Using external sampler for batch 10/20
2025-05-27 15:56:38,361 - evaluation.metrics - INFO - Generated 500/995 samples
2025-05-27 15:56:38,361 - evaluation.metrics - INFO - Using external sampler for batch 11/20
2025-05-27 15:56:38,559 - evaluation.metrics - INFO - Using external sampler for batch 12/20
2025-05-27 15:56:38,760 - evaluation.metrics - INFO - Using external sampler for batch 13/20
2025-05-27 15:56:38,960 - evaluation.metrics - INFO - Using external sampler for batch 14/20
2025-05-27 15:56:39,159 - evaluation.metrics - INFO - Using external sampler for batch 15/20
2025-05-27 15:56:39,363 - evaluation.metrics - INFO - Generated 750/995 samples
2025-05-27 15:56:39,363 - evaluation.metrics - INFO - Using external sampler for batch 16/20
2025-05-27 15:56:39,563 - evaluation.metrics - INFO - Using external sampler for batch 17/20
2025-05-27 15:56:39,769 - evaluation.metrics - INFO - Using external sampler for batch 18/20
2025-05-27 15:56:39,958 - evaluation.metrics - INFO - Using external sampler for batch 19/20
2025-05-27 15:56:40,174 - evaluation.metrics - INFO - Using external sampler for batch 20/20
2025-05-27 15:56:40,396 - evaluation.metrics - INFO - Generated 995/995 samples
2025-05-27 15:56:40,478 - evaluation.metrics - INFO - Successfully generated 995/995 samples
2025-05-27 15:56:40,479 - evaluation.metrics - INFO - Final samples already in valid range: [-0.855351, 0.783573]
2025-05-27 15:56:40,681 - evaluation.metrics - INFO - Generated 995 samples in 6.08 seconds
2025-05-27 15:56:40,682 - evaluation.metrics - INFO - Sample range: [-0.8554, 0.7836]
2025-05-27 15:56:42,597 - evaluation.metrics - INFO - Starting model evaluation...
2025-05-27 15:56:42,597 - evaluation.metrics - INFO - Preprocessing real images for evaluation...
2025-05-27 15:56:42,597 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:56:42,598 - evaluation.metrics - INFO - real images value range: [-1.0000, 1.0000]
2025-05-27 15:56:42,599 - evaluation.metrics - INFO - real images channel variances: [0.14970065653324127, 0.16889339685440063, 0.13949567079544067]
2025-05-27 15:56:42,599 - evaluation.metrics - INFO - Preprocessed real images: shape=torch.Size([995, 3, 32, 32]), range=[-1.0000, 1.0000]
2025-05-27 15:56:42,600 - evaluation.metrics - INFO - Preprocessing generated images for evaluation...
2025-05-27 15:56:42,600 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:56:42,681 - evaluation.metrics - INFO - generated images value range: [-0.8554, 0.7836]
2025-05-27 15:56:42,682 - evaluation.metrics - INFO - generated images channel variances: [0.001751520554535091, 0.04693817347288132, 8.809681457933038e-05]
2025-05-27 15:56:42,682 - evaluation.metrics - WARNING - generated images has very low variance in some channels, this may affect evaluation
2025-05-27 15:56:42,682 - evaluation.metrics - INFO - Adding small noise to generated images channel 2 to increase variance
2025-05-27 15:56:42,683 - evaluation.metrics - INFO - Preprocessed generated images: shape=torch.Size([995, 3, 32, 32]), range=[-0.8554, 0.7836]
2025-05-27 15:56:42,684 - evaluation.metrics - INFO - Verifying local Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:42,684 - evaluation.metrics - INFO - Verifying Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:43,204 - evaluation.metrics - INFO - State dict contains 484 parameters
2025-05-27 15:56:43,205 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:43,205 - evaluation.metrics - INFO - Feature extraction mode: both
2025-05-27 15:56:43,205 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:56:47,779 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:50,589 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:56:50,788 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:50,790 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:56:50,790 - evaluation.metrics - INFO - Inception-v3 model loaded in 7.59 seconds
2025-05-27 15:56:50,790 - evaluation.metrics - INFO - Supporting both 2048-dim pool3 features (FID) and 1000-dim logits (IS)
2025-05-27 15:56:50,818 - evaluation.metrics - INFO - Inception model verification successful
2025-05-27 15:56:50,821 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 15:56:50,821 - evaluation.metrics - INFO - Loading Inception model for FID calculation (2048-dim features)...
2025-05-27 15:56:50,821 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:50,821 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 15:56:50,822 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:56:55,302 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:55,642 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:56:55,669 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:55,670 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:56:55,670 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.85 seconds
2025-05-27 15:56:55,670 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 15:56:55,671 - evaluation.metrics - INFO - Loading Inception model for IS calculation (1000-dim features)...
2025-05-27 15:56:55,671 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:56:55,671 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 15:56:55,671 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:57:00,298 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:57:00,640 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:57:01,001 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:57:01,003 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:57:01,003 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.33 seconds
2025-05-27 15:57:01,003 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 15:57:01,004 - evaluation.metrics - INFO - Calculating FID with 2048-dim pool3 features...
2025-05-27 15:57:01,004 - evaluation.metrics - INFO - Using precomputed FID baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 15:57:01,005 - evaluation.metrics - INFO - Calculating FID using precomputed baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 15:57:01,014 - evaluation.metrics - INFO - Loaded baseline: 199 samples, 2048D features
2025-05-27 15:57:01,016 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:57:01,949 - evaluation.metrics - INFO - Computing FID score with baseline...
2025-05-27 15:58:01,827 - evaluation.metrics - INFO - FID calculation completed in 60.82 seconds
2025-05-27 15:58:01,827 - evaluation.metrics - INFO - FID score: 320.7370
2025-05-27 15:58:01,830 - evaluation.metrics - INFO - Calculating IS with 1000-dim classification features...
2025-05-27 15:58:01,831 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:58:01,832 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:58:02,396 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:58:02,399 - evaluation.metrics - INFO - Inception Score calculation completed in 0.57 seconds
2025-05-27 15:58:02,399 - evaluation.metrics - INFO - Inception Score: 1.7037 ± 0.0673
2025-05-27 15:58:02,399 - evaluation.metrics - INFO - Evaluation completed in 79.80 seconds
2025-05-27 15:58:02,399 - evaluation.metrics - INFO - Results: FID = 320.7370, IS = 1.7037 ± 0.0673
2025-05-27 15:58:02,405 - evaluation.metrics - INFO - Starting channel-specific evaluation with simplified approach...
2025-05-27 15:58:02,405 - evaluation.metrics - INFO - Will evaluate 3 channels
2025-05-27 15:58:02,405 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 15:58:02,405 - evaluation.metrics - INFO - Loading Inception models with differential configuration...
2025-05-27 15:58:02,405 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:58:02,405 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 15:58:02,405 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:58:06,689 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:58:07,050 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:58:07,076 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:58:07,077 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:58:07,077 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.67 seconds
2025-05-27 15:58:07,077 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 15:58:07,078 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:58:07,078 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 15:58:07,078 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:58:11,294 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:58:11,634 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:58:11,660 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:58:11,661 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:58:11,661 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.58 seconds
2025-05-27 15:58:11,661 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 15:58:11,665 - evaluation.metrics - INFO - Evaluating Period-DM surface (channel 0)...
2025-05-27 15:58:11,667 - evaluation.metrics - INFO - Channel 0 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.6142, -0.0725]
2025-05-27 15:58:11,669 - evaluation.metrics - INFO - Evaluating Period-DM surface using channel replication method
2025-05-27 15:58:11,669 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 15:58:11,669 - evaluation.metrics - INFO - Calculating FID for Period-DM surface with 2048-dim features...
2025-05-27 15:58:11,669 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 15:58:11,670 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:58:11,670 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:58:11,672 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 15:58:12,288 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:58:13,456 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 15:59:24,609 - evaluation.metrics - INFO - FID calculation completed in 72.94 seconds
2025-05-27 15:59:24,610 - evaluation.metrics - INFO - FID score: 460.2098
2025-05-27 15:59:24,610 - evaluation.metrics - INFO - Calculating IS for Period-DM surface with 1000-dim features...
2025-05-27 15:59:24,611 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:59:24,616 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:59:25,166 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:59:25,169 - evaluation.metrics - INFO - Inception Score calculation completed in 0.56 seconds
2025-05-27 15:59:25,169 - evaluation.metrics - INFO - Inception Score: 1.3785 ± 0.0563
2025-05-27 15:59:25,170 - evaluation.metrics - INFO - Period-DM surface results: FID = 460.2098, IS = 1.3785 ± 0.0563
2025-05-27 15:59:25,170 - evaluation.metrics - INFO - Evaluating Phase-Subband surface (channel 1)...
2025-05-27 15:59:25,172 - evaluation.metrics - INFO - Channel 1 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.8554, 0.7836]
2025-05-27 15:59:25,175 - evaluation.metrics - INFO - Evaluating Phase-Subband surface using channel replication method
2025-05-27 15:59:25,175 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 15:59:25,175 - evaluation.metrics - INFO - Calculating FID for Phase-Subband surface with 2048-dim features...
2025-05-27 15:59:25,175 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 15:59:25,177 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:59:25,177 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:59:25,179 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 15:59:25,782 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:59:26,857 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:00:39,896 - evaluation.metrics - INFO - FID calculation completed in 74.72 seconds
2025-05-27 16:00:39,896 - evaluation.metrics - INFO - FID score: 301.5430
2025-05-27 16:00:39,897 - evaluation.metrics - INFO - Calculating IS for Phase-Subband surface with 1000-dim features...
2025-05-27 16:00:39,898 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:00:39,906 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:00:40,448 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:00:40,452 - evaluation.metrics - INFO - Inception Score calculation completed in 0.55 seconds
2025-05-27 16:00:40,452 - evaluation.metrics - INFO - Inception Score: 1.4499 ± 0.0324
2025-05-27 16:00:40,452 - evaluation.metrics - INFO - Phase-Subband surface results: FID = 301.5430, IS = 1.4499 ± 0.0324
2025-05-27 16:00:40,454 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface (channel 2)...
2025-05-27 16:00:40,456 - evaluation.metrics - INFO - Channel 2 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.2271, -0.0661]
2025-05-27 16:00:40,460 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface using channel replication method
2025-05-27 16:00:40,460 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 16:00:40,460 - evaluation.metrics - INFO - Calculating FID for Phase-Subintegration surface with 2048-dim features...
2025-05-27 16:00:40,460 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 16:00:40,462 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 16:00:40,462 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 16:00:40,478 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 16:00:41,082 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 16:00:42,254 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 16:01:49,413 - evaluation.metrics - INFO - FID calculation completed in 68.95 seconds
2025-05-27 16:01:49,413 - evaluation.metrics - INFO - FID score: 367.0688
2025-05-27 16:01:49,414 - evaluation.metrics - INFO - Calculating IS for Phase-Subintegration surface with 1000-dim features...
2025-05-27 16:01:49,414 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 16:01:49,419 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 16:01:49,933 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 16:01:49,936 - evaluation.metrics - INFO - Inception Score calculation completed in 0.52 seconds
2025-05-27 16:01:49,936 - evaluation.metrics - INFO - Inception Score: 1.1274 ± 0.0132
2025-05-27 16:01:49,936 - evaluation.metrics - INFO - Phase-Subintegration surface results: FID = 367.0688, IS = 1.1274 ± 0.0132
2025-05-27 16:01:49,936 - evaluation.metrics - INFO - Average results: FID = 376.2739, IS = 1.3186
2025-05-27 16:01:49,936 - evaluation.metrics - INFO - Channel-specific evaluation completed in 227.53 seconds
