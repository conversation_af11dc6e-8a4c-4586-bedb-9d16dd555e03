2025-05-27 15:25:13,293 - evaluation.metrics - INFO - Generating 995 samples for evaluation with batch size 50
2025-05-27 15:25:13,293 - evaluation.metrics - INFO - Using external sampler: PulsarAdaptiveSampler
2025-05-27 15:25:13,293 - evaluation.metrics - INFO - Will generate 995 samples in 20 batches of size 50
2025-05-27 15:25:13,294 - evaluation.metrics - INFO - Using external sampler for batch 1/20
2025-05-27 15:25:30,965 - evaluation.metrics - INFO - Using external sampler for batch 2/20
2025-05-27 15:25:31,065 - evaluation.metrics - INFO - Using external sampler for batch 3/20
2025-05-27 15:25:31,261 - evaluation.metrics - INFO - Using external sampler for batch 4/20
2025-05-27 15:25:31,458 - evaluation.metrics - INFO - Using external sampler for batch 5/20
2025-05-27 15:25:31,649 - evaluation.metrics - INFO - Generated 250/995 samples
2025-05-27 15:25:31,650 - evaluation.metrics - INFO - Using external sampler for batch 6/20
2025-05-27 15:25:31,856 - evaluation.metrics - INFO - Using external sampler for batch 7/20
2025-05-27 15:25:32,043 - evaluation.metrics - INFO - Using external sampler for batch 8/20
2025-05-27 15:25:32,234 - evaluation.metrics - INFO - Using external sampler for batch 9/20
2025-05-27 15:25:32,452 - evaluation.metrics - INFO - Using external sampler for batch 10/20
2025-05-27 15:25:32,659 - evaluation.metrics - INFO - Generated 500/995 samples
2025-05-27 15:25:32,659 - evaluation.metrics - INFO - Using external sampler for batch 11/20
2025-05-27 15:25:32,848 - evaluation.metrics - INFO - Using external sampler for batch 12/20
2025-05-27 15:25:33,064 - evaluation.metrics - INFO - Using external sampler for batch 13/20
2025-05-27 15:25:33,254 - evaluation.metrics - INFO - Using external sampler for batch 14/20
2025-05-27 15:25:33,453 - evaluation.metrics - INFO - Using external sampler for batch 15/20
2025-05-27 15:25:33,656 - evaluation.metrics - INFO - Generated 750/995 samples
2025-05-27 15:25:33,656 - evaluation.metrics - INFO - Using external sampler for batch 16/20
2025-05-27 15:25:33,847 - evaluation.metrics - INFO - Using external sampler for batch 17/20
2025-05-27 15:25:34,325 - evaluation.metrics - INFO - Using external sampler for batch 18/20
2025-05-27 15:25:34,532 - evaluation.metrics - INFO - Using external sampler for batch 19/20
2025-05-27 15:25:34,752 - evaluation.metrics - INFO - Using external sampler for batch 20/20
2025-05-27 15:25:34,995 - evaluation.metrics - INFO - Generated 995/995 samples
2025-05-27 15:25:35,003 - evaluation.metrics - INFO - Successfully generated 995/995 samples
2025-05-27 15:25:35,080 - evaluation.metrics - INFO - Final samples already in valid range: [-0.716521, 0.705169]
2025-05-27 15:25:35,179 - evaluation.metrics - INFO - Generated 995 samples in 21.89 seconds
2025-05-27 15:25:35,179 - evaluation.metrics - INFO - Sample range: [-0.7165, 0.7052]
2025-05-27 15:25:37,302 - evaluation.metrics - INFO - Starting model evaluation...
2025-05-27 15:25:37,302 - evaluation.metrics - INFO - Preprocessing real images for evaluation...
2025-05-27 15:25:37,302 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:25:37,303 - evaluation.metrics - INFO - real images value range: [-1.0000, 1.0000]
2025-05-27 15:25:37,305 - evaluation.metrics - INFO - real images channel variances: [0.15036647021770477, 0.16898447275161743, 0.13913485407829285]
2025-05-27 15:25:37,305 - evaluation.metrics - INFO - Preprocessed real images: shape=torch.Size([995, 3, 32, 32]), range=[-1.0000, 1.0000]
2025-05-27 15:25:37,305 - evaluation.metrics - INFO - Preprocessing generated images for evaluation...
2025-05-27 15:25:37,305 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:25:37,308 - evaluation.metrics - INFO - generated images value range: [-0.7165, 0.7052]
2025-05-27 15:25:37,309 - evaluation.metrics - INFO - generated images channel variances: [0.0016345460899174213, 0.026880670338869095, 0.0006459258729591966]
2025-05-27 15:25:37,309 - evaluation.metrics - INFO - Preprocessed generated images: shape=torch.Size([995, 3, 32, 32]), range=[-0.7165, 0.7052]
2025-05-27 15:25:37,309 - evaluation.metrics - INFO - Verifying local Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:37,309 - evaluation.metrics - INFO - Verifying Inception model at evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:38,560 - evaluation.metrics - INFO - State dict contains 484 parameters
2025-05-27 15:25:38,561 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:38,561 - evaluation.metrics - INFO - Feature extraction mode: both
2025-05-27 15:25:38,562 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:25:44,484 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:46,310 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:25:46,725 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:46,728 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:25:46,728 - evaluation.metrics - INFO - Inception-v3 model loaded in 8.17 seconds
2025-05-27 15:25:46,728 - evaluation.metrics - INFO - Supporting both 2048-dim pool3 features (FID) and 1000-dim logits (IS)
2025-05-27 15:25:46,846 - evaluation.metrics - INFO - Inception model verification successful
2025-05-27 15:25:46,849 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 15:25:46,849 - evaluation.metrics - INFO - Loading Inception model for FID calculation (2048-dim features)...
2025-05-27 15:25:46,849 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:46,850 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 15:25:46,850 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:25:51,089 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:51,358 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:25:51,383 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:51,384 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:25:51,384 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.53 seconds
2025-05-27 15:25:51,384 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 15:25:51,385 - evaluation.metrics - INFO - Loading Inception model for IS calculation (1000-dim features)...
2025-05-27 15:25:51,385 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:51,385 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 15:25:51,385 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:25:55,397 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:55,730 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:25:55,781 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:25:55,783 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:25:55,783 - evaluation.metrics - INFO - Inception-v3 model loaded in 4.40 seconds
2025-05-27 15:25:55,783 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 15:25:55,784 - evaluation.metrics - INFO - Calculating FID with 2048-dim pool3 features...
2025-05-27 15:25:55,784 - evaluation.metrics - INFO - Using precomputed FID baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 15:25:55,784 - evaluation.metrics - INFO - Calculating FID using precomputed baseline from evaluation/fid_baseline_2048dim.pkl
2025-05-27 15:25:55,802 - evaluation.metrics - INFO - Loaded baseline: 199 samples, 2048D features
2025-05-27 15:25:55,804 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:25:56,947 - evaluation.metrics - INFO - Computing FID score with baseline...
2025-05-27 15:26:53,524 - evaluation.metrics - INFO - FID calculation completed in 57.74 seconds
2025-05-27 15:26:53,524 - evaluation.metrics - INFO - FID score: 269.1612
2025-05-27 15:26:53,527 - evaluation.metrics - INFO - Calculating IS with 1000-dim classification features...
2025-05-27 15:26:53,527 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:26:53,528 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:26:54,060 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:26:54,063 - evaluation.metrics - INFO - Inception Score calculation completed in 0.54 seconds
2025-05-27 15:26:54,063 - evaluation.metrics - INFO - Inception Score: 2.0116 ± 0.0932
2025-05-27 15:26:54,063 - evaluation.metrics - INFO - Evaluation completed in 76.76 seconds
2025-05-27 15:26:54,063 - evaluation.metrics - INFO - Results: FID = 269.1612, IS = 2.0116 ± 0.0932
2025-05-27 15:26:54,067 - evaluation.metrics - INFO - Starting channel-specific evaluation with simplified approach...
2025-05-27 15:26:54,068 - evaluation.metrics - INFO - Will evaluate 3 channels
2025-05-27 15:26:54,068 - evaluation.metrics - INFO - Using 995 real images and 995 generated images
2025-05-27 15:26:54,068 - evaluation.metrics - INFO - Loading Inception models with differential configuration...
2025-05-27 15:26:54,068 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:26:54,068 - evaluation.metrics - INFO - Feature extraction mode: fid
2025-05-27 15:26:54,068 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:27:00,279 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:27:00,646 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:27:00,671 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:27:00,672 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:27:00,672 - evaluation.metrics - INFO - Inception-v3 model loaded in 6.60 seconds
2025-05-27 15:27:00,672 - evaluation.metrics - INFO - Using 2048-dim pool3 features for FID evaluation
2025-05-27 15:27:00,672 - evaluation.metrics - INFO - Loading Inception-v3 model from local path: evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:27:00,673 - evaluation.metrics - INFO - Feature extraction mode: is
2025-05-27 15:27:00,673 - evaluation.metrics - INFO - Creating Inception-v3 architecture...
2025-05-27 15:27:05,594 - evaluation.metrics - INFO - Loading weights from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:27:05,963 - evaluation.metrics - INFO - Loaded state dict with strict=True
2025-05-27 15:27:05,988 - evaluation.metrics - INFO - Successfully loaded local Inception-v3 model from evaluation/inception_v3_google-0cc3c7bd.pth
2025-05-27 15:27:05,989 - evaluation.metrics - INFO - Inception-v3 model has 27,161,264 parameters
2025-05-27 15:27:05,989 - evaluation.metrics - INFO - Inception-v3 model loaded in 5.32 seconds
2025-05-27 15:27:05,989 - evaluation.metrics - INFO - Using 1000-dim logits for IS evaluation
2025-05-27 15:27:05,993 - evaluation.metrics - INFO - Evaluating Period-DM surface (channel 0)...
2025-05-27 15:27:05,995 - evaluation.metrics - INFO - Channel 0 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.7165, -0.1146]
2025-05-27 15:27:05,997 - evaluation.metrics - INFO - Evaluating Period-DM surface using channel replication method
2025-05-27 15:27:05,997 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 15:27:05,997 - evaluation.metrics - INFO - Calculating FID for Period-DM surface with 2048-dim features...
2025-05-27 15:27:05,997 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 15:27:05,998 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:27:05,998 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:27:06,000 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 15:27:06,667 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:27:07,850 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 15:28:29,409 - evaluation.metrics - INFO - FID calculation completed in 83.41 seconds
2025-05-27 15:28:29,409 - evaluation.metrics - INFO - FID score: 446.9159
2025-05-27 15:28:29,410 - evaluation.metrics - INFO - Calculating IS for Period-DM surface with 1000-dim features...
2025-05-27 15:28:29,410 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:28:29,417 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:28:31,145 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:28:31,148 - evaluation.metrics - INFO - Inception Score calculation completed in 1.74 seconds
2025-05-27 15:28:31,148 - evaluation.metrics - INFO - Inception Score: 1.7853 ± 0.0970
2025-05-27 15:28:31,148 - evaluation.metrics - INFO - Period-DM surface results: FID = 446.9159, IS = 1.7853 ± 0.0970
2025-05-27 15:28:31,148 - evaluation.metrics - INFO - Evaluating Phase-Subband surface (channel 1)...
2025-05-27 15:28:31,150 - evaluation.metrics - INFO - Channel 1 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.7073, 0.7052]
2025-05-27 15:28:31,153 - evaluation.metrics - INFO - Evaluating Phase-Subband surface using channel replication method
2025-05-27 15:28:31,153 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 15:28:31,153 - evaluation.metrics - INFO - Calculating FID for Phase-Subband surface with 2048-dim features...
2025-05-27 15:28:31,153 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 15:28:31,155 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:28:31,155 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:28:31,157 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 15:28:31,764 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:28:32,878 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 15:29:54,510 - evaluation.metrics - INFO - FID calculation completed in 83.36 seconds
2025-05-27 15:29:54,511 - evaluation.metrics - INFO - FID score: 287.9080
2025-05-27 15:29:54,512 - evaluation.metrics - INFO - Calculating IS for Phase-Subband surface with 1000-dim features...
2025-05-27 15:29:54,512 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:29:54,520 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:29:55,053 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:29:55,056 - evaluation.metrics - INFO - Inception Score calculation completed in 0.54 seconds
2025-05-27 15:29:55,056 - evaluation.metrics - INFO - Inception Score: 2.3556 ± 0.0629
2025-05-27 15:29:55,056 - evaluation.metrics - INFO - Phase-Subband surface results: FID = 287.9080, IS = 2.3556 ± 0.0629
2025-05-27 15:29:55,056 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface (channel 2)...
2025-05-27 15:29:55,058 - evaluation.metrics - INFO - Channel 2 value ranges - Real: [-1.0000, 1.0000], Generated: [-0.2580, 0.1481]
2025-05-27 15:29:55,062 - evaluation.metrics - INFO - Evaluating Phase-Subintegration surface using channel replication method
2025-05-27 15:29:55,064 - evaluation.metrics - INFO - Channel shapes - Real: torch.Size([995, 3, 32, 32]), Generated: torch.Size([995, 3, 32, 32])
2025-05-27 15:29:55,064 - evaluation.metrics - INFO - Calculating FID for Phase-Subintegration surface with 2048-dim features...
2025-05-27 15:29:55,064 - evaluation.metrics - INFO - Calculating FID between 995 real images and 995 generated images
2025-05-27 15:29:55,066 - evaluation.metrics - INFO - Moving real images from cuda:0 to cuda
2025-05-27 15:29:55,066 - evaluation.metrics - INFO - Moving generated images from cpu to cuda
2025-05-27 15:29:55,078 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from real images...
2025-05-27 15:29:55,670 - evaluation.metrics - INFO - Extracting 2048-dim pool3 features from generated images...
2025-05-27 15:29:56,748 - evaluation.metrics - INFO - Computing FID score...
2025-05-27 15:31:02,117 - evaluation.metrics - INFO - FID calculation completed in 67.05 seconds
2025-05-27 15:31:02,117 - evaluation.metrics - INFO - FID score: 316.0285
2025-05-27 15:31:02,118 - evaluation.metrics - INFO - Calculating IS for Phase-Subintegration surface with 1000-dim features...
2025-05-27 15:31:02,118 - evaluation.metrics - INFO - Calculating Inception Score for 995 images with 10 splits
2025-05-27 15:31:02,188 - evaluation.metrics - INFO - Extracting 1000-dim classification predictions...
2025-05-27 15:31:02,740 - evaluation.metrics - INFO - Calculating IS scores for 10 splits...
2025-05-27 15:31:02,743 - evaluation.metrics - INFO - Inception Score calculation completed in 0.63 seconds
2025-05-27 15:31:02,743 - evaluation.metrics - INFO - Inception Score: 1.5271 ± 0.0373
2025-05-27 15:31:02,743 - evaluation.metrics - INFO - Phase-Subintegration surface results: FID = 316.0285, IS = 1.5271 ± 0.0373
2025-05-27 15:31:02,743 - evaluation.metrics - INFO - Average results: FID = 350.2841, IS = 1.8893
2025-05-27 15:31:02,743 - evaluation.metrics - INFO - Channel-specific evaluation completed in 248.68 seconds
