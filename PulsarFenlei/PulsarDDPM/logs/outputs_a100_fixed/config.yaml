base_channels: 48
batch_size: 16
channel_aware: true
channel_diffusion_weight: 0.0
channel_mults: 1,2,3
compile_model: true
consistency_weight: 0.2
data_dir: /Pulsar/PulsarFenlei/data/htru1-batches-py
ddim_eta: 0.0
ddim_steps: 100
device: cuda
dropout: 0.2
enable_adaptive_weights: true
enable_data_augmentation: true
enable_loss_scaling: true
eval_batch_size: 50
eval_every: 50
eval_num_samples: 995
gradient_clip_val: 0.5
img_channels: 3
inception_model_path: evaluation/inception_v3_google-0cc3c7bd.pth
log_every: 10
lr: 8.0e-05
max_epochs: 400
mixed_precision: true
mse_weight: 0.6
num_heads: 6
num_inference_steps: 20
num_res_blocks: 2
num_timesteps: 300
num_workers: 8
numerical_stability: true
output_dir: outputs_a100_fixed
persistent_workers: true
physics_weight: 0.2
pin_memory: true
pulsar_optimization: true
resume: null
save_every: 50
seed: 42
spectral_weight: 0.0
stage_epochs: 150,150,100
stage_lr_factors: 1.0,0.9,0.8
transformer_dim: 120
use_optimized_components: true
use_pulsar_adaptive: true
warmup_epochs: 5
weight_decay: 1.0e-05
