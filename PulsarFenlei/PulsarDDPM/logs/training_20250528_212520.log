[0;35m[2025-05-28 21:25:20] 🚀 WGAN-GP+VAE脉冲星生成系统 - 完整训练流程[0m
[0;34m[2025-05-28 21:25:20] ℹ️  开始时间: Wed May 28 21:25:20 CST 2025[0m
[0;34m[2025-05-28 21:25:20] ℹ️  训练配置: 150 epochs, batch_size=16, lr=5e-5[0m
[0;34m[2025-05-28 21:25:20] ℹ️  创建必要的目录...[0m
[0;32m[2025-05-28 21:25:20] ✅ 目录创建完成[0m
[0;35m[2025-05-28 21:25:20] 🚀 开始环境检查[0m
[0;32m[2025-05-28 21:25:21] ✅ Conda环境检查通过[0m
[0;34m[2025-05-28 21:25:21] ℹ️  激活conda环境: wgan_env[0m
[0;32m[2025-05-28 21:25:22] ✅ Conda环境激活成功[0m
[0;34m[2025-05-28 21:25:22] ℹ️  检查Python环境...[0m
✅ Python环境检查通过
  - PyTorch: 2.1.2+cu118
  - NumPy: 1.24.3
  - h5py: 3.9.0
  - Matplotlib: 3.7.2
[0;34m[2025-05-28 21:25:25] ℹ️  检查GPU状态...[0m
