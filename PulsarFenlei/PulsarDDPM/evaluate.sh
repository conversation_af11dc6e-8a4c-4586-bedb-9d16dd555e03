#!/bin/bash

# =============================================================================
# WGAN-GP+VAE模型评估脚本
# 
# 用法：
#   ./evaluate.sh                                    # 使用最新检查点
#   ./evaluate.sh --checkpoint path/to/model.pth     # 指定检查点
#   ./evaluate.sh --num_samples 2000                 # 指定生成样本数
# =============================================================================

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 默认参数
CHECKPOINT=""
NUM_SAMPLES=1000
BATCH_SIZE=32
OUTPUT_DIR="./evaluation_results"
SAVE_SAMPLES=false

# 函数：打印帮助信息
show_help() {
    echo "WGAN-GP+VAE模型评估脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --checkpoint PATH     指定模型检查点路径"
    echo "  --num_samples NUM     生成样本数量 (默认: 1000)"
    echo "  --batch_size SIZE     评估批次大小 (默认: 32)"
    echo "  --output_dir DIR      输出目录 (默认: ./evaluation_results)"
    echo "  --save_samples        保存生成的样本数据"
    echo "  --help, -h           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用最新检查点"
    echo "  $0 --checkpoint model_final.pth      # 指定检查点"
    echo "  $0 --num_samples 2000 --save_samples # 生成2000个样本并保存"
}

# 函数：查找最新检查点
find_latest_checkpoint() {
    local checkpoint_dir="./checkpoints"
    
    if [[ ! -d "$checkpoint_dir" ]]; then
        echo ""
        return 1
    fi
    
    # 查找最新的.pth文件
    local latest=$(find "$checkpoint_dir" -name "*.pth" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [[ -n "$latest" && -f "$latest" ]]; then
        echo "$latest"
        return 0
    else
        echo ""
        return 1
    fi
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --checkpoint)
            CHECKPOINT="$2"
            shift 2
            ;;
        --num_samples)
            NUM_SAMPLES="$2"
            shift 2
            ;;
        --batch_size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --output_dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --save_samples)
            SAVE_SAMPLES=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}🎯 WGAN-GP+VAE模型评估${NC}"
echo -e "${BLUE}========================${NC}"

# 检查是否在正确的目录
if [[ ! -f "run_evaluation.py" ]]; then
    echo -e "${RED}❌ 找不到评估脚本 run_evaluation.py${NC}"
    echo -e "${YELLOW}请确保在项目根目录下运行此脚本${NC}"
    exit 1
fi

# 如果没有指定检查点，尝试查找最新的
if [[ -z "$CHECKPOINT" ]]; then
    echo -e "${YELLOW}🔍 未指定检查点，查找最新检查点...${NC}"
    CHECKPOINT=$(find_latest_checkpoint)
    
    if [[ -z "$CHECKPOINT" ]]; then
        echo -e "${RED}❌ 未找到任何检查点文件${NC}"
        echo -e "${YELLOW}请确保训练已完成并生成了检查点文件${NC}"
        echo -e "${YELLOW}或使用 --checkpoint 参数指定检查点路径${NC}"
        exit 1
    else
        echo -e "${GREEN}✅ 找到最新检查点: $CHECKPOINT${NC}"
    fi
fi

# 验证检查点文件存在
if [[ ! -f "$CHECKPOINT" ]]; then
    echo -e "${RED}❌ 检查点文件不存在: $CHECKPOINT${NC}"
    exit 1
fi

# 显示评估参数
echo -e "${GREEN}评估参数:${NC}"
echo -e "  检查点: $CHECKPOINT"
echo -e "  生成样本数: $NUM_SAMPLES"
echo -e "  批次大小: $BATCH_SIZE"
echo -e "  输出目录: $OUTPUT_DIR"
echo -e "  保存样本: $SAVE_SAMPLES"
echo ""

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 构建评估命令
EVAL_CMD="python3 run_evaluation.py \
    --checkpoint \"$CHECKPOINT\" \
    --num_samples $NUM_SAMPLES \
    --batch_size $BATCH_SIZE \
    --output_dir \"$OUTPUT_DIR\" \
    --channel_analysis"

if [[ "$SAVE_SAMPLES" == "true" ]]; then
    EVAL_CMD="$EVAL_CMD --save_samples"
fi

echo -e "${GREEN}🚀 开始模型评估...${NC}"
echo ""

# 执行评估
if eval "$EVAL_CMD"; then
    echo ""
    echo -e "${GREEN}🎉 评估完成！${NC}"
    echo -e "${GREEN}📁 结果保存在: $OUTPUT_DIR${NC}"
    echo ""
    echo -e "${BLUE}📋 查看评估结果:${NC}"
    echo -e "  - 评估报告: $OUTPUT_DIR/evaluation_report.md"
    echo -e "  - 样本对比: $OUTPUT_DIR/sample_comparison.png"
    echo -e "  - 生成网格: $OUTPUT_DIR/generated_grid.png"
    
    # 显示关键指标
    if [[ -f "$OUTPUT_DIR/evaluation_report.json" ]]; then
        echo ""
        echo -e "${BLUE}📊 关键指标:${NC}"
        python3 -c "
import json
try:
    with open('$OUTPUT_DIR/evaluation_report.json', 'r') as f:
        report = json.load(f)
    
    metrics = report['metrics']
    assessment = report['performance_assessment']
    
    print(f\"  FID: {metrics['fid']:.2f} (目标: <40) {'✅' if assessment['fid_achieved'] else '❌'}\")
    print(f\"  IS: {metrics['is_mean']:.2f} ± {metrics['is_std']:.2f} (目标: >5) {'✅' if assessment['is_achieved'] else '❌'}\")
    
    if assessment['fid_achieved'] and assessment['is_achieved']:
        print('\\n🎯 所有性能目标均已达成！')
    else:
        print('\\n⚠️ 部分性能目标未达成')
        
except Exception as e:
    print(f'无法读取评估结果: {e}')
"
    fi
    
    exit 0
else
    echo ""
    echo -e "${RED}❌ 评估失败${NC}"
    exit 1
fi
