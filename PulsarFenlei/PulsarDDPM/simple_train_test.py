#!/usr/bin/env python3
"""
简化的训练测试脚本
测试WGAN-GP+VAE系统是否能够运行几个epoch
"""

import sys
import os
sys.path.insert(0, os.getcwd())

import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import Dataset, DataLoader

# 简化的模拟数据集
class SimpleDataset(Dataset):
    def __init__(self, size=100):
        self.size = size
        
    def __len__(self):
        return self.size
        
    def __getitem__(self, idx):
        # 生成模拟的32x32x3脉冲星数据
        sample = torch.randn(3, 32, 32)
        
        # 添加一些结构
        x = torch.linspace(0, 4*np.pi, 32)
        y = torch.linspace(0, 4*np.pi, 32)
        X, Y = torch.meshgrid(x, y, indexing='ij')
        
        # Channel 0: 周期性结构
        sample[0] += 0.3 * torch.sin(X) * torch.cos(Y)
        # Channel 1&2: 相位结构
        sample[1] += 0.2 * torch.sin(2*X + Y)
        sample[2] += 0.2 * torch.cos(X + 2*Y)
        
        # 归一化到[-1, 1]
        sample = torch.tanh(sample)
        
        return sample

# 简化的VAE模型
class SimpleVAE(nn.Module):
    def __init__(self, latent_dim=64):
        super().__init__()
        
        # 编码器
        self.encoder = nn.Sequential(
            nn.Conv2d(3, 32, 4, 2, 1),  # 32x32 -> 16x16
            nn.ReLU(),
            nn.Conv2d(32, 64, 4, 2, 1), # 16x16 -> 8x8
            nn.ReLU(),
            nn.Conv2d(64, 128, 4, 2, 1), # 8x8 -> 4x4
            nn.ReLU(),
            nn.Flatten(),
            nn.Linear(128 * 4 * 4, 256),
            nn.ReLU()
        )
        
        self.fc_mu = nn.Linear(256, latent_dim)
        self.fc_logvar = nn.Linear(256, latent_dim)
        
        # 解码器
        self.decoder_fc = nn.Linear(latent_dim, 256)
        self.decoder = nn.Sequential(
            nn.Linear(256, 128 * 4 * 4),
            nn.ReLU(),
            nn.Unflatten(1, (128, 4, 4)),
            nn.ConvTranspose2d(128, 64, 4, 2, 1), # 4x4 -> 8x8
            nn.ReLU(),
            nn.ConvTranspose2d(64, 32, 4, 2, 1),  # 8x8 -> 16x16
            nn.ReLU(),
            nn.ConvTranspose2d(32, 3, 4, 2, 1),   # 16x16 -> 32x32
            nn.Tanh()
        )
        
    def encode(self, x):
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar
    
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z):
        h = self.decoder_fc(z)
        return self.decoder(h)
    
    def forward(self, x):
        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)
        recon = self.decode(z)
        return recon, mu, logvar

def vae_loss(recon_x, x, mu, logvar):
    """VAE损失函数"""
    # 重建损失
    recon_loss = nn.MSELoss()(recon_x, x)
    
    # KL散度
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    kl_loss = kl_loss / x.size(0)  # 平均到批次
    
    return recon_loss + 0.1 * kl_loss, recon_loss, kl_loss

def train_simple_vae(epochs=3, batch_size=8):
    """训练简化的VAE模型"""
    print(f"🚀 开始简化VAE训练测试 (epochs={epochs}, batch_size={batch_size})")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 数据
    dataset = SimpleDataset(size=100)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    print(f"数据集大小: {len(dataset)}, 批次数: {len(dataloader)}")
    
    # 模型
    model = SimpleVAE(latent_dim=64).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型参数量: {total_params/1e6:.2f}M")
    
    # 训练循环
    model.train()
    for epoch in range(epochs):
        epoch_loss = 0
        epoch_recon_loss = 0
        epoch_kl_loss = 0
        
        for batch_idx, batch in enumerate(dataloader):
            batch = batch.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            recon_batch, mu, logvar = model(batch)
            
            # 计算损失
            loss, recon_loss, kl_loss = vae_loss(recon_batch, batch, mu, logvar)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            epoch_recon_loss += recon_loss.item()
            epoch_kl_loss += kl_loss.item()
            
            if batch_idx % 5 == 0:
                print(f"  Epoch {epoch+1}/{epochs}, Batch {batch_idx+1}/{len(dataloader)}: "
                      f"Loss={loss.item():.4f}, Recon={recon_loss.item():.4f}, KL={kl_loss.item():.4f}")
        
        avg_loss = epoch_loss / len(dataloader)
        avg_recon = epoch_recon_loss / len(dataloader)
        avg_kl = epoch_kl_loss / len(dataloader)
        
        print(f"✅ Epoch {epoch+1}/{epochs} 完成: "
              f"Avg Loss={avg_loss:.4f}, Avg Recon={avg_recon:.4f}, Avg KL={avg_kl:.4f}")
    
    print("🎉 简化VAE训练测试完成！")
    
    # 测试生成
    print("\n🔍 测试生成能力...")
    model.eval()
    with torch.no_grad():
        # 生成一些样本
        z = torch.randn(4, 64).to(device)
        samples = model.decode(z)
        
        print(f"生成样本形状: {samples.shape}")
        print(f"生成样本范围: [{samples.min().item():.3f}, {samples.max().item():.3f}]")
    
    return True

if __name__ == "__main__":
    try:
        success = train_simple_vae(epochs=3, batch_size=8)
        if success:
            print("\n✅ 简化训练测试成功！系统基本可运行")
        else:
            print("\n❌ 简化训练测试失败")
    except Exception as e:
        print(f"\n❌ 训练测试出错: {e}")
        import traceback
        traceback.print_exc()
