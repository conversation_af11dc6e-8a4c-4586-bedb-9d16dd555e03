#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查HTRU1数据集的标签
"""

import os
import sys
import pickle
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    data_dir = '/Pulsar/PulsarFenlei/data/htru1-batches-py'
    
    # 加载元数据
    meta_file = os.path.join(data_dir, 'batches.meta')
    with open(meta_file, 'rb') as f:
        meta = pickle.load(f, encoding='bytes')
    
    print("标签名称:")
    for i, label_name in enumerate(meta[b'label_names']):
        print(f"  标签 {i}: {label_name.decode('utf-8')}")
    
    # 加载训练数据
    train_data = []
    train_labels = []
    
    for i in range(1, 6):
        file_path = os.path.join(data_dir, f'data_batch_{i}')
        with open(file_path, 'rb') as f:
            batch = pickle.load(f, encoding='bytes')
            train_data.extend(batch[b'data'])
            train_labels.extend(batch[b'labels'])
    
    # 加载测试数据
    test_file = os.path.join(data_dir, 'test_batch')
    with open(test_file, 'rb') as f:
        batch = pickle.load(f, encoding='bytes')
        test_data = batch[b'data']
        test_labels = batch[b'labels']
    
    # 统计标签分布
    train_label_counts = {}
    for label in train_labels:
        train_label_counts[label] = train_label_counts.get(label, 0) + 1
    
    test_label_counts = {}
    for label in test_labels:
        test_label_counts[label] = test_label_counts.get(label, 0) + 1
    
    print("\n训练集标签分布:")
    for label, count in train_label_counts.items():
        label_name = meta[b'label_names'][label].decode('utf-8')
        print(f"  标签 {label} ({label_name}): {count} 样本")
    
    print("\n测试集标签分布:")
    for label, count in test_label_counts.items():
        label_name = meta[b'label_names'][label].decode('utf-8')
        print(f"  标签 {label} ({label_name}): {count} 样本")
    
    # 检查HTRU1Dataset类的过滤
    from data.dataloader import HTRU1Dataset
    
    train_dataset = HTRU1Dataset(root=data_dir, train=True)
    test_dataset = HTRU1Dataset(root=data_dir, train=False)
    
    print("\nHTRU1Dataset过滤后:")
    print(f"  训练集: {len(train_dataset)} 样本")
    print(f"  测试集: {len(test_dataset)} 样本")
    
    # 检查第一个样本的标签
    if len(train_dataset) > 0:
        _, label = train_dataset[0]
        print(f"\n过滤后的第一个训练样本标签: {label} ({meta[b'label_names'][label].decode('utf-8')})")

if __name__ == '__main__':
    main()
