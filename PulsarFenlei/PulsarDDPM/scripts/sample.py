import os
import sys
import torch
import argparse
import logging
import yaml
import numpy as np
from typing import Dict, Any, List, Tuple
from tqdm import tqdm
import matplotlib.pyplot as plt

# Add project root directory to path to ensure imports work from any working directory
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import modules using relative imports from project root
from data.dataloader import get_dataloader
from models.unet import UNet
from models.diffusion import GaussianDiffusion
from evaluation.metrics import evaluate_model, evaluate_channels

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Sample from DDPM model")

    # Model arguments
    parser.add_argument("--checkpoint", type=str, required=True,
                        help="Path to checkpoint")
    parser.add_argument("--config", type=str, required=True,
                        help="Path to configuration file")

    # Sampling arguments
    parser.add_argument("--num_samples", type=int, default=995,
                        help="Number of samples to generate (changed from 199 to match training set positive samples)")
    parser.add_argument("--batch_size", type=int, default=32,
                        help="Batch size for sampling")

    # Output arguments
    parser.add_argument("--output_dir", type=str, default="samples",
                        help="Output directory")

    # Evaluation arguments
    parser.add_argument("--evaluate", action="store_true",
                        help="Whether to evaluate generated samples")
    parser.add_argument("--inception_model_path", type=str,
                        default="/Pulsar/PulsarFenlei/PulsarDDPM/evaluation/inception_v3_google-0cc3c7bd.pth",
                        help="Path to Inception-v3 model")
    parser.add_argument("--data_dir", type=str, default="/Pulsar/PulsarFenlei/data/htru1-batches-py",
                        help="Path to HTRU1 dataset")

    # Device arguments
    parser.add_argument("--device", type=str, default="cuda",
                        help="Device to use")

    # Debug arguments
    parser.add_argument("--debug", action="store_true",
                        help="Whether to use debug mode")

    # Random seed arguments
    parser.add_argument("--seed", type=int, default=None,
                        help="Random seed for reproducible sampling. If not specified, will use random seed")

    return parser.parse_args()


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file."""
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    return config


def build_model(config: Dict[str, Any], device: str) -> GaussianDiffusion:
    """Build DDPM model from configuration."""
    # Parse channel multipliers
    channel_mults = tuple(map(int, config["channel_mults"].split(",")))

    # Create UNet model
    unet = UNet(
        img_channels=config["img_channels"],
        base_channels=config["base_channels"],
        channel_mults=channel_mults,
        num_res_blocks=config["num_res_blocks"],
        time_emb_dim=config["time_emb_dim"],
        dropout=config["dropout"],
        # ECA parameters (default to False if not in config)
        use_eca=config.get("use_eca", False),
        use_input_eca=config.get("use_input_eca", False),
    )

    # Create diffusion model
    diffusion = GaussianDiffusion(
        model=unet,
        img_size=(config["img_size"], config["img_size"]),
        img_channels=config["img_channels"],
        betas=torch.linspace(1e-4, 0.02, config["num_timesteps"]),
        loss_type=config["loss_type"],
        ema_decay=config["ema_decay"],
        ema_start=config["ema_start"],
        ema_update_rate=config["ema_update_rate"],
        device=device,
    )

    return diffusion


def load_checkpoint(diffusion: GaussianDiffusion, checkpoint_path: str, device: str) -> None:
    """Load checkpoint with enhanced error handling."""

    # Check if checkpoint file exists
    if not os.path.exists(checkpoint_path):
        # Try to find alternative checkpoints
        checkpoint_dir = os.path.dirname(checkpoint_path)
        if os.path.exists(checkpoint_dir):
            available_checkpoints = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pt')]
            if available_checkpoints:
                # Use the most recent checkpoint
                latest_checkpoint = max(available_checkpoints,
                                      key=lambda x: os.path.getctime(os.path.join(checkpoint_dir, x)))
                checkpoint_path = os.path.join(checkpoint_dir, latest_checkpoint)
                logger.warning(f"Best checkpoint not found, using {latest_checkpoint}")
            else:
                raise FileNotFoundError(f"No checkpoint files found in {checkpoint_dir}")
        else:
            raise FileNotFoundError(f"Checkpoint directory not found: {checkpoint_dir}")

    checkpoint = torch.load(checkpoint_path, map_location=device)
    diffusion.load_state_dict(checkpoint["model_state_dict"])
    logger.info(f"Loaded checkpoint from {checkpoint_path}")


def sample_from_model(
    diffusion: GaussianDiffusion,
    num_samples: int,
    batch_size: int,
    device: str,
) -> torch.Tensor:
    """Sample from model."""
    logger.info(f"Generating {num_samples} samples...")

    # Calculate number of batches
    num_batches = int(np.ceil(num_samples / batch_size))

    # Generate samples
    samples = []

    with torch.no_grad():
        for i in tqdm(range(num_batches), desc="Generating samples"):
            # Calculate batch size for this batch
            batch_size_i = min(batch_size, num_samples - i * batch_size)

            # Generate samples
            batch_samples = diffusion.sample(batch_size_i, use_ema=True)

            # Move to CPU
            batch_samples = batch_samples.cpu()

            # Append to list
            samples.append(batch_samples)

    # Concatenate samples
    samples = torch.cat(samples, dim=0)

    return samples


def save_samples(
    samples: torch.Tensor,
    output_dir: str,
    num_samples_to_save: int = 16,
) -> None:
    """Save samples to disk."""
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Save all samples as numpy array
    samples_np = samples.numpy()
    samples_path = os.path.join(output_dir, "samples.npy")
    np.save(samples_path, samples_np)
    logger.info(f"Saved {len(samples)} samples to {samples_path}")

    # Save a grid of samples as image
    num_samples_to_save = min(num_samples_to_save, len(samples))
    grid_size = int(np.ceil(np.sqrt(num_samples_to_save)))

    # Create grid
    fig, axes = plt.subplots(grid_size, grid_size, figsize=(grid_size * 2, grid_size * 2))

    # Convert samples from [-1, 1] to [0, 1]
    samples_to_save = (samples[:num_samples_to_save].numpy() + 1) / 2

    # Plot samples
    for i in range(grid_size):
        for j in range(grid_size):
            idx = i * grid_size + j
            if idx < num_samples_to_save:
                # Get sample
                sample = samples_to_save[idx]

                # Transpose from (C, H, W) to (H, W, C)
                sample = np.transpose(sample, (1, 2, 0))

                # Plot sample
                axes[i, j].imshow(sample)
                axes[i, j].axis("off")
            else:
                axes[i, j].axis("off")

    # Save grid
    grid_path = os.path.join(output_dir, "samples_grid.png")
    plt.tight_layout()
    plt.savefig(grid_path)
    plt.close()
    logger.info(f"Saved grid of {num_samples_to_save} samples to {grid_path}")

    # Save individual channels
    for channel in range(samples.shape[1]):
        # Create grid
        fig, axes = plt.subplots(grid_size, grid_size, figsize=(grid_size * 2, grid_size * 2))

        # Plot samples
        for i in range(grid_size):
            for j in range(grid_size):
                idx = i * grid_size + j
                if idx < num_samples_to_save:
                    # Get sample
                    sample = samples_to_save[idx, channel]

                    # Plot sample
                    axes[i, j].imshow(sample, cmap="viridis")
                    axes[i, j].axis("off")
                else:
                    axes[i, j].axis("off")

        # Save grid
        grid_path = os.path.join(output_dir, f"samples_grid_channel_{channel}.png")
        plt.tight_layout()
        plt.savefig(grid_path)
        plt.close()
        logger.info(f"Saved grid of {num_samples_to_save} samples for channel {channel} to {grid_path}")


def evaluate_samples(
    real_images: torch.Tensor,
    generated_images: torch.Tensor,
    inception_model_path: str,
    device: str,
) -> Tuple[Dict[str, float], Dict[str, Dict[str, float]]]:
    """Evaluate generated samples."""
    logger.info("Evaluating generated samples...")

    # Evaluate model
    metrics = evaluate_model(
        real_images=real_images,
        generated_images=generated_images,
        inception_model_path=inception_model_path,
        batch_size=32,
        device=device,
    )

    # Evaluate channels
    channel_metrics = evaluate_channels(
        real_images=real_images,
        generated_images=generated_images,
        inception_model_path=inception_model_path,
        batch_size=32,
        device=device,
    )

    return metrics, channel_metrics


def main() -> None:
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Set random seed (before any other operations)
    if args.seed is not None:
        try:
            from utils.seed_utils import set_seed_for_evaluation
            actual_seed = set_seed_for_evaluation(args.seed)
            logger.info(f"Random seed set to {actual_seed} for reproducible sampling")
        except ImportError:
            logger.warning("Cannot import seed_utils, using basic random seed setting")
            import torch
            import numpy as np
            import random

            torch.manual_seed(args.seed)
            torch.cuda.manual_seed_all(args.seed)
            np.random.seed(args.seed)
            random.seed(args.seed)
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
            logger.info(f"Basic random seed set to {args.seed}")

    # Set debug mode
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug mode enabled")

    # Load configuration
    logger.info(f"Loading configuration from {args.config}...")
    config = load_config(args.config)

    # Build model
    logger.info("Building model...")
    diffusion = build_model(config, args.device)

    # Load checkpoint
    logger.info(f"Loading checkpoint from {args.checkpoint}...")
    load_checkpoint(diffusion, args.checkpoint, args.device)

    # Sample from model
    samples = sample_from_model(diffusion, args.num_samples, args.batch_size, args.device)

    # Save samples
    logger.info(f"Saving samples to {args.output_dir}...")
    save_samples(samples, args.output_dir)

    # Evaluate samples if requested
    if args.evaluate:
        logger.info("Loading real images...")
        # Load real images (using training set for FID consistency)
        train_dataloader = get_dataloader(
            root=args.data_dir,
            batch_size=args.batch_size,
            train=True,
            augment=False,
            num_workers=4,
            shuffle=False,
            drop_last=False,
            resolution=config["img_size"],
        )

        # Get real images
        real_images = []
        for x, _ in train_dataloader:
            real_images.append(x)
        real_images = torch.cat(real_images, dim=0)

        logger.info(f"Loaded {len(real_images)} real images")

        # Evaluate samples
        metrics, channel_metrics = evaluate_samples(
            real_images=real_images,
            generated_images=samples,
            inception_model_path=args.inception_model_path,
            device=args.device,
        )

        # Print metrics
        logger.info(f"Metrics: {metrics}")
        logger.info(f"Channel metrics: {channel_metrics}")

        # Save metrics
        metrics_path = os.path.join(args.output_dir, "metrics.yaml")
        with open(metrics_path, "w") as f:
            yaml.dump({"metrics": metrics, "channel_metrics": channel_metrics}, f)
        logger.info(f"Saved metrics to {metrics_path}")

    logger.info("Done!")


if __name__ == "__main__":
    main()
