import os
import sys
import torch
import argparse
import logging
import yaml
import importlib.util
from typing import Dict, Any, List, Tuple, Optional

# Add project root directory to path to ensure imports work from any working directory
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import modules using relative imports from project root
from data.dataloader import get_dataloader
from models.unet import UNet
from models.diffusion import GaussianDiffusion
from models.pulsar_adaptive_sampler import PulsarAdaptiveSampler

# 智能导入Trainer - 解决模块名冲突问题
def import_trainer():
    """智能导入Trainer类，解决模块名冲突"""
    import sys
    import os
    import importlib.util

    # 获取项目根目录
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
    trainer_path = os.path.join(project_root, "train", "trainer.py")

    if not os.path.exists(trainer_path):
        raise ImportError(f"Trainer module not found at {trainer_path}")

    # 使用importlib直接加载模块，避免名称冲突
    spec = importlib.util.spec_from_file_location("train_trainer", trainer_path)
    trainer_module = importlib.util.module_from_spec(spec)

    # 临时添加项目根目录到sys.path以支持trainer.py的导入
    original_path = sys.path.copy()
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    try:
        spec.loader.exec_module(trainer_module)
        return trainer_module.Trainer
    finally:
        # 恢复原始路径
        sys.path[:] = original_path

# 导入Trainer类
try:
    Trainer = import_trainer()
    print("✅ Trainer导入成功")
except ImportError as e:
    print(f"❌ 无法导入Trainer: {e}")
    print(f"Python路径: {sys.path}")
    print(f"当前工作目录: {os.getcwd()}")
    raise

# Configure logging
logger = logging.getLogger(__name__)

# We'll set up the handlers after setting up output directories
# This is a placeholder configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Set higher log level for specific modules to reduce output
logging.getLogger('models.diffusion').setLevel(logging.ERROR)  # Only show ERROR and above for diffusion model
logging.getLogger('evaluation.metrics').setLevel(logging.INFO)  # Keep INFO level for evaluation metrics


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Train DDPM model")

    # Data arguments
    parser.add_argument("--data_dir", type=str, default="../data/htru1-batches-py",
                        help="Path to HTRU1 dataset")
    parser.add_argument("--batch_size", type=int, default=32,
                        help="Batch size")
    parser.add_argument("--num_workers", type=int, default=4,
                        help="Number of workers for dataloader")

    # Model arguments
    parser.add_argument("--img_size", type=int, default=32,
                        help="Image size")
    parser.add_argument("--img_channels", type=int, default=3,
                        help="Number of image channels")
    parser.add_argument("--base_channels", type=int, default=64,
                        help="Number of base channels in UNet (64 for better expressivity with small samples)")
    parser.add_argument("--channel_mults", type=str, default="1,2,4,8",
                        help="Channel multipliers for UNet (wider multipliers for better hierarchical features)")
    parser.add_argument("--num_res_blocks", type=int, default=2,
                        help="Number of residual blocks per resolution (2 for better feature extraction)")
    parser.add_argument("--dropout", type=float, default=0.3,
                        help="Dropout rate (maintained at 0.3 for regularization)")
    parser.add_argument("--time_emb_dim", type=int, default=128,
                        help="Time embedding dimension (128 for better temporal representation)")
    parser.add_argument("--num_timesteps", type=int, default=1000,
                        help="Number of diffusion timesteps")
    parser.add_argument("--norm", type=str, default="gn",
                        help="Normalization type: gn, bn, in, ln, adaptive, conservative")
    parser.add_argument("--num_groups", type=int, default=8,
                        help="Number of groups for GroupNorm (must be a divisor of base_channels)")

    # Training arguments
    parser.add_argument("--lr", type=float, default=5e-5,
                        help="Learning rate (maintained at 5e-5 for stability)")
    parser.add_argument("--weight_decay", type=float, default=1e-5,
                        help="Weight decay (increased from 0.0 to 1e-5 for better regularization)")
    parser.add_argument("--scheduler_type", type=str, default="warmup_cosine",
                        help="Type of learning rate scheduler: warmup_cosine, cosine, step, warmup_step, "
                             "warmup_cosine_restart, cosine_warmup_restarts, curriculum_cosine, cosine_restart")
    parser.add_argument("--warmup_epochs", type=int, default=5,
                        help="Number of warmup epochs (maintained at 5 for stable initial training)")
    parser.add_argument("--max_epochs", type=int, default=200,
                        help="Maximum number of epochs")
    parser.add_argument("--min_lr", type=float, default=1e-6,
                        help="Minimum learning rate (lower bound for cosine annealing)")
    parser.add_argument("--lr_cycle_epochs", type=int, default=None,
                        help="Number of epochs per learning rate cycle (for restart schedulers)")
    parser.add_argument("--lr_cycle_mult", type=float, default=1.0,
                        help="Multiplier for cycle length after each restart")
    parser.add_argument("--lr_cycle_decay", type=float, default=0.5,
                        help="Decay factor for max learning rate after each restart")

    # Cosine annealing restart parameters (for cosine_restart scheduler)
    parser.add_argument("--t_0", type=int, default=400,
                        help="Initial restart period for CosineAnnealingWarmRestarts scheduler")
    parser.add_argument("--t_mult", type=float, default=1.2,
                        help="Period multiplier factor for CosineAnnealingWarmRestarts scheduler")
    parser.add_argument("--restart_decay", type=float, default=0.8,
                        help="Learning rate decay factor on restart for CosineAnnealingWarmRestarts scheduler")
    parser.add_argument("--visualize_lr", action="store_true",
                        help="Whether to visualize learning rate schedule")
    parser.add_argument("--gradient_clip_val", type=float, default=0.5,
                        help="Gradient clipping value (maintained at 0.5 to prevent gradient explosion)")
    parser.add_argument("--ema_decay", type=float, default=0.9999,
                        help="EMA decay rate")
    parser.add_argument("--ema_start", type=int, default=5000,
                        help="Number of steps before starting EMA")
    parser.add_argument("--ema_update_rate", type=int, default=1,
                        help="Number of steps between EMA updates")
    parser.add_argument("--loss_type", type=str, default="l2",
                        help="Loss type (l1 or l2)")
    parser.add_argument("--grad_accumulation_steps", type=int, default=1,
                        help="Number of steps to accumulate gradients over")

    # Output arguments
    parser.add_argument("--output_dir", type=str, default="outputs",
                        help="Output directory")
    parser.add_argument("--log_dir", type=str, default="logs",
                        help="Log directory")
    parser.add_argument("--checkpoint_dir", type=str, default="checkpoints",
                        help="Checkpoint directory")
    parser.add_argument("--save_every", type=int, default=10,
                        help="Save checkpoint every n epochs")
    parser.add_argument("--eval_every", type=int, default=10,
                        help="Evaluate every n epochs")

    # Progressive training arguments
    parser.add_argument("--progressive_training", action="store_true",
                        help="Whether to use progressive training")
    parser.add_argument("--progressive_resolutions", type=str, default="16,32",
                        help="Resolutions for progressive training")
    parser.add_argument("--progressive_epochs", type=str, default="100,100",
                        help="Epochs for each resolution in progressive training")

    # Device arguments
    parser.add_argument("--device", type=str, default="cuda",
                        help="Device to use")

    # Mixed precision arguments
    parser.add_argument("--amp", action="store_true",
                        help="Whether to use automatic mixed precision training")
    parser.add_argument("--amp_scale", type=float, default=2**16,
                        help="Initial scale factor for AMP")

    # Debug arguments
    parser.add_argument("--debug", action="store_true",
                        help="Whether to use debug mode")

    # Checkpoint arguments
    parser.add_argument("--resume", type=str, default=None,
                        help="Path to checkpoint to resume from")

    # 已移除small_sample_mode相关参数

    # Removed curriculum learning arguments - replaced by intelligent data augmentation

    # TASK 2: Pulsar-specific loss function arguments
    parser.add_argument("--use_channel_weighted_loss", action="store_true",
                        help="Whether to use channel-weighted loss for pulsar data")
    parser.add_argument("--channel_weights", type=str, default="0.4,0.35,0.25",
                        help="Weights for Period-DM, Phase-Subband, Phase-Subintegration channels")
    parser.add_argument("--adaptive_channel_weights", action="store_true",
                        help="Whether to use adaptive channel weights")

    # TASK 3: Data augmentation arguments
    parser.add_argument("--use_channel_augmentation", action="store_true",
                        help="Whether to use channel-specific augmentation")
    parser.add_argument("--augmentation_prob", type=float, default=0.7,
                        help="Probability of applying augmentation")
    parser.add_argument("--progressive_augmentation", action="store_true",
                        help="Whether to use progressive augmentation")
    parser.add_argument("--progressive_intensity", action="store_true",
                        help="Whether to use progressive intensity adjustment")
    parser.add_argument("--min_intensity", type=float, default=0.3,
                        help="Minimum augmentation intensity")
    parser.add_argument("--max_intensity", type=float, default=1.0,
                        help="Maximum augmentation intensity")
    parser.add_argument("--enable_physical_validation", action="store_true",
                        help="Whether to enable physical constraint validation")
    parser.add_argument("--peak_preservation_threshold", type=float, default=0.8,
                        help="Threshold for peak preservation validation")
    parser.add_argument("--stripe_coherence_threshold", type=float, default=0.7,
                        help="Threshold for stripe coherence validation")
    parser.add_argument("--detail_variance_threshold", type=float, default=0.5,
                        help="Threshold for detail variance validation")
    parser.add_argument("--adaptive_probability", action="store_true",
                        help="Whether to use adaptive augmentation probability")
    parser.add_argument("--loss_history_window", type=int, default=10,
                        help="Window size for loss history in adaptive probability")
    parser.add_argument("--prob_adjustment_factor", type=float, default=0.1,
                        help="Adjustment factor for adaptive probability")
    parser.add_argument("--min_prob", type=float, default=0.3,
                        help="Minimum augmentation probability")
    parser.add_argument("--max_prob", type=float, default=0.9,
                        help="Maximum augmentation probability")

    # Channel-specific processing arguments
    parser.add_argument("--channel_specific", action="store_true",
                        help="Whether to use channel-specific processing")
    parser.add_argument("--channel_emb_dim", type=int, default=64,
                        help="Dimension of channel embeddings")
    parser.add_argument("--use_channel_conv_kernels", action="store_true",
                        help="Whether to use channel-specific convolution kernels")
    parser.add_argument("--use_cbam", action="store_true",
                        help="Whether to use CBAM attention")
    parser.add_argument("--cbam_reduction_ratio", type=int, default=16,
                        help="Reduction ratio for CBAM channel attention")
    parser.add_argument("--attention_resolutions", type=str, default="1,2",
                        help="Comma-separated list of resolutions at which to apply attention (1,2 for better feature integration)")

    # Feature matching loss arguments
    parser.add_argument("--use_feature_matching", action="store_true",
                        help="Whether to use feature matching loss with channel-specific weights")
    parser.add_argument("--feature_matching_weight", type=float, default=0.1,
                        help="Weight for feature matching loss (0.1 recommended for balance)")

    # Evaluation arguments
    parser.add_argument("--enable_auto_eval", action="store_true",
                        help="Whether to enable automatic evaluation during training")
    parser.add_argument("--eval_num_samples", type=int, default=995,
                        help="Number of samples to generate for evaluation (changed to match training set positive samples)")
    parser.add_argument("--eval_batch_size", type=int, default=50,
                        help="Batch size for evaluation")
    parser.add_argument("--inception_model_path", type=str, default=None,
                        help="Path to Inception model for FID calculation")

    # ECA attention arguments
    parser.add_argument("--use_eca", action="store_true",
                        help="Whether to use ECA attention in ResidualBlocks")
    parser.add_argument("--use_input_eca", action="store_true",
                        help="Whether to use ECA attention for input channel correlation")

    # PulsarAdaptiveSampler arguments
    parser.add_argument("--use_pulsar_adaptive", action="store_true", default=True,
                        help="Use PulsarAdaptiveSampler for evaluation (default: True)")
    parser.add_argument("--num_inference_steps", type=int, default=20,
                        help="Number of inference steps for PulsarAdaptiveSampler")
    parser.add_argument("--numerical_stability", action="store_true", default=True,
                        help="Enable numerical stability features in PulsarAdaptiveSampler")
    parser.add_argument("--pulsar_optimization", action="store_true", default=True,
                        help="Enable pulsar-specific optimizations in PulsarAdaptiveSampler")
    parser.add_argument("--channel_aware", action="store_true", default=True,
                        help="Enable channel-aware processing in PulsarAdaptiveSampler")

    return parser.parse_args()


def setup_output_dirs(args: argparse.Namespace) -> None:
    """Setup output directories and configure logging."""
    os.makedirs(args.output_dir, exist_ok=True)

    args.log_dir = os.path.join(args.output_dir, args.log_dir)
    args.checkpoint_dir = os.path.join(args.output_dir, args.checkpoint_dir)

    os.makedirs(args.log_dir, exist_ok=True)
    os.makedirs(args.checkpoint_dir, exist_ok=True)

    # Setup logging with file output
    log_file = os.path.join(args.log_dir, "training.log")

    # Remove all handlers associated with the root logger object
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Configure logging with both console and file output
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),  # Console handler
            logging.FileHandler(log_file)  # File handler
        ]
    )

    # Set module-specific log levels
    logging.getLogger('models.diffusion').setLevel(logging.ERROR)
    logging.getLogger('evaluation.metrics').setLevel(logging.INFO)

    logger.info(f"Logging to {log_file}")


def save_config(args: argparse.Namespace) -> None:
    """Save configuration to file."""
    config_path = os.path.join(args.output_dir, "config.yaml")
    with open(config_path, "w") as f:
        yaml.dump(vars(args), f)
    logger.info(f"Saved configuration to {config_path}")


def build_model(args: argparse.Namespace) -> GaussianDiffusion:
    """Build DDPM model."""
    # Parse channel multipliers
    channel_mults = tuple(map(int, args.channel_mults.split(",")))

    # Parse attention resolutions if provided
    if hasattr(args, 'attention_resolutions') and args.attention_resolutions:
        if isinstance(args.attention_resolutions, str):
            attention_resolutions = tuple(map(int, args.attention_resolutions.split(",")))
        else:
            attention_resolutions = args.attention_resolutions
    else:
        attention_resolutions = (1,)

    # Import parameter validation function
    from models.utils import validate_model_params

    # Create a dictionary of parameters for validation
    # 注意：small_sample_mode参数已弃用，不再包含在参数字典中
    model_params = {
        "img_channels": args.img_channels,
        "base_channels": args.base_channels,
        "channel_mults": channel_mults,
        "num_res_blocks": args.num_res_blocks,
        "time_emb_dim": args.time_emb_dim,
        "dropout": args.dropout,
        "norm": args.norm,  # 使用从命令行传递的归一化类型
        "num_groups": args.num_groups,
        "attention_resolutions": attention_resolutions,
    }

    # Validate and potentially adjust parameters
    validated_params = validate_model_params(model_params)

    # Print validation results if parameters were adjusted
    if validated_params != model_params:
        logger.info("Parameters were adjusted for compatibility:")
        for key in validated_params:
            if validated_params[key] != model_params.get(key):
                logger.info(f"  - {key}: {model_params.get(key)} -> {validated_params[key]}")

    # Create UNet model with validated parameters
    unet = UNet(
        img_channels=validated_params["img_channels"],
        base_channels=validated_params["base_channels"],
        channel_mults=validated_params["channel_mults"],
        num_res_blocks=validated_params["num_res_blocks"],
        time_emb_dim=validated_params["time_emb_dim"],
        dropout=validated_params["dropout"],
        norm=validated_params["norm"],  # 传递归一化类型
        num_groups=validated_params["num_groups"],
        attention_resolutions=validated_params["attention_resolutions"],
        # ECA attention parameters
        use_eca=getattr(args, 'use_eca', False),
        use_input_eca=getattr(args, 'use_input_eca', False),
    )

    # Parse channel weights for task 2
    channel_weights = None
    if hasattr(args, 'channel_weights') and args.channel_weights:
        channel_weights = [float(w.strip()) for w in args.channel_weights.split(',')]

    # Create diffusion model
    diffusion = GaussianDiffusion(
        model=unet,
        img_size=(args.img_size, args.img_size),
        img_channels=args.img_channels,
        betas=torch.linspace(1e-4, 0.02, args.num_timesteps),
        loss_type=args.loss_type,
        ema_decay=args.ema_decay,
        ema_start=args.ema_start,
        ema_update_rate=args.ema_update_rate,
        device=args.device,
        # Removed curriculum learning - replaced by intelligent data augmentation
        use_feature_matching=getattr(args, 'use_feature_matching', False),
        feature_matching_weight=getattr(args, 'feature_matching_weight', 0.1),
        # TASK 2: Pulsar-specific loss function parameters
        use_channel_weighted_loss=getattr(args, 'use_channel_weighted_loss', False),
        channel_weights=channel_weights,
        adaptive_channel_weights=getattr(args, 'adaptive_channel_weights', False),
    )

    # Curriculum learning configuration removed - using intelligent data augmentation instead

    return diffusion


def main() -> None:
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Setup output directories
    setup_output_dirs(args)

    # 已移除small_sample_mode相关配置加载逻辑

    # Save configuration
    save_config(args)

    # Set debug mode
    if args.debug:
        # Set debug level for main logger and most modules
        logger.setLevel(logging.DEBUG)
        logging.getLogger('train.trainer').setLevel(logging.DEBUG)
        logging.getLogger('data').setLevel(logging.DEBUG)

        # Keep diffusion model at ERROR level even in debug mode to avoid excessive warnings
        # This prevents the "Extreme values in pred_x_0" warnings from flooding the console
        logging.getLogger('models.diffusion').setLevel(logging.ERROR)

        logger.debug("Debug mode enabled")

    # Create dataloaders
    logger.info("Creating dataloaders...")
    train_dataloader = get_dataloader(
        root=args.data_dir,
        batch_size=args.batch_size,
        train=True,
        augment=True,
        num_workers=args.num_workers,
        shuffle=True,
        drop_last=True,
        resolution=args.img_size,
        # TASK 3: Channel-specific augmentation parameters
        use_channel_augmentation=getattr(args, 'use_channel_augmentation', False),
        augmentation_prob=getattr(args, 'augmentation_prob', 0.7),
        progressive_augmentation=getattr(args, 'progressive_augmentation', False),
        current_epoch=0,  # Will be updated during training
        total_epochs=args.max_epochs,
        progressive_intensity=getattr(args, 'progressive_intensity', False),
        min_intensity=getattr(args, 'min_intensity', 0.3),
        max_intensity=getattr(args, 'max_intensity', 1.0),
        enable_physical_validation=getattr(args, 'enable_physical_validation', False),
        peak_preservation_threshold=getattr(args, 'peak_preservation_threshold', 0.8),
        stripe_coherence_threshold=getattr(args, 'stripe_coherence_threshold', 0.7),
        detail_variance_threshold=getattr(args, 'detail_variance_threshold', 0.5),
    )

    # Build model
    logger.info("Building model...")
    diffusion = build_model(args)

    # Parse progressive training arguments
    if args.progressive_training:
        progressive_resolutions = list(map(int, args.progressive_resolutions.split(",")))
        progressive_epochs = list(map(int, args.progressive_epochs.split(",")))
    else:
        progressive_resolutions = None
        progressive_epochs = None

    # Curriculum learning logic removed - using intelligent data augmentation instead

    # Create PulsarAdaptiveSampler if enabled
    sampler = None
    if getattr(args, 'use_pulsar_adaptive', True):
        try:
            logger.info("Creating PulsarAdaptiveSampler for evaluation...")
            sampler = PulsarAdaptiveSampler(
                model=diffusion,
                num_timesteps=args.num_timesteps,
                device=args.device,
                numerical_stability=getattr(args, 'numerical_stability', True),
                pulsar_optimization=getattr(args, 'pulsar_optimization', True),
                channel_aware=getattr(args, 'channel_aware', True)
            )
            logger.info("PulsarAdaptiveSampler created successfully")
        except Exception as e:
            logger.warning(f"Failed to create PulsarAdaptiveSampler: {e}")
            logger.info("Falling back to model's built-in sampling method")
            sampler = None

    # Create trainer
    logger.info("Creating trainer...")
    trainer = Trainer(
        diffusion_model=diffusion,
        train_dataloader=train_dataloader,
        val_dataloader=None,
        lr=args.lr,
        weight_decay=args.weight_decay,
        scheduler_type=args.scheduler_type,
        warmup_epochs=args.warmup_epochs,
        max_epochs=args.max_epochs,
        min_lr=args.min_lr,
        lr_cycle_epochs=args.lr_cycle_epochs,
        lr_cycle_mult=args.lr_cycle_mult,
        lr_cycle_decay=args.lr_cycle_decay,
        # Cosine annealing restart parameters
        t_0=args.t_0,
        t_mult=args.t_mult,
        restart_decay=args.restart_decay,
        visualize_lr=args.visualize_lr,
        gradient_clip_val=args.gradient_clip_val,
        device=args.device,
        log_dir=args.log_dir,
        checkpoint_dir=args.checkpoint_dir,
        save_every=args.save_every,
        eval_every=args.eval_every,
        progressive_training=args.progressive_training,
        progressive_resolutions=progressive_resolutions,
        progressive_epochs=progressive_epochs,
        # Curriculum learning parameters removed
        # Evaluation parameters
        enable_auto_eval=args.enable_auto_eval,
        data_dir=args.data_dir,
        inception_model_path=args.inception_model_path,
        eval_num_samples=args.eval_num_samples,
        eval_batch_size=args.eval_batch_size,
        # PulsarAdaptiveSampler parameters
        external_sampler=sampler,
        num_inference_steps=getattr(args, 'num_inference_steps', 20),
    )

    # Resume from checkpoint if specified
    if args.resume:
        logger.info(f"Resuming from checkpoint {args.resume}...")
        trainer.load_checkpoint(args.resume)

    # Train model
    logger.info("Starting training...")
    trainer.train()

    logger.info("Training completed!")


if __name__ == "__main__":
    main()
