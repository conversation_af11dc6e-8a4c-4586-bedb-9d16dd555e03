#!/usr/bin/env python3
"""
WGAN-GP+VAE完整训练脚本
执行三阶段渐进式训练：VAE预训练→WGAN-GP集成→联合优化

使用方法:
python scripts/train_wgan_vae.py --epochs 150 --batch_size 16 --device cuda
python scripts/train_wgan_vae.py --stage1_epochs 50 --stage2_epochs 40 --stage3_epochs 60
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import argparse
import torch
import logging
from training.full_trainer import TrainingConfig, create_training_pipeline
from evaluation.fid_is_evaluator import evaluate_generation_quality
from evaluation.visualization import create_training_summary_report, visualize_samples
from utils.random_seed import set_random_seed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='WGAN-GP+VAE脉冲星生成模型训练')
    
    # 基础配置
    parser.add_argument('--latent_dim', type=int, default=64, help='潜在空间维度')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    parser.add_argument('--device', type=str, default='auto', help='设备 (auto/cuda/cpu)')
    parser.add_argument('--random_seed', type=int, default=42, help='随机种子')
    
    # 学习率配置
    parser.add_argument('--vae_lr', type=float, default=2e-4, help='VAE学习率')
    parser.add_argument('--discriminator_lr', type=float, default=4e-4, help='判别器学习率')
    
    # 训练轮数配置
    parser.add_argument('--stage1_epochs', type=int, default=50, help='阶段1轮数 (VAE预训练)')
    parser.add_argument('--stage2_epochs', type=int, default=40, help='阶段2轮数 (WGAN-GP集成)')
    parser.add_argument('--stage3_epochs', type=int, default=60, help='阶段3轮数 (联合优化)')
    parser.add_argument('--epochs', type=int, help='总轮数 (覆盖分阶段设置)')
    
    # 损失函数配置
    parser.add_argument('--beta_vae', type=float, default=1.0, help='β-VAE系数')
    parser.add_argument('--lambda_gp', type=float, default=10.0, help='梯度惩罚系数')
    parser.add_argument('--n_critic', type=int, default=5, help='判别器训练频率')
    
    # 数据配置
    parser.add_argument('--data_augment', action='store_true', default=True, help='数据增强')
    parser.add_argument('--augment_factor', type=int, default=2, help='增强倍数')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载工作进程数')
    
    # 保存配置
    parser.add_argument('--save_dir', type=str, default='./checkpoints_wgan_vae', help='保存目录')
    parser.add_argument('--save_interval', type=int, default=10, help='保存间隔')
    parser.add_argument('--log_interval', type=int, default=5, help='日志间隔')
    
    # 评估配置
    parser.add_argument('--eval_interval', type=int, default=20, help='评估间隔')
    parser.add_argument('--eval_samples', type=int, default=1000, help='评估样本数')
    
    # 可视化配置
    parser.add_argument('--visualize', action='store_true', default=True, help='生成可视化')
    parser.add_argument('--vis_samples', type=int, default=16, help='可视化样本数')
    
    return parser.parse_args()

def main():
    """主训练函数"""
    args = parse_arguments()
    
    logger.info("🚀 开始WGAN-GP+VAE脉冲星生成模型训练")
    logger.info("=" * 60)
    
    # 处理总轮数参数
    if args.epochs:
        # 如果指定了总轮数，平均分配到三个阶段
        total_epochs = args.epochs
        args.stage1_epochs = total_epochs // 3
        args.stage2_epochs = total_epochs // 3
        args.stage3_epochs = total_epochs - args.stage1_epochs - args.stage2_epochs
        logger.info(f"总轮数 {total_epochs} 分配为: {args.stage1_epochs}/{args.stage2_epochs}/{args.stage3_epochs}")
    
    # 创建训练配置
    config = TrainingConfig(
        latent_dim=args.latent_dim,
        batch_size=args.batch_size,
        device=args.device,
        random_seed=args.random_seed,
        vae_lr=args.vae_lr,
        discriminator_lr=args.discriminator_lr,
        stage1_epochs=args.stage1_epochs,
        stage2_epochs=args.stage2_epochs,
        stage3_epochs=args.stage3_epochs,
        beta_vae=args.beta_vae,
        lambda_gp=args.lambda_gp,
        n_critic=args.n_critic,
        data_augment=args.data_augment,
        augment_factor=args.augment_factor,
        num_workers=args.num_workers,
        save_dir=args.save_dir,
        save_interval=args.save_interval,
        log_interval=args.log_interval
    )
    
    # 打印配置
    logger.info("训练配置:")
    logger.info(f"  设备: {config.device}")
    logger.info(f"  批次大小: {config.batch_size}")
    logger.info(f"  潜在维度: {config.latent_dim}")
    logger.info(f"  训练轮数: {config.stage1_epochs + config.stage2_epochs + config.stage3_epochs}")
    logger.info(f"  学习率: VAE={config.vae_lr}, D={config.discriminator_lr}")
    logger.info(f"  保存目录: {config.save_dir}")
    
    try:
        # 创建训练管道
        pipeline = create_training_pipeline(config)
        
        # 执行训练
        training_history = pipeline.run_full_training()
        
        logger.info("✅ 训练完成！开始最终评估...")
        
        # 最终评估
        final_metrics = evaluate_generation_quality(
            pipeline.model, 
            pipeline.dataloader,
            pipeline.device,
            num_samples=args.eval_samples
        )
        
        logger.info("📊 最终评估结果:")
        for key, value in final_metrics.items():
            logger.info(f"  {key}: {value:.4f}")
        
        # 生成可视化
        if args.visualize:
            logger.info("🎨 生成可视化...")
            
            # 生成样本可视化
            with torch.no_grad():
                vis_samples = pipeline.model.sample(args.vis_samples, pipeline.device)
            
            vis_dir = os.path.join(config.save_dir, 'visualizations')
            os.makedirs(vis_dir, exist_ok=True)
            
            # 保存样本网格
            visualize_samples(
                vis_samples, 
                title="Final Generated Samples",
                save_path=os.path.join(vis_dir, 'final_samples.png')
            )
            
            # 创建训练总结报告
            create_training_summary_report(
                training_history, 
                final_metrics,
                vis_dir
            )
        
        # 检查目标达成
        logger.info("🎯 目标达成检查:")
        fid_success = final_metrics.get('fid', float('inf')) < 40
        is_success = final_metrics.get('is_mean', 0) > 5
        
        logger.info(f"  FID < 40: {'✅' if fid_success else '❌'} ({final_metrics.get('fid', 0):.2f})")
        logger.info(f"  IS > 5: {'✅' if is_success else '❌'} ({final_metrics.get('is_mean', 0):.2f})")
        
        if fid_success and is_success:
            logger.info("🎉 所有目标达成！训练成功完成！")
        else:
            logger.info("⚠️ 部分目标未达成，可能需要调整超参数或增加训练轮数")
        
    except KeyboardInterrupt:
        logger.info("⏹️ 训练被用户中断")
    except Exception as e:
        logger.error(f"❌ 训练过程中出现错误: {e}")
        raise
    
    logger.info("🏁 训练脚本执行完成")

if __name__ == "__main__":
    main()
