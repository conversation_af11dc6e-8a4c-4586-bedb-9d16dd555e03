#!/bin/bash

# PulsarDDPM优化训练脚本 - 基于性能诊断修复
# 解决FID=295, IS=1.09远低于目标FID<40, IS>5的问题

echo "🚀 启动PulsarDDPM优化训练 (基于性能诊断修复)"
echo "目标: FID<40, IS>5"
echo "修复: 训练不足+过拟合+损失不平衡+批次过大"

# 激活conda环境
source ~/anaconda3/etc/profile.d/conda.sh
conda activate wgan_env

# 设置CUDA环境
export CUDA_VISIBLE_DEVICES=0

# 进入项目目录
cd /Pulsar/PulsarFenlei/PulsarDDPM

# 优化训练参数 - 解决关键问题
python train_pulsar_ddpm_a100.py \
    --output_dir outputs_a100_fixed \
    --max_epochs 400 \
    --stage_epochs "150,150,100" \
    --stage_lr_factors "1.0,0.9,0.8" \
    --batch_size 16 \
    --lr 8e-5 \
    --warmup_epochs 5 \
    --dropout 0.2 \
    --base_channels 48 \
    --channel_mults "1,2,3" \
    --mse_weight 0.6 \
    --physics_weight 0.2 \
    --consistency_weight 0.2 \
    --spectral_weight 0.0 \
    --channel_diffusion_weight 0.0 \
    --eval_every 50 \
    --save_every 50 \
    --use_optimized_components \
    --enable_data_augmentation \
    --enable_loss_scaling \
    --enable_adaptive_weights \
    --mixed_precision \
    --compile_model \
    --seed 42

echo "训练完成！检查 outputs_a100_fixed/ 目录查看结果"
