#!/usr/bin/env python3
"""
最小化测试脚本
逐步测试每个组件以找出问题所在
"""

import torch
import numpy as np
import time

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    
    try:
        import sys
        import os
        sys.path.insert(0, os.getcwd())
        print("✅ 路径设置完成")
        
        from utils.random_seed import set_random_seed
        print("✅ random_seed导入成功")
        
        from utils.data_loader import create_htru1_dataloader
        print("✅ data_loader导入成功")
        
        from models.vae import PulsarVAE
        print("✅ VAE模型导入成功")
        
        from models.discriminator import PulsarWGANDiscriminator
        print("✅ 判别器模型导入成功")
        
        from models.wgan_vae import PulsarWGANVAE
        print("✅ WGAN-VAE模型导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n🔍 测试数据加载...")
    
    try:
        from utils.data_loader import create_htru1_dataloader
        
        # 创建最小数据加载器
        dataloader = create_htru1_dataloader(
            batch_size=2,
            augment=False,
            num_workers=0
        )
        print(f"✅ 数据加载器创建成功: {len(dataloader)} 批次")
        
        # 测试获取一个批次
        batch = next(iter(dataloader))
        print(f"✅ 批次获取成功: {batch.shape}")
        
        return True, batch
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_model_creation():
    """测试模型创建"""
    print("\n🔍 测试模型创建...")
    
    try:
        from models.wgan_vae import PulsarWGANVAE
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 创建模型
        model = PulsarWGANVAE(latent_dim=32).to(device)
        print(f"✅ 模型创建成功: {model.total_params/1e6:.2f}M 参数")
        
        return True, model, device
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_vae_forward():
    """测试VAE前向传播"""
    print("\n🔍 测试VAE前向传播...")
    
    try:
        success, model, device = test_model_creation()
        if not success:
            return False
        
        success, batch = test_data_loading()
        if not success:
            return False
        
        batch = batch.to(device)
        
        # VAE前向传播
        with torch.no_grad():
            x_recon, mu, logvar = model.vae(batch)
        
        print(f"✅ VAE前向传播成功:")
        print(f"  输入: {batch.shape}")
        print(f"  重建: {x_recon.shape}")
        print(f"  均值: {mu.shape}")
        print(f"  方差: {logvar.shape}")
        
        return True, model, batch, device
        
    except Exception as e:
        print(f"❌ VAE前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None

def test_discriminator_forward():
    """测试判别器前向传播"""
    print("\n🔍 测试判别器前向传播...")
    
    try:
        success, model, batch, device = test_vae_forward()
        if not success:
            return False
        
        # 判别器前向传播
        with torch.no_grad():
            real_score = model.discriminator(batch)
            fake_batch = model.vae(batch)[0]
            fake_score = model.discriminator(fake_batch)
        
        print(f"✅ 判别器前向传播成功:")
        print(f"  真实分数: {real_score.shape}, 均值: {real_score.mean().item():.4f}")
        print(f"  生成分数: {fake_score.shape}, 均值: {fake_score.mean().item():.4f}")
        
        return True, model, batch, device
        
    except Exception as e:
        print(f"❌ 判别器前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None

def test_loss_computation():
    """测试损失计算"""
    print("\n🔍 测试损失计算...")
    
    try:
        from losses.combined_loss import PulsarCombinedLoss
        
        success, model, batch, device = test_discriminator_forward()
        if not success:
            return False
        
        # 创建损失函数
        loss_fn = PulsarCombinedLoss(
            vae_weight=1.0,
            wgan_weight=0.0,
            physics_weight=0.0
        )
        print("✅ 损失函数创建成功")
        
        # VAE前向传播
        x_recon, mu, logvar = model.vae(batch)
        
        # 计算VAE损失
        vae_loss_dict = loss_fn.compute_vae_loss_component(x_recon, batch, mu, logvar)
        print(f"✅ VAE损失计算成功: {len(vae_loss_dict)} 项")
        
        # 计算判别器损失
        d_loss_dict = loss_fn.compute_discriminator_loss(
            model.discriminator, batch, x_recon.detach(), device
        )
        print(f"✅ 判别器损失计算成功: {len(d_loss_dict)} 项")
        
        return True
        
    except Exception as e:
        print(f"❌ 损失计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 WGAN-GP+VAE最小化测试")
    print("=" * 50)
    
    tests = [
        ("基础导入", test_basic_imports),
        ("数据加载", test_data_loading),
        ("模型创建", test_model_creation),
        ("VAE前向传播", test_vae_forward),
        ("判别器前向传播", test_discriminator_forward),
        ("损失计算", test_loss_computation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        start_time = time.time()
        try:
            if test_func():
                elapsed = time.time() - start_time
                print(f"✅ {test_name}通过 ({elapsed:.2f}s)")
                passed += 1
            else:
                print(f"❌ {test_name}失败")
                break  # 如果某个测试失败，停止后续测试
        except Exception as e:
            print(f"💥 {test_name}异常: {e}")
            break
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础组件正常工作！")
        return 0
    else:
        print(f"❌ 在'{tests[passed][0]}'阶段失败")
        return 1

if __name__ == "__main__":
    exit(main())
