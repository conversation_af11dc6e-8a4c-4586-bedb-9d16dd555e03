#!/usr/bin/env python3
"""
A100 GPU训练脚本 - SimplifiedCNNUNet
专为NVIDIA A100-SXM4-40GB优化的完整训练脚本
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import argparse
import logging
import yaml
import time
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from tqdm import tqdm

# 添加项目路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入SimplifiedCNNUNet相关模块
from models.simplified_cnn_unet import SimplifiedCNNUNet
from models.pulsar_adaptive_sampler import PulsarAdaptiveSampler
from train.optimized_loss import OptimizedPulsarLoss
from train.loss_integration_adapter import LossIntegrationAdapter, create_integrated_loss
from train.progressive_trainer import ProgressiveTrainer
from data.dataloader import get_dataloader
from data.integrated_data_pipeline import create_enhanced_dataloader
from evaluation.metrics import evaluate_model, generate_samples_for_evaluation

# 配置日志
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="A100 GPU训练 - SimplifiedCNNUNet")

    # 数据参数 (Phase 1优化)
    parser.add_argument("--data_dir", type=str,
                       default="/Pulsar/PulsarFenlei/data/htru1-batches-py",
                       help="HTRU1数据集路径")
    parser.add_argument("--batch_size", type=int, default=32,
                       help="批次大小 (Phase 1优化: 提高训练稳定性)")
    parser.add_argument("--num_workers", type=int, default=8,
                       help="数据加载器工作进程数")

    # 模型参数 (Phase 1优化)
    parser.add_argument("--img_channels", type=int, default=3,
                       help="图像通道数 (Period-DM, Phase-Subband, Phase-Subintegration)")
    parser.add_argument("--base_channels", type=int, default=66,
                       help="基础通道数 (Phase 1优化: 48→66, 能被3整除且为偶数)")
    parser.add_argument("--channel_mults", type=str, default="1,2,3,4",
                       help="通道倍数 (Phase 1优化: 增加第4层)")
    parser.add_argument("--num_res_blocks", type=int, default=2,
                       help="残差块数量")
    parser.add_argument("--transformer_dim", type=int, default=120,
                       help="Transformer维度 (Phase 1优化: 96→120, 能被6整除)")
    parser.add_argument("--num_heads", type=int, default=6,
                       help="注意力头数 (Phase 1优化: 4→6, 与transformer_dim匹配)")
    parser.add_argument("--num_timesteps", type=int, default=300,
                       help="扩散时间步数 (Phase 1优化: 150→300)")
    parser.add_argument("--dropout", type=float, default=0.05,
                       help="Dropout率 (Phase 1优化: 0.1→0.05)")

    # 训练参数 (学习率优化修复)
    parser.add_argument("--lr", type=float, default=5e-5,
                       help="学习率 (优化修复: 1e-5→5e-5, 适合4.04M参数模型)")
    parser.add_argument("--weight_decay", type=float, default=1e-5,
                       help="权重衰减")
    parser.add_argument("--max_epochs", type=int, default=400,
                       help="最大训练轮数")
    parser.add_argument("--warmup_epochs", type=int, default=5,
                       help="预热轮数 (优化修复: 10→5, 减少warmup占比)")
    parser.add_argument("--gradient_clip_val", type=float, default=0.5,
                       help="梯度裁剪值")

    # 三阶段训练参数 (学习率优化修复)
    parser.add_argument("--stage_epochs", type=str, default="150,150,100",
                       help="三阶段训练轮数 (basic,physics,fine_tune)")
    parser.add_argument("--stage_lr_factors", type=str, default="1.0,0.8,0.6",
                       help="三阶段学习率因子 (优化修复: 更温和的衰减避免学习停滞)")

    # 物理约束损失参数 (Phase 2紧急修复)
    parser.add_argument("--mse_weight", type=float, default=0.8,
                       help="MSE损失权重 (Phase 2修复: 0.7→0.8, 确保基础扩散稳定)")
    parser.add_argument("--consistency_weight", type=float, default=0.1,
                       help="通道一致性损失权重")
    parser.add_argument("--physics_weight", type=float, default=0.05,
                       help="物理特征损失权重 (Phase 2修复: 0.2→0.05, 避免损失暴增)")
    parser.add_argument("--spectral_weight", type=float, default=0.0,
                       help="频谱一致性损失权重 (Phase 2修复: 暂时禁用频谱损失避免数值爆炸)")
    parser.add_argument("--channel_diffusion_weight", type=float, default=0.02,
                       help="通道特定扩散损失权重 (Phase 2修复: 0.05→0.02, 降低通道扩散权重)")

    # PulsarAdaptiveSampler参数 (替代DPM-Solver++)
    parser.add_argument("--use_pulsar_adaptive", action="store_true", default=True,
                       help="使用PulsarAdaptiveSampler (默认启用，替代DPM-Solver++)")
    parser.add_argument("--num_inference_steps", type=int, default=20,
                       help="PulsarAdaptiveSampler推理步数")
    parser.add_argument("--numerical_stability", action="store_true", default=True,
                       help="启用数值稳定性保护")
    parser.add_argument("--pulsar_optimization", action="store_true", default=True,
                       help="启用脉冲星特定优化")
    parser.add_argument("--channel_aware", action="store_true", default=True,
                       help="启用通道感知处理")

    # DDIM采样参数 (向后兼容)
    parser.add_argument("--ddim_steps", type=int, default=100,
                       help="DDIM采样步数 (向后兼容)")
    parser.add_argument("--ddim_eta", type=float, default=0.0,
                       help="DDIM随机性参数 (向后兼容)")

    # 输出参数
    parser.add_argument("--output_dir", type=str, default="outputs_a100",
                       help="输出目录")
    parser.add_argument("--save_every", type=int, default=50,
                       help="保存检查点间隔")
    parser.add_argument("--eval_every", type=int, default=50,
                       help="评估间隔")
    parser.add_argument("--log_every", type=int, default=10,
                       help="日志记录间隔")

    # 评估参数 (HTRU1优化策略)
    parser.add_argument("--eval_num_samples", type=int, default=995,
                       help="评估样本数 (匹配HTRU1训练集正样本数量，确保FID/IS评估一致性)")
    parser.add_argument("--eval_batch_size", type=int, default=50,
                       help="评估批次大小")
    parser.add_argument("--inception_model_path", type=str,
                       default="evaluation/inception_v3_google-0cc3c7bd.pth",
                       help="Inception模型路径")

    # A100优化参数
    parser.add_argument("--mixed_precision", action="store_true", default=True,
                       help="启用混合精度训练")
    parser.add_argument("--compile_model", action="store_true", default=True,
                       help="启用模型编译 (PyTorch 2.0+)")
    parser.add_argument("--pin_memory", action="store_true", default=True,
                       help="启用内存固定")
    parser.add_argument("--persistent_workers", action="store_true", default=True,
                       help="启用持久化工作进程")

    # 设备参数
    parser.add_argument("--device", type=str, default="cuda",
                       help="训练设备")
    parser.add_argument("--seed", type=int, default=42,
                       help="随机种子")

    # 恢复训练
    parser.add_argument("--resume", type=str, default=None,
                       help="恢复训练的检查点路径")

    # Phase 3优化参数
    parser.add_argument("--use_optimized_components", action="store_true", default=False,
                       help="使用优化组件 (SimplifiedCNNUNet + OptimizedPulsarLoss + IntegratedDataPipeline)")
    parser.add_argument("--enable_data_augmentation", action="store_true", default=False,
                       help="启用天文数据增强")
    parser.add_argument("--enable_loss_scaling", action="store_true", default=False,
                       help="启用损失缩放")
    parser.add_argument("--enable_adaptive_weights", action="store_true", default=False,
                       help="启用自适应权重")

    return parser.parse_args()


def setup_logging(output_dir: str) -> str:
    """设置日志系统"""
    os.makedirs(output_dir, exist_ok=True)

    # 创建时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(output_dir, f"training_{timestamp}.log")

    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # 清除现有处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # 配置日志处理器
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file)
        ]
    )

    # 设置模块日志级别
    logging.getLogger('models').setLevel(logging.INFO)
    logging.getLogger('train').setLevel(logging.INFO)
    logging.getLogger('evaluation').setLevel(logging.INFO)

    logger.info(f"日志保存到: {log_file}")
    return log_file


def setup_device_and_seed(device: str, seed: int) -> torch.device:
    """设置设备和随机种子"""
    # 使用统一的随机种子管理工具
    try:
        from utils.seed_utils import set_seed_for_training
        actual_seed = set_seed_for_training(seed)
        logger.info(f"使用统一随机种子管理工具设置种子: {actual_seed}")
    except ImportError:
        logger.warning("无法导入seed_utils，使用基本的随机种子设置")
        # 基本的随机种子设置
        import random
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        np.random.seed(seed)
        random.seed(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
        logger.info(f"基本随机种子设置完成: {seed}")

    # 设置设备
    if device == "cuda" and torch.cuda.is_available():
        device = torch.device("cuda")

        # A100优化设置 (如果不是确定性模式)
        if not torch.backends.cudnn.deterministic:
            torch.backends.cudnn.benchmark = True
            logger.info("启用CUDNN基准模式以提高性能")
        else:
            logger.info("确定性模式已启用，禁用CUDNN基准模式以确保可重现性")

        # 显示GPU信息
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"使用GPU: {gpu_name} ({gpu_memory:.1f}GB)")

        # 启用TF32优化（A100及更新GPU的性能提升）
        if torch.cuda.get_device_capability()[0] >= 8:  # A100及更新GPU
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
            torch.set_float32_matmul_precision('high')
            logger.info("✅ 已启用TF32优化 (A100 GPU加速20-50%)")
        else:
            logger.info("ℹ️ 当前GPU不支持TF32优化")

    else:
        device = torch.device("cpu")
        logger.warning("使用CPU训练，性能可能较慢")

    logger.info(f"设备: {device}, 随机种子: {seed}")
    return device


def create_model(args, device: torch.device) -> SimplifiedCNNUNet:
    """创建SimplifiedCNNUNet模型"""
    logger.info("创建SimplifiedCNNUNet模型...")

    # 创建模型（SimplifiedCNNUNet不需要channel_mults参数）
    model = SimplifiedCNNUNet(
        in_channels=args.img_channels,
        out_channels=args.img_channels,
        base_channels=args.base_channels,
        time_emb_dim=args.transformer_dim,  # 复用transformer_dim作为time_emb_dim
        dropout=args.dropout
    )

    # 移动到设备
    model = model.to(device)

    # 计算模型参数量（兼容SimplifiedCNNUNet）
    total_params = sum(p.numel() for p in model.parameters())
    total_params_M = total_params / 1e6

    logger.info(f"模型参数量: {total_params:,} ({total_params_M:.2f}M)")

    # 显示SimplifiedCNNUNet架构信息
    logger.info(f"模型架构: SimplifiedCNNUNet")
    logger.info(f"  输入通道: {args.img_channels}")
    logger.info(f"  基础通道: {args.base_channels}")
    logger.info(f"  时间嵌入维度: {args.transformer_dim}")
    logger.info(f"  Dropout: {args.dropout}")

    # 显示可训练参数统计
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"可训练参数: {trainable_params:,} ({trainable_params/1e6:.2f}M)")

    if trainable_params != total_params:
        frozen_params = total_params - trainable_params
        logger.info(f"冻结参数: {frozen_params:,} ({frozen_params/1e6:.2f}M)")

    # 模型编译 (PyTorch 2.0+)
    if args.compile_model and hasattr(torch, 'compile'):
        try:
            model = torch.compile(model)
            logger.info("模型编译成功 (PyTorch 2.0+)")
        except Exception as e:
            logger.warning(f"模型编译失败: {e}")

    return model


def find_data_directory():
    """智能查找HTRU1数据集路径"""
    # 尝试多个可能的数据集路径
    possible_data_dirs = [
        "/Pulsar/PulsarFenlei/data/htru1-batches-py",
        "/yanyb/jmy/Pulsar/PulsarFenlei/data/htru1-batches-py",
        "../data/htru1-batches-py",
        "../../data/htru1-batches-py",
        "../../../data/htru1-batches-py",
        os.path.join(os.path.dirname(__file__), "..", "..", "data", "htru1-batches-py"),
        os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "htru1-batches-py")
    ]

    for data_dir in possible_data_dirs:
        # 转换为绝对路径
        abs_data_dir = os.path.abspath(data_dir)

        # 检查目录是否存在
        if os.path.exists(abs_data_dir):
            # 检查是否包含必要的数据文件
            required_files = ['data_batch_1', 'data_batch_2', 'data_batch_3', 'data_batch_4', 'data_batch_5']
            missing_files = []

            for file_name in required_files:
                file_path = os.path.join(abs_data_dir, file_name)
                if not os.path.exists(file_path):
                    missing_files.append(file_name)

            if not missing_files:
                logger.info(f"✅ 找到完整的HTRU1数据集: {abs_data_dir}")
                return abs_data_dir
            else:
                logger.warning(f"⚠️ 数据目录存在但缺少文件: {abs_data_dir}, 缺少: {missing_files}")
        else:
            logger.debug(f"❌ 数据目录不存在: {abs_data_dir}")

    # 如果都没找到，返回None
    logger.error("❌ 未找到有效的HTRU1数据集路径")
    logger.error("请确保数据集位于以下任一路径:")
    for data_dir in possible_data_dirs:
        logger.error(f"  - {os.path.abspath(data_dir)}")

    return None


def create_dataloader(args, device):
    """创建数据加载器"""
    logger.info("创建数据加载器...")

    # 智能查找数据路径
    if not os.path.exists(args.data_dir):
        logger.warning(f"指定的数据路径不存在: {args.data_dir}")
        logger.info("尝试自动查找HTRU1数据集...")

        auto_data_dir = find_data_directory()
        if auto_data_dir:
            logger.info(f"自动找到数据路径: {auto_data_dir}")
            args.data_dir = auto_data_dir
        else:
            raise FileNotFoundError(f"无法找到HTRU1数据集，请检查数据路径配置")
    else:
        logger.info(f"使用指定的数据路径: {args.data_dir}")

    # 创建原始训练数据加载器
    original_train_dataloader = get_dataloader(
        root=args.data_dir,
        batch_size=args.batch_size,
        train=True,
        augment=True,
        num_workers=args.num_workers,
        shuffle=True,
        drop_last=True,
        resolution=32  # HTRU1固定分辨率
    )

    # 根据优化参数决定是否使用增强版数据加载器
    if args.use_optimized_components or args.enable_data_augmentation:
        logger.info("🚀 集成数据处理优化组件...")
        train_dataloader = create_enhanced_dataloader(
            original_dataloader=original_train_dataloader,
            normalization_method='positive_sample_based',  # 基于正样本统计
            enable_augmentation=args.enable_data_augmentation,  # 根据参数启用天文数据增强
            augmentation_prob=0.7,  # 70%概率应用增强
            preserve_physics=True,  # 保持脉冲星物理特性
            device=device
        )
        logger.info("✅ 使用增强版数据处理管道")
    else:
        train_dataloader = original_train_dataloader
        logger.info("✅ 使用标准数据处理管道")

    logger.info(f"训练数据: {len(train_dataloader)} 批次, 批次大小: {args.batch_size}")
    logger.info(f"总样本数: {len(train_dataloader.dataset)}")

    return train_dataloader


def save_config(args, output_dir: str):
    """保存配置文件"""
    config_path = os.path.join(output_dir, "config.yaml")
    config_dict = vars(args).copy()

    # 转换不可序列化的对象
    for key, value in config_dict.items():
        if hasattr(value, '__dict__'):
            config_dict[key] = str(value)

    with open(config_path, 'w') as f:
        yaml.dump(config_dict, f, default_flow_style=False)

    logger.info(f"配置保存到: {config_path}")


def main():
    """主函数"""
    # 解析参数
    args = parse_args()

    # 设置输出目录和日志
    log_file = setup_logging(args.output_dir)

    # 保存配置
    save_config(args, args.output_dir)

    # 设置设备和随机种子
    device = setup_device_and_seed(args.device, args.seed)

    logger.info("=" * 80)
    logger.info("🚀 SimplifiedCNNUNet A100 GPU训练开始")
    logger.info("=" * 80)
    logger.info(f"输出目录: {args.output_dir}")
    logger.info(f"数据目录: {args.data_dir}")
    logger.info(f"最大训练轮数: {args.max_epochs}")
    logger.info(f"批次大小: {args.batch_size}")
    logger.info(f"混合精度: {args.mixed_precision}")

    try:
        # 创建模型
        model = create_model(args, device)

        # 创建数据加载器
        train_dataloader = create_dataloader(args, device)

        # 根据优化参数创建损失函数
        if args.use_optimized_components:
            logger.info("🚀 使用OptimizedPulsarLoss (203倍改善)")
            use_optimized = True
            enable_scaling = args.enable_loss_scaling
            enable_adaptive = args.enable_adaptive_weights
        else:
            logger.info("使用标准损失函数")
            use_optimized = False
            enable_scaling = False
            enable_adaptive = False

        loss_config = {
            'physics_loss_config': {
                'mse_weight': args.mse_weight,
                'consistency_weight': args.consistency_weight,
                'physics_weight': args.physics_weight,
                'spectral_weight': args.spectral_weight,
                'channel_diffusion_weight': args.channel_diffusion_weight
            },
            'use_optimized_loss': use_optimized,
            'enable_loss_scaling': enable_scaling,
            'enable_adaptive_weights': enable_adaptive,
            'num_timesteps': args.num_timesteps
        }

        loss_function = create_integrated_loss(loss_config, device)

        # 解析三阶段参数
        stage_epochs = list(map(int, args.stage_epochs.split(",")))
        stage_lr_factors = list(map(float, args.stage_lr_factors.split(",")))

        # 创建PulsarAdaptiveSampler (如果启用)
        sampler = None
        if args.use_pulsar_adaptive:
            sampler = PulsarAdaptiveSampler(
                model=model,
                num_timesteps=args.num_timesteps,
                device=device,
                numerical_stability=args.numerical_stability,
                pulsar_optimization=args.pulsar_optimization,
                channel_aware=args.channel_aware
            )
            logger.info("PulsarAdaptiveSampler创建成功，将替代DPM-Solver++")
        else:
            logger.info("使用传统DDIM采样方法")

        # 创建渐进式训练器
        trainer = ProgressiveTrainer(
            diffusion_model=model,
            train_dataloader=train_dataloader,
            stage_epochs=stage_epochs,
            stage_lr_factors=stage_lr_factors,
            lr=args.lr,
            weight_decay=args.weight_decay,
            warmup_epochs=args.warmup_epochs,
            gradient_clip_val=args.gradient_clip_val,
            device=device,
            log_dir=os.path.join(args.output_dir, "logs"),
            checkpoint_dir=os.path.join(args.output_dir, "checkpoints"),
            physics_loss_config={
                'mse_weight': args.mse_weight,
                'consistency_weight': args.consistency_weight,
                'physics_weight': args.physics_weight,
                'spectral_weight': args.spectral_weight,
                'channel_diffusion_weight': args.channel_diffusion_weight
            },
            # 显式传递评估参数
            eval_every=args.eval_every,
            save_every=args.save_every,
            enable_auto_eval=True,
            data_dir=args.data_dir,
            inception_model_path=args.inception_model_path,
            eval_num_samples=args.eval_num_samples,
            eval_batch_size=args.eval_batch_size,
            # PulsarAdaptiveSampler参数
            external_sampler=sampler,
            num_inference_steps=args.num_inference_steps
        )

        # 恢复训练
        if args.resume:
            logger.info(f"从检查点恢复训练: {args.resume}")
            trainer.load_checkpoint(args.resume)

        # 开始训练
        logger.info("开始三阶段渐进式训练...")
        trainer.train()

        logger.info("=" * 80)
        logger.info("🎉 训练完成！")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise

    finally:
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


if __name__ == "__main__":
    main()
