import os
import time
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
import sys
from typing import Optional, Tuple, List, Dict, Any, Union, Callable
from tqdm import tqdm

# Add project root to path to ensure imports work
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import scheduler with absolute import to avoid relative import issues
try:
    from train.scheduler import get_scheduler
except ImportError:
    # Fallback for when train module is not available as a package
    import importlib.util
    scheduler_path = os.path.join(os.path.dirname(__file__), "scheduler.py")
    if os.path.exists(scheduler_path):
        spec = importlib.util.spec_from_file_location("scheduler", scheduler_path)
        scheduler_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(scheduler_module)
        get_scheduler = scheduler_module.get_scheduler
    else:
        raise ImportError("Could not import scheduler module")

# Import evaluation modules
try:
    from evaluation.metrics import (
        evaluate_model,
        evaluate_channels,
        generate_samples_for_evaluation,
        DEFAULT_INCEPTION_MODEL_PATH,
        DEFAULT_NUM_SAMPLES
    )
    from data.dataloader import get_dataloader
except ImportError as e:
    logging.warning(f"Could not import evaluation modules: {e}")
    logging.warning("Automatic evaluation during training will be disabled")

# Configure logging
logger = logging.getLogger(__name__)


class Trainer:
    """
    Trainer for DDPM model.

    Args:
        diffusion_model (nn.Module): Diffusion model
        train_dataloader (torch.utils.data.DataLoader): Training dataloader
        val_dataloader (torch.utils.data.DataLoader, optional): Validation dataloader
        lr (float): Learning rate
        weight_decay (float): Weight decay
        scheduler_type (str): Type of learning rate scheduler
        warmup_epochs (int): Number of warmup epochs
        max_epochs (int): Maximum number of epochs
        gradient_clip_val (float): Gradient clipping value
        device (str): Device to use
        log_dir (str): Directory to save logs
        checkpoint_dir (str): Directory to save checkpoints
        save_every (int): Save checkpoint every n epochs
        eval_every (int): Evaluate every n epochs
        progressive_training (bool): Whether to use progressive training
        progressive_resolutions (List[int]): List of resolutions for progressive training
        progressive_epochs (List[int]): List of epochs for each resolution in progressive training
    """
    def __init__(
        self,
        diffusion_model: nn.Module,
        train_dataloader: torch.utils.data.DataLoader,
        val_dataloader: Optional[torch.utils.data.DataLoader] = None,
        lr: float = 5e-5,
        weight_decay: float = 0.0,
        scheduler_type: str = "warmup_cosine",
        warmup_epochs: int = 5,
        max_epochs: int = 200,
        min_lr: float = None,  # If None, will default to lr * 0.01
        lr_cycle_epochs: int = None,  # If None, will default to max_epochs // 3
        lr_cycle_mult: float = 1.0,
        lr_cycle_decay: float = 0.5,
        # Cosine annealing restart parameters
        t_0: int = 400,
        t_mult: float = 1.2,
        restart_decay: float = 0.8,
        visualize_lr: bool = False,
        gradient_clip_val: float = 0.5,
        device: str = "cuda",
        log_dir: str = "logs",
        checkpoint_dir: str = "checkpoints",
        save_every: int = 10,
        eval_every: int = 10,
        progressive_training: bool = False,
        progressive_resolutions: List[int] = [16, 32],
        progressive_epochs: List[int] = [100, 100],
        curriculum_learning: bool = False,
        curriculum_transitions: List[int] = [100, 200],
        adjust_lr_on_curriculum_change: bool = True,
        # Evaluation parameters
        enable_auto_eval: bool = True,
        data_dir: str = "/yanyb/jmy/Pulsar/PulsarFenlei/data/htru1-batches-py",
        inception_model_path: str = None,
        eval_num_samples: int = 995,
        eval_batch_size: int = 50,
        # PulsarAdaptiveSampler parameters
        external_sampler: Optional[Any] = None,
        num_inference_steps: int = 20,
    ):
        self.diffusion_model = diffusion_model
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader

        self.lr = lr
        self.weight_decay = weight_decay
        self.scheduler_type = scheduler_type
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.gradient_clip_val = gradient_clip_val

        # Learning rate scheduler parameters (优化修复: 提高最小学习率)
        self.min_lr = min_lr if min_lr is not None else lr * 0.2  # 从0.01改为0.2，避免学习停滞
        self.lr_cycle_epochs = lr_cycle_epochs
        self.lr_cycle_mult = lr_cycle_mult
        self.lr_cycle_decay = lr_cycle_decay
        # Cosine annealing restart parameters
        self.t_0 = t_0
        self.t_mult = t_mult
        self.restart_decay = restart_decay
        self.visualize_lr = visualize_lr

        self.device = device
        self.log_dir = log_dir
        self.checkpoint_dir = checkpoint_dir
        self.save_every = save_every
        self.eval_every = eval_every

        self.progressive_training = progressive_training
        self.progressive_resolutions = progressive_resolutions
        self.progressive_epochs = progressive_epochs

        # Curriculum learning settings
        self.curriculum_learning = curriculum_learning
        self.curriculum_transitions = curriculum_transitions
        self.adjust_lr_on_curriculum_change = adjust_lr_on_curriculum_change

        # Configure curriculum learning in diffusion model if supported
        if self.curriculum_learning and hasattr(self.diffusion_model, 'curriculum_learning'):
            # Check if we're using the new 6-stage curriculum learning
            if hasattr(self.diffusion_model, 'num_stages') and self.diffusion_model.num_stages == 6:
                # For 6-stage curriculum, we need 5 transition points
                if len(curriculum_transitions) == 2:
                    # Convert from 3-stage to 6-stage format
                    logger.info(f"Converting 3-stage transitions {curriculum_transitions} to 6-stage format")
                    # The diffusion model will handle the conversion
                else:
                    logger.info(f"Using 6-stage curriculum learning with transitions at epochs {curriculum_transitions}")
            else:
                logger.info(f"Using 3-stage curriculum learning with transitions at epochs {curriculum_transitions}")

            self.diffusion_model.curriculum_learning = True
            self.diffusion_model.set_curriculum_transitions(curriculum_transitions)
        elif self.curriculum_learning:
            logger.warning("Curriculum learning requested but diffusion model does not support it")
            self.curriculum_learning = False

        # Create directories
        os.makedirs(log_dir, exist_ok=True)
        os.makedirs(checkpoint_dir, exist_ok=True)

        # Setup optimizer and scheduler
        self.optimizer = optim.AdamW(
            self.diffusion_model.parameters(),
            lr=lr,
            weight_decay=weight_decay,
        )

        # Add curriculum transitions to optimizer param groups for visualization
        if self.curriculum_learning and curriculum_transitions:
            for param_group in self.optimizer.param_groups:
                param_group['curriculum_transitions'] = curriculum_transitions

                # Add number of stages for curriculum cosine scheduler
                if hasattr(self.diffusion_model, 'num_stages'):
                    param_group['num_stages'] = self.diffusion_model.num_stages
                else:
                    param_group['num_stages'] = 3  # Default to 3-stage curriculum

        # Determine curriculum stage factors based on number of stages
        curriculum_stage_factors = None
        if self.curriculum_learning:
            if hasattr(self.diffusion_model, 'num_stages') and self.diffusion_model.num_stages == 6:
                # 6-stage curriculum
                curriculum_stage_factors = [1.0, 0.8, 0.6, 0.4, 0.2, 0.1]
            else:
                # 3-stage curriculum
                curriculum_stage_factors = [1.0, 0.5, 0.25]

        # Determine cycle parameters for restart schedulers
        lr_cycle_epochs = getattr(self, 'lr_cycle_epochs', None)
        if lr_cycle_epochs is None:
            # Default to 1/3 of max epochs if not specified
            lr_cycle_epochs = max_epochs // 3

        # Get minimum learning rate (优化修复: 提高最小学习率)
        min_lr = getattr(self, 'min_lr', lr * 0.2)  # 从0.01改为0.2，避免学习停滞

        # Setup visualization path
        visualization_path = None
        if getattr(self, 'visualize_lr', False):
            visualization_path = os.path.join(self.log_dir, "lr_schedule.png")

        # Create scheduler
        self.scheduler = get_scheduler(
            scheduler_type=scheduler_type,
            optimizer=self.optimizer,
            warmup_epochs=warmup_epochs,
            max_epochs=max_epochs,
            min_lr=min_lr,
            curriculum_stage_factors=curriculum_stage_factors,
            visualize=getattr(self, 'visualize_lr', False),
            visualization_path=visualization_path,
            # Cosine annealing restart parameters
            t_0=getattr(self, 't_0', 400),
            t_mult=getattr(self, 't_mult', 1.2),
            restart_decay=getattr(self, 'restart_decay', 0.8),
        )

        # Move model to device
        self.diffusion_model.to(device)

        # Store initial learning rate for curriculum learning
        self.initial_lr = lr

        # Initialize training state
        self.current_epoch = 0
        self.global_step = 0
        self.best_val_loss = float("inf")
        self.curriculum_stage_changed = False

        # Evaluation parameters
        self.enable_auto_eval = enable_auto_eval
        self.data_dir = data_dir
        self.inception_model_path = inception_model_path or os.path.join(project_root, "evaluation/inception_v3_google-0cc3c7bd.pth")
        self.eval_num_samples = eval_num_samples
        self.eval_batch_size = eval_batch_size

        # PulsarAdaptiveSampler parameters
        self.external_sampler = external_sampler
        self.num_inference_steps = num_inference_steps

        # Early stopping parameters
        self.early_stopping_patience = 3  # Stop if no improvement for 3 consecutive evaluations
        self.early_stopping_min_delta = 0.01  # Minimum change to qualify as improvement
        self.early_stopping_counter = 0
        self.best_fid_score = float('inf')
        self.early_stopping_enabled = True

        # Check if evaluation modules are available
        self.evaluation_available = 'evaluate_model' in globals() and 'evaluate_channels' in globals() and 'generate_samples_for_evaluation' in globals()
        if self.enable_auto_eval and not self.evaluation_available:
            logger.warning("Automatic evaluation is enabled but evaluation modules are not available. Disabling automatic evaluation.")
            self.enable_auto_eval = False
            # Disable early stopping if evaluation is not available
            self.early_stopping_enabled = False

    def train_epoch(self, epoch: int, resolution: int = 32) -> Dict[str, float]:
        """
        Train for one epoch.

        Args:
            epoch (int): Current epoch
            resolution (int): Current resolution

        Returns:
            Dict[str, float]: Training metrics
        """
        self.diffusion_model.train()

        # Update curriculum stage if curriculum learning is enabled
        if self.curriculum_learning and hasattr(self.diffusion_model, 'update_curriculum_stage'):
            # Check if curriculum stage changed
            stage_changed = self.diffusion_model.update_curriculum_stage(epoch)

            # Get current curriculum stage and metrics
            current_stage = self.diffusion_model.curriculum_metrics['stage']
            num_stages = getattr(self.diffusion_model, 'num_stages', 3)  # Default to 3 for backward compatibility

            # Get transition state
            in_transition = self.diffusion_model.curriculum_metrics.get('in_transition', False)
            transition_progress = self.diffusion_model.curriculum_metrics.get('transition_progress', 0.0)
            stage_progress = self.diffusion_model.curriculum_metrics.get('stage_progress', 0.0)

            # Check if we're using curriculum cosine scheduler
            using_curriculum_scheduler = (
                self.scheduler_type == 'curriculum_cosine' and
                hasattr(self.scheduler, 'update_curriculum_stage')
            )

            # Adjust learning rate if curriculum stage changed and adjustment is enabled
            if self.adjust_lr_on_curriculum_change:
                if using_curriculum_scheduler:
                    # Update curriculum stage in the scheduler
                    self.scheduler.update_curriculum_stage(
                        stage=current_stage,
                        in_transition=in_transition,
                        transition_progress=transition_progress
                    )

                    # Log the update
                    if stage_changed:
                        self.curriculum_stage_changed = True
                        logger.info(f"Curriculum learning: Updated scheduler with stage {current_stage} "
                                   f"(progress: {stage_progress:.2f}, transition: {transition_progress:.2f})")

                elif stage_changed:
                    # Traditional approach for other scheduler types
                    self.curriculum_stage_changed = True

                    # Adjust learning rate based on stage and progress
                    if num_stages == 6:  # New 6-stage curriculum
                        # More gradual learning rate decay across 6 stages
                        # Stage 0: 100%, Stage 1: 80%, Stage 2: 60%, Stage 3: 40%, Stage 4: 20%, Stage 5: 10%
                        if current_stage < 5:
                            stage_lr_factor = 1.0 - (current_stage * 0.2)
                        else:
                            stage_lr_factor = 0.1  # Minimum 10% of initial LR
                    else:  # Original 3-stage curriculum
                        # Stage 0: 100%, Stage 1: 50%, Stage 2: 25%
                        stage_lr_factor = 1.0 / (2 ** current_stage)

                    # Apply smooth transition if in transition window
                    if in_transition:
                        # Calculate next stage factor
                        next_stage = min(current_stage + 1, num_stages - 1)

                        if num_stages == 6:
                            if next_stage < 5:
                                next_stage_factor = 1.0 - (next_stage * 0.2)
                            else:
                                next_stage_factor = 0.1
                        else:
                            next_stage_factor = 1.0 / (2 ** next_stage)

                        # Interpolate between current and next stage factors
                        stage_lr_factor = stage_lr_factor * (1 - transition_progress) + next_stage_factor * transition_progress

                    # Calculate new learning rate
                    new_lr = self.initial_lr * stage_lr_factor

                    logger.info(f"Curriculum learning: Adjusting learning rate for stage {current_stage} "
                               f"(progress: {stage_progress:.2f}) to {new_lr:.6f}")

                    # Update learning rate in optimizer
                    for param_group in self.optimizer.param_groups:
                        param_group['lr'] = new_lr

        epoch_loss = 0.0
        num_batches = len(self.train_dataloader)

        # Add curriculum stage to progress bar description if curriculum learning is enabled
        if self.curriculum_learning and hasattr(self.diffusion_model, 'curriculum_stage'):
            progress_desc = f"Epoch {epoch}/{self.max_epochs} (C-Stage {self.diffusion_model.curriculum_stage})"
        else:
            progress_desc = f"Epoch {epoch}/{self.max_epochs}"

        progress_bar = tqdm(self.train_dataloader, desc=progress_desc)

        for batch_idx, (x, _) in enumerate(progress_bar):
            # Move data to device
            x = x.to(self.device)

            # Zero gradients
            self.optimizer.zero_grad()

            # Forward pass
            loss = self.diffusion_model(x)

            # Backward pass
            loss.backward()

            # Gradient clipping
            if self.gradient_clip_val > 0:
                torch.nn.utils.clip_grad_norm_(self.diffusion_model.parameters(), self.gradient_clip_val)

            # Update weights
            self.optimizer.step()

            # Update learning rate (correct order: optimizer.step() before scheduler.step())
            self.scheduler.step()

            # Update EMA
            self.diffusion_model.update_ema()

            # Update metrics
            epoch_loss += loss.item()

            # Update progress bar
            progress_bar.set_postfix({
                "loss": loss.item(),
                "lr": self.scheduler.get_last_lr()[0],
                "resolution": resolution,
            })

            # Display progress in format: [batch_idx/num_batches] Epoch epoch/max_epochs lr:lr loss:loss
            # Only log every 50 batches to reduce console output, or at the beginning and end of each epoch
            if batch_idx % 50 == 0 or batch_idx == 0 or batch_idx == num_batches - 1:
                logger.info(f"[{batch_idx}/{num_batches}] Epoch {epoch}/{self.max_epochs} lr:{self.scheduler.get_last_lr()[0]:.6f} loss:{loss.item():.6f}")

            # Update global step
            self.global_step += 1

        # Compute average loss
        epoch_loss /= num_batches

        return {"loss": epoch_loss}

    def validate(self, resolution: int = 32) -> Dict[str, float]:
        """
        Validate the model.

        Args:
            resolution (int): Current resolution

        Returns:
            Dict[str, float]: Validation metrics
        """
        if self.val_dataloader is None:
            return {"val_loss": 0.0}

        self.diffusion_model.eval()

        val_loss = 0.0
        num_batches = len(self.val_dataloader)

        with torch.no_grad():
            for x, _ in self.val_dataloader:
                # Move data to device
                x = x.to(self.device)

                # Forward pass
                loss = self.diffusion_model(x)

                # Update metrics
                val_loss += loss.item()

        # Compute average loss
        val_loss /= num_batches

        return {"val_loss": val_loss}

    def check_early_stopping(self, eval_metrics: Dict[str, Any], epoch: int) -> bool:
        """
        Check if early stopping criteria are met.

        Args:
            eval_metrics (Dict[str, Any]): Evaluation metrics from current epoch
            epoch (int): Current epoch

        Returns:
            bool: True if training should stop early, False otherwise
        """
        if not self.early_stopping_enabled or not eval_metrics:
            return False

        # Get current FID score
        current_fid = eval_metrics.get('fid', float('inf'))

        # Skip early stopping check if FID is invalid
        if current_fid == float('inf') or current_fid != current_fid:  # NaN check
            logger.warning(f"Invalid FID score at epoch {epoch}: {current_fid}, skipping early stopping check")
            return False

        # Check if this is an improvement
        improvement = self.best_fid_score - current_fid
        is_improvement = improvement > self.early_stopping_min_delta

        if is_improvement:
            # Reset counter and update best score
            self.early_stopping_counter = 0
            self.best_fid_score = current_fid
            logger.info(f"Early stopping: New best FID score {current_fid:.4f} at epoch {epoch} "
                       f"(improvement: {improvement:.4f})")
        else:
            # Increment counter
            self.early_stopping_counter += 1
            logger.info(f"Early stopping: No improvement at epoch {epoch} "
                       f"(current FID: {current_fid:.4f}, best: {self.best_fid_score:.4f}, "
                       f"counter: {self.early_stopping_counter}/{self.early_stopping_patience})")

        # Check if we should stop
        should_stop = self.early_stopping_counter >= self.early_stopping_patience

        if should_stop:
            logger.info("=" * 60)
            logger.info("EARLY STOPPING TRIGGERED")
            logger.info("=" * 60)
            logger.info(f"No improvement in FID score for {self.early_stopping_patience} consecutive evaluations")
            logger.info(f"Best FID score: {self.best_fid_score:.4f}")
            logger.info(f"Current FID score: {current_fid:.4f}")
            logger.info(f"Training stopped at epoch {epoch}")
            logger.info("=" * 60)

        return should_stop

    def evaluate(self, epoch: int) -> Dict[str, Any]:
        """
        Evaluate the model using FID and IS metrics.

        This method performs a complete evaluation of the model by:
        1. Generating samples using the model
        2. Loading real images from the dataset
        3. Calculating FID and IS metrics for the generated samples
        4. Calculating channel-specific metrics

        Args:
            epoch (int): Current epoch

        Returns:
            Dict[str, Any]: Evaluation metrics including FID and IS
        """
        if not self.enable_auto_eval or not self.evaluation_available:
            logger.warning("Automatic evaluation is disabled or evaluation modules are not available")
            return {}

        logger.info(f"Evaluating model at epoch {epoch}...")
        start_time = time.time()

        # Set up evaluation-specific log file
        eval_log_dir = os.path.join(self.log_dir, "eval")
        os.makedirs(eval_log_dir, exist_ok=True)
        eval_log_file = os.path.join(eval_log_dir, f"eval_epoch_{epoch}.log")

        # Create file handler for evaluation logs
        file_handler = logging.FileHandler(eval_log_file)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        file_handler.setLevel(logging.INFO)

        # Add handler only to evaluation-specific logger to avoid duplication
        # (root logger already has console handler, we only need file handler for evaluation.metrics)
        metrics_logger = logging.getLogger('evaluation.metrics')
        metrics_logger.addHandler(file_handler)

        # Ensure evaluation logger doesn't propagate to root to avoid duplication
        metrics_logger.propagate = False

        logger.info(f"Evaluation logs will be saved to {eval_log_file}")

        try:
            # Set model to eval mode
            self.diffusion_model.eval()

            # Generate samples
            logger.info(f"Generating {self.eval_num_samples} samples for evaluation...")
            if self.external_sampler is not None:
                logger.info("Using PulsarAdaptiveSampler for evaluation")
                generated_images = generate_samples_for_evaluation(
                    model=self.diffusion_model,
                    num_samples=self.eval_num_samples,
                    batch_size=self.eval_batch_size,
                    device=self.device,
                    sampler=self.external_sampler,
                    num_inference_steps=self.num_inference_steps
                )
            else:
                logger.info("Using model's built-in sampling method")
                generated_images = generate_samples_for_evaluation(
                    model=self.diffusion_model,
                    num_samples=self.eval_num_samples,
                    batch_size=self.eval_batch_size,
                    device=self.device
                )

            if generated_images is None or len(generated_images) == 0:
                logger.error("Failed to generate images for evaluation")
                return {}

            logger.info(f"Generated {len(generated_images)} images with shape {generated_images.shape}")

            # Save a subset of generated samples for inspection
            try:
                output_dir = os.path.join(self.checkpoint_dir, f"eval_epoch_{epoch}")
                os.makedirs(output_dir, exist_ok=True)

                # Save at most 100 samples to avoid large files
                sample_path = os.path.join(output_dir, "generated_samples.pt")
                save_samples = generated_images[:min(100, len(generated_images))].cpu()
                torch.save(save_samples, sample_path)
                logger.info(f"Saved {len(save_samples)} samples to {sample_path}")
            except Exception as e:
                logger.warning(f"Error saving generated samples: {e}")
                # Continue with evaluation even if saving fails

            # Load real images
            logger.info("Loading real images for evaluation...")
            try:
                # Load training data for FID evaluation (changed from test data for consistency)
                train_dataloader = get_dataloader(
                    root=self.data_dir,
                    batch_size=self.eval_batch_size,
                    train=True,
                    augment=False,
                    num_workers=4,
                    shuffle=False,
                    drop_last=False
                )

                # Get real images
                real_images = []
                for x, _ in train_dataloader:
                    real_images.append(x)
                real_images = torch.cat(real_images, dim=0).to(self.device)

                logger.info(f"Loaded {len(real_images)} real images with shape {real_images.shape}")
            except Exception as e:
                logger.error(f"Error loading real images: {e}")
                return {}

            # Evaluate model
            logger.info("Calculating overall FID and IS metrics...")
            try:
                metrics = evaluate_model(
                    real_images=real_images,
                    generated_images=generated_images,
                    inception_model_path=self.inception_model_path,
                    batch_size=self.eval_batch_size,
                    device=self.device,
                    num_samples=self.eval_num_samples
                )

                if metrics is None:
                    logger.error("Failed to compute evaluation metrics")
                    return {}

                logger.info(f"Overall evaluation completed: FID={metrics['fid']:.4f}, IS={metrics['is_mean']:.4f}±{metrics['is_std']:.4f}")
            except Exception as e:
                logger.error(f"Error during model evaluation: {e}")
                return {}

            # Evaluate channels
            logger.info("Calculating channel-specific metrics...")
            try:
                channel_results = evaluate_channels(
                    real_images=real_images,
                    generated_images=generated_images,
                    inception_model_path=self.inception_model_path,
                    batch_size=self.eval_batch_size,
                    device=self.device,
                    num_samples=self.eval_num_samples
                )

                if channel_results is None:
                    logger.error("Failed to compute channel-specific metrics")
                    return metrics  # Return overall metrics even if channel evaluation fails

                logger.info("Channel-specific evaluation completed")

                # Add channel results to metrics
                metrics.update(channel_results)
            except Exception as e:
                logger.error(f"Error during channel evaluation: {e}")
                # Return overall metrics even if channel evaluation fails
                return metrics

            # Print results
            logger.info("=" * 50)
            logger.info(f"EVALUATION RESULTS (Epoch {epoch}):")
            logger.info("=" * 50)
            logger.info(f"Overall FID: {metrics['fid']:.4f}")
            logger.info(f"Overall IS: {metrics['is_mean']:.4f} ± {metrics['is_std']:.4f}")
            logger.info("-" * 50)

            # Print channel-specific results
            for i in range(3):
                channel_key = f"channel_{i}"
                if channel_key in metrics:
                    channel_name = metrics[channel_key]["name"]
                    fid = metrics[channel_key]["fid"]
                    is_mean = metrics[channel_key]["is_mean"]
                    is_std = metrics[channel_key]["is_std"]
                    logger.info(f"{channel_name}:")
                    logger.info(f"  FID = {fid:.4f}")
                    logger.info(f"  IS  = {is_mean:.4f} ± {is_std:.4f}")
                    logger.info("-" * 50)

            logger.info(f"Evaluation completed in {time.time() - start_time:.2f} seconds")

            # Save evaluation results to a separate JSON file
            try:
                import json
                results_file = os.path.join(eval_log_dir, f"results_epoch_{epoch}.json")
                with open(results_file, 'w') as f:
                    json.dump(metrics, f, indent=2)
                logger.info(f"Saved evaluation results to {results_file}")
            except Exception as e:
                logger.warning(f"Failed to save evaluation results to JSON: {e}")

            # Remove the file handler to avoid duplicate logs
            metrics_logger = logging.getLogger('evaluation.metrics')

            for handler in list(metrics_logger.handlers):
                if isinstance(handler, logging.FileHandler) and handler.baseFilename.endswith(f"eval_epoch_{epoch}.log"):
                    metrics_logger.removeHandler(handler)
                    handler.close()
                    logger.info(f"Removed evaluation log handler")

            return metrics

        except Exception as e:
            logger.error(f"Unexpected error during evaluation: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Remove the file handler even if evaluation fails
            metrics_logger = logging.getLogger('evaluation.metrics')

            for handler in list(metrics_logger.handlers):
                if isinstance(handler, logging.FileHandler) and handler.baseFilename.endswith(f"eval_epoch_{epoch}.log"):
                    metrics_logger.removeHandler(handler)
                    handler.close()

            return {}

    def save_checkpoint(self, epoch: int, metrics: Dict[str, float], is_best: bool = False) -> None:
        """
        Save checkpoint.

        Args:
            epoch (int): Current epoch
            metrics (Dict[str, float]): Metrics to save
            is_best (bool): Whether this is the best checkpoint
        """
        # Create checkpoint dictionary
        checkpoint = {
            "epoch": epoch,
            "global_step": self.global_step,
            "model_state_dict": self.diffusion_model.state_dict(),
            "ema_model_state_dict": self.diffusion_model.ema_model.state_dict() if self.diffusion_model.ema_model is not None else None,
            "optimizer_state_dict": self.optimizer.state_dict(),
            "scheduler_state_dict": self.scheduler.state_dict(),
            "metrics": metrics,
        }

        # Add evaluation metrics if available
        if "fid" in metrics or "is_mean" in metrics:
            checkpoint["evaluation_metrics"] = {
                "fid": metrics.get("fid", float("nan")),
                "is_mean": metrics.get("is_mean", float("nan")),
                "is_std": metrics.get("is_std", float("nan")),
            }

            # Add channel-specific metrics if available
            for i in range(3):
                channel_key = f"channel_{i}"
                if f"{channel_key}_fid" in metrics:
                    checkpoint["evaluation_metrics"][channel_key] = {
                        "fid": metrics.get(f"{channel_key}_fid", float("nan")),
                        "is_mean": metrics.get(f"{channel_key}_is_mean", float("nan")),
                        "is_std": metrics.get(f"{channel_key}_is_std", float("nan")),
                    }

        # Save regular checkpoint
        checkpoint_path = os.path.join(self.checkpoint_dir, f"checkpoint_epoch_{epoch}.pt")
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Saved checkpoint to {checkpoint_path}")

        # Save best checkpoint
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, "best_checkpoint.pt")
            torch.save(checkpoint, best_path)
            logger.info(f"Saved best checkpoint to {best_path}")

        # Save latest checkpoint
        latest_path = os.path.join(self.checkpoint_dir, "latest_checkpoint.pt")
        torch.save(checkpoint, latest_path)

    def load_checkpoint(self, checkpoint_path: str) -> None:
        """
        Load checkpoint.

        Args:
            checkpoint_path (str): Path to checkpoint
        """
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.diffusion_model.load_state_dict(checkpoint["model_state_dict"])

        # Load EMA model state with safety check
        if "ema_model_state_dict" in checkpoint and checkpoint["ema_model_state_dict"] is not None:
            if hasattr(self.diffusion_model, 'ema_model') and self.diffusion_model.ema_model is not None:
                self.diffusion_model.ema_model.load_state_dict(checkpoint["ema_model_state_dict"])
            else:
                logger.warning("EMA model state dict found in checkpoint but EMA model is not initialized")

        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.scheduler.load_state_dict(checkpoint["scheduler_state_dict"])

        self.current_epoch = checkpoint["epoch"]
        self.global_step = checkpoint["global_step"]

        logger.info(f"Loaded checkpoint from {checkpoint_path}")

    def train(self) -> Dict[str, List[float]]:
        """
        Train the model.

        Returns:
            Dict[str, List[float]]: Training history
        """
        history = {
            "train_loss": [],
            "val_loss": [],
            "lr": [],
        }

        # Add curriculum learning metrics to history if enabled
        if self.curriculum_learning:
            history["curriculum_stage"] = []
            history["curriculum_timestep_range"] = []

        # Add evaluation metrics to history if enabled
        if self.enable_auto_eval and self.evaluation_available:
            history["fid"] = []
            history["is_mean"] = []
            history["is_std"] = []
            # Add channel-specific metrics
            for i in range(3):
                channel_key = f"channel_{i}"
                history[f"{channel_key}_fid"] = []
                history[f"{channel_key}_is_mean"] = []
                history[f"{channel_key}_is_std"] = []

        start_time = time.time()

        if self.progressive_training:
            assert len(self.progressive_resolutions) == len(self.progressive_epochs), "Number of resolutions must match number of epochs"

            # Progressive training
            current_epoch = 0
            for resolution, num_epochs in zip(self.progressive_resolutions, self.progressive_epochs):
                logger.info(f"Starting progressive training with resolution {resolution}x{resolution} for {num_epochs} epochs")

                for epoch in range(current_epoch, current_epoch + num_epochs):
                    # Train for one epoch
                    train_metrics = self.train_epoch(epoch, resolution)

                    # Validate
                    if self.val_dataloader is not None and epoch % self.eval_every == 0:
                        val_metrics = self.validate(resolution)
                    else:
                        val_metrics = {"val_loss": 0.0}

                    # Evaluate with FID and IS metrics if enabled
                    eval_metrics = {}
                    # Evaluate if: 1) it's a regular evaluation interval, or 2) it's the last epoch of this resolution
                    is_last_epoch = epoch == current_epoch + num_epochs - 1
                    if self.enable_auto_eval and self.evaluation_available and epoch > 0 and (epoch % self.eval_every == 0 or is_last_epoch):
                        if is_last_epoch:
                            logger.info(f"Running resolution final evaluation (epoch {epoch}, resolution {resolution}x{resolution})...")
                        else:
                            logger.info(f"Running scheduled evaluation at epoch {epoch} (eval_every={self.eval_every})...")
                        eval_metrics = self.evaluate(epoch)

                        # Log evaluation metrics
                        if eval_metrics:
                            logger.info(f"Evaluation metrics at epoch {epoch}:")
                            logger.info(f"  FID: {eval_metrics.get('fid', 'N/A')}")
                            logger.info(f"  IS: {eval_metrics.get('is_mean', 'N/A')} ± {eval_metrics.get('is_std', 'N/A')}")

                    # Update history
                    history["train_loss"].append(train_metrics["loss"])
                    history["val_loss"].append(val_metrics["val_loss"])
                    history["lr"].append(self.scheduler.get_last_lr()[0])

                    # Update evaluation metrics in history if available
                    if eval_metrics and self.enable_auto_eval and self.evaluation_available:
                        # Update overall metrics
                        history["fid"].append(eval_metrics.get("fid", float("nan")))
                        history["is_mean"].append(eval_metrics.get("is_mean", float("nan")))
                        history["is_std"].append(eval_metrics.get("is_std", float("nan")))

                        # Update channel-specific metrics
                        for i in range(3):
                            channel_key = f"channel_{i}"
                            if channel_key in eval_metrics:
                                history[f"{channel_key}_fid"].append(eval_metrics[channel_key].get("fid", float("nan")))
                                history[f"{channel_key}_is_mean"].append(eval_metrics[channel_key].get("is_mean", float("nan")))
                                history[f"{channel_key}_is_std"].append(eval_metrics[channel_key].get("is_std", float("nan")))
                            else:
                                history[f"{channel_key}_fid"].append(float("nan"))
                                history[f"{channel_key}_is_mean"].append(float("nan"))
                                history[f"{channel_key}_is_std"].append(float("nan"))
                    elif self.enable_auto_eval and self.evaluation_available:
                        # Add placeholder values when not evaluating
                        history["fid"].append(float("nan"))
                        history["is_mean"].append(float("nan"))
                        history["is_std"].append(float("nan"))

                        for i in range(3):
                            channel_key = f"channel_{i}"
                            history[f"{channel_key}_fid"].append(float("nan"))
                            history[f"{channel_key}_is_mean"].append(float("nan"))
                            history[f"{channel_key}_is_std"].append(float("nan"))

                    # Update curriculum learning metrics if enabled
                    if self.curriculum_learning and hasattr(self.diffusion_model, 'get_curriculum_metrics'):
                        curriculum_metrics = self.diffusion_model.get_curriculum_metrics()
                        history["curriculum_stage"].append(curriculum_metrics["stage"])
                        history["curriculum_timestep_range"].append(curriculum_metrics["timestep_range"])

                        # Log curriculum learning metrics
                        if epoch % 10 == 0:
                            logger.info(f"Curriculum learning metrics: stage={curriculum_metrics['stage']}, "
                                       f"timestep_range={curriculum_metrics['timestep_range']}, "
                                       f"recent_loss={curriculum_metrics['recent_loss']:.6f}")

                    # Save checkpoint
                    # Only save if epoch > 0 or if explicitly configured to save at epoch 0
                    if epoch > 0 and epoch % self.save_every == 0:
                        is_best = val_metrics["val_loss"] < self.best_val_loss
                        if is_best:
                            self.best_val_loss = val_metrics["val_loss"]

                        self.save_checkpoint(epoch, {**train_metrics, **val_metrics}, is_best)
                    elif epoch == 0:
                        # For epoch 0, only save if it's explicitly requested via save_every
                        if self.save_every == 0:
                            logger.info("Saving initial model state (epoch 0)...")
                            is_best = val_metrics["val_loss"] < self.best_val_loss
                            if is_best:
                                self.best_val_loss = val_metrics["val_loss"]
                            self.save_checkpoint(epoch, {**train_metrics, **val_metrics}, is_best)

                    # Update current epoch
                    self.current_epoch = epoch + 1

                current_epoch += num_epochs
        else:
            # Regular training
            for epoch in range(self.current_epoch, self.max_epochs):
                # Train for one epoch
                train_metrics = self.train_epoch(epoch)

                # Validate
                if self.val_dataloader is not None and epoch % self.eval_every == 0:
                    val_metrics = self.validate()
                else:
                    val_metrics = {"val_loss": 0.0}

                # Evaluate with FID and IS metrics if enabled
                eval_metrics = {}
                # Evaluate if: 1) it's a regular evaluation interval, or 2) it's the last epoch
                if self.enable_auto_eval and self.evaluation_available and epoch > 0 and (epoch % self.eval_every == 0 or epoch == self.max_epochs - 1):
                    if epoch == self.max_epochs - 1:
                        logger.info(f"Running final epoch evaluation (epoch {epoch}, max_epochs={self.max_epochs})...")
                    else:
                        logger.info(f"Running scheduled evaluation at epoch {epoch} (eval_every={self.eval_every})...")
                    eval_metrics = self.evaluate(epoch)

                    # Log evaluation metrics
                    if eval_metrics:
                        logger.info(f"Evaluation metrics at epoch {epoch}:")
                        logger.info(f"  FID: {eval_metrics.get('fid', 'N/A')}")
                        logger.info(f"  IS: {eval_metrics.get('is_mean', 'N/A')} ± {eval_metrics.get('is_std', 'N/A')}")

                        # Check early stopping after evaluation
                        if self.check_early_stopping(eval_metrics, epoch):
                            logger.info("Early stopping triggered, breaking training loop")
                            # Update current epoch before breaking
                            self.current_epoch = epoch + 1
                            break

                # Update history
                history["train_loss"].append(train_metrics["loss"])
                history["val_loss"].append(val_metrics["val_loss"])
                history["lr"].append(self.scheduler.get_last_lr()[0])

                # Update evaluation metrics in history if available
                if eval_metrics and self.enable_auto_eval and self.evaluation_available:
                    # Update overall metrics
                    history["fid"].append(eval_metrics.get("fid", float("nan")))
                    history["is_mean"].append(eval_metrics.get("is_mean", float("nan")))
                    history["is_std"].append(eval_metrics.get("is_std", float("nan")))

                    # Update channel-specific metrics
                    for i in range(3):
                        channel_key = f"channel_{i}"
                        if channel_key in eval_metrics:
                            history[f"{channel_key}_fid"].append(eval_metrics[channel_key].get("fid", float("nan")))
                            history[f"{channel_key}_is_mean"].append(eval_metrics[channel_key].get("is_mean", float("nan")))
                            history[f"{channel_key}_is_std"].append(eval_metrics[channel_key].get("is_std", float("nan")))
                        else:
                            history[f"{channel_key}_fid"].append(float("nan"))
                            history[f"{channel_key}_is_mean"].append(float("nan"))
                            history[f"{channel_key}_is_std"].append(float("nan"))
                elif self.enable_auto_eval and self.evaluation_available:
                    # Add placeholder values when not evaluating
                    history["fid"].append(float("nan"))
                    history["is_mean"].append(float("nan"))
                    history["is_std"].append(float("nan"))

                    for i in range(3):
                        channel_key = f"channel_{i}"
                        history[f"{channel_key}_fid"].append(float("nan"))
                        history[f"{channel_key}_is_mean"].append(float("nan"))
                        history[f"{channel_key}_is_std"].append(float("nan"))

                # Update curriculum learning metrics if enabled
                if self.curriculum_learning and hasattr(self.diffusion_model, 'get_curriculum_metrics'):
                    curriculum_metrics = self.diffusion_model.get_curriculum_metrics()
                    history["curriculum_stage"].append(curriculum_metrics["stage"])
                    history["curriculum_timestep_range"].append(curriculum_metrics["timestep_range"])

                    # Log curriculum learning metrics
                    if epoch % 10 == 0:
                        logger.info(f"Curriculum learning metrics: stage={curriculum_metrics['stage']}, "
                                   f"timestep_range={curriculum_metrics['timestep_range']}, "
                                   f"recent_loss={curriculum_metrics['recent_loss']:.6f}")

                # Save checkpoint
                # Only save if epoch > 0 or if explicitly configured to save at epoch 0
                if epoch > 0 and epoch % self.save_every == 0:
                    is_best = val_metrics["val_loss"] < self.best_val_loss
                    if is_best:
                        self.best_val_loss = val_metrics["val_loss"]

                    self.save_checkpoint(epoch, {**train_metrics, **val_metrics}, is_best)
                elif epoch == 0:
                    # For epoch 0, only save if it's explicitly requested via save_every
                    if self.save_every == 0:
                        logger.info("Saving initial model state (epoch 0)...")
                        is_best = val_metrics["val_loss"] < self.best_val_loss
                        if is_best:
                            self.best_val_loss = val_metrics["val_loss"]
                        self.save_checkpoint(epoch, {**train_metrics, **val_metrics}, is_best)

                # Update current epoch
                self.current_epoch = epoch + 1

        # Perform final evaluation if enabled
        if self.enable_auto_eval and self.evaluation_available:
            final_epoch = self.current_epoch - 1  # Get the last completed epoch

            # Check if the last epoch was already evaluated
            last_epoch_evaluated = (
                # Case 1: Last epoch was evaluated because it was a multiple of eval_every
                (final_epoch % self.eval_every == 0) or
                # Case 2: Last epoch was evaluated because it was the last epoch in the training loop
                (final_epoch == self.max_epochs - 1)
            )

            if last_epoch_evaluated:
                logger.info("=" * 80)
                logger.info("SKIPPING FINAL EVALUATION - LAST EPOCH ALREADY EVALUATED")
                logger.info(f"Final epoch: {final_epoch}, Max epochs: {self.max_epochs}, Eval every: {self.eval_every}")
                logger.info("=" * 80)
                # Use the last evaluation metrics (they should be in history)
                if "fid" in history and len(history["fid"]) > 0:
                    eval_metrics = {
                        "fid": history["fid"][-1],
                        "is_mean": history["is_mean"][-1],
                        "is_std": history["is_std"][-1]
                    }
                    # Add channel-specific metrics if available
                    for i in range(3):
                        channel_key = f"channel_{i}"
                        if f"{channel_key}_fid" in history:
                            eval_metrics[channel_key] = {
                                "fid": history[f"{channel_key}_fid"][-1],
                                "is_mean": history[f"{channel_key}_is_mean"][-1],
                                "is_std": history[f"{channel_key}_is_std"][-1]
                            }
                else:
                    eval_metrics = {}
            else:
                logger.info("=" * 80)
                logger.info("RUNNING FINAL EVALUATION AFTER TRAINING COMPLETION")
                logger.info("=" * 80)
                logger.info(f"Final epoch: {final_epoch}, Max epochs: {self.max_epochs}, Eval every: {self.eval_every}")
                eval_metrics = self.evaluate(final_epoch)

            # Log evaluation metrics
            if eval_metrics:
                logger.info("=" * 50)
                if last_epoch_evaluated:
                    logger.info("FINAL RESULTS (FROM LAST EPOCH EVALUATION):")
                else:
                    logger.info("FINAL EVALUATION METRICS:")
                logger.info("=" * 50)
                logger.info(f"  FID: {eval_metrics.get('fid', 'N/A')}")
                logger.info(f"  IS: {eval_metrics.get('is_mean', 'N/A')} ± {eval_metrics.get('is_std', 'N/A')}")

                # Print channel-specific results if available
                for i in range(3):
                    channel_key = f"channel_{i}"
                    if channel_key in eval_metrics:
                        channel_name = eval_metrics[channel_key].get("name", f"Channel {i}")
                        fid = eval_metrics[channel_key].get("fid", "N/A")
                        is_mean = eval_metrics[channel_key].get("is_mean", "N/A")
                        is_std = eval_metrics[channel_key].get("is_std", "N/A")
                        logger.info(f"  {channel_name}:")
                        logger.info(f"    FID = {fid}")
                        logger.info(f"    IS  = {is_mean} ± {is_std}")

                logger.info("=" * 50)

                # Update history with final evaluation metrics
                if "fid" in history:
                    # Replace the last entry with actual values
                    history["fid"][-1] = eval_metrics.get("fid", float("nan"))
                    history["is_mean"][-1] = eval_metrics.get("is_mean", float("nan"))
                    history["is_std"][-1] = eval_metrics.get("is_std", float("nan"))

                    # Update channel-specific metrics
                    for i in range(3):
                        channel_key = f"channel_{i}"
                        if channel_key in eval_metrics:
                            history[f"{channel_key}_fid"][-1] = eval_metrics[channel_key].get("fid", float("nan"))
                            history[f"{channel_key}_is_mean"][-1] = eval_metrics[channel_key].get("is_mean", float("nan"))
                            history[f"{channel_key}_is_std"][-1] = eval_metrics[channel_key].get("is_std", float("nan"))

        end_time = time.time()
        logger.info(f"Training completed in {end_time - start_time:.2f} seconds")

        return history
