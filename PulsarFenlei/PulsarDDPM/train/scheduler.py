import math
import torch
import numpy as np
import matplotlib.pyplot as plt
import os
from torch.optim.lr_scheduler import _LRScheduler, CosineAnnealingLR, CosineAnnealingWarmRestarts
from typing import List, Dict, Any, Optional, Union, Callable
import logging

# Configure logging
logger = logging.getLogger(__name__)


class WarmupCosineScheduler(_LRScheduler):
    """
    Cosine annealing learning rate scheduler with warmup.

    Args:
        optimizer (torch.optim.Optimizer): Optimizer
        warmup_epochs (int): Number of warmup epochs
        max_epochs (int): Total number of epochs
        min_lr (float): Minimum learning rate
        last_epoch (int): Last epoch
        verbose (bool): Whether to print learning rate
    """
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        warmup_epochs: int,
        max_epochs: int,
        min_lr: float = 1e-6,
        last_epoch: int = -1,
        verbose: bool = False,
    ):
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.min_lr = min_lr

        # Check if max_epochs is less than or equal to warmup_epochs
        if max_epochs <= warmup_epochs:
            import warnings
            warnings.warn(
                f"max_epochs ({max_epochs}) is less than or equal to warmup_epochs ({warmup_epochs}). "
                f"This means there will be no cosine annealing phase. "
                f"Consider setting max_epochs > warmup_epochs for proper learning rate scheduling.",
                UserWarning
            )

        super().__init__(optimizer, last_epoch, verbose)

    def get_lr(self) -> List[float]:
        """
        Get learning rate based on current epoch.

        Returns:
            List[float]: Learning rates for each parameter group
        """
        if not self._get_lr_called_within_step:
            import warnings
            warnings.warn("To get the last learning rate computed by the scheduler, "
                          "please use `get_last_lr()`.", UserWarning)

        if self.last_epoch < self.warmup_epochs:
            # Linear warmup
            alpha = self.last_epoch / max(1, self.warmup_epochs)  # Avoid division by zero
            return [base_lr * alpha for base_lr in self.base_lrs]
        else:
            # Cosine annealing
            # Check if max_epochs equals warmup_epochs to avoid division by zero
            if self.max_epochs <= self.warmup_epochs:
                # If max_epochs <= warmup_epochs, return base learning rates
                # This is a special case where there's no annealing phase
                return self.base_lrs

            progress = (self.last_epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
            progress = min(1.0, progress)
            cosine_factor = 0.5 * (1.0 + math.cos(math.pi * progress))
            return [self.min_lr + (base_lr - self.min_lr) * cosine_factor for base_lr in self.base_lrs]


class CosineAnnealingWarmupRestarts(_LRScheduler):
    """
    Cosine Annealing with Warm Restarts and Warmup.

    This scheduler combines:
    1. Linear warmup at the beginning
    2. Cosine annealing with periodic restarts
    3. Minimum learning rate to prevent too small values
    4. Optional hard/soft restarts

    Args:
        optimizer (torch.optim.Optimizer): Optimizer
        first_cycle_steps (int): First cycle step size (T_0)
        cycle_mult (float): Cycle steps magnification (T_mult)
        max_lr (float): First cycle's max learning rate
        min_lr (float): Min learning rate
        warmup_steps (int): Linear warmup step size
        gamma (float): Decrease rate of max learning rate by cycle
        last_epoch (int): The index of last epoch
        verbose (bool): Whether to print learning rate
        hard_restart (bool): Whether to restart LR completely (True) or smoothly (False)
    """
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        first_cycle_steps: int,
        cycle_mult: float = 1.0,
        max_lr: float = 0.1,
        min_lr: float = 1e-6,
        warmup_steps: int = 0,
        gamma: float = 1.0,
        last_epoch: int = -1,
        verbose: bool = False,
        hard_restart: bool = True
    ):
        assert warmup_steps < first_cycle_steps, "Warmup steps should be less than first cycle steps"

        self.first_cycle_steps = first_cycle_steps
        self.cycle_mult = cycle_mult
        self.base_max_lr = max_lr
        self.max_lr = max_lr
        self.min_lr = min_lr
        self.warmup_steps = warmup_steps
        self.gamma = gamma
        self.hard_restart = hard_restart

        self.cycle = 0
        self.cycle_steps = first_cycle_steps

        # Initialize optimizer with max_lr for proper base_lrs setup
        for param_group in optimizer.param_groups:
            param_group['initial_lr'] = max_lr

        super().__init__(optimizer, last_epoch, verbose)

        # Initialize step_size_up and step_size_down for visualization
        self.step_size_up = warmup_steps
        self.step_size_down = first_cycle_steps - warmup_steps

        # PyTorch compatibility fix
        self._get_lr_called_within_step = False

    def get_lr(self) -> List[float]:
        """
        Calculate current learning rate based on cycle, warmup and restart logic.

        Returns:
            List[float]: Learning rates for each parameter group
        """
        if not self._get_lr_called_within_step:
            import warnings
            warnings.warn("To get the last learning rate computed by the scheduler, "
                          "please use `get_last_lr()`.", UserWarning)

        # Calculate current cycle and step within cycle
        if self.last_epoch == 0:
            return [self.min_lr for _ in self.base_lrs]

        # Calculate which cycle we're in
        if self.last_epoch <= self.warmup_steps:
            # We're in warmup phase
            return [self.min_lr + (base_lr - self.min_lr) * (self.last_epoch / self.warmup_steps)
                    for base_lr in self.base_lrs]

        # Calculate cycle and step within cycle
        cycle_epoch = self.last_epoch - self.warmup_steps
        cycle_steps = self.first_cycle_steps

        cycles_completed = 0
        cycle_step = cycle_epoch

        while cycle_step > cycle_steps:
            cycle_step = cycle_step - cycle_steps
            cycle_steps = cycle_steps * self.cycle_mult
            cycles_completed += 1

        # Calculate max_lr for current cycle (decreasing by gamma)
        max_lr = self.max_lr * (self.gamma ** cycles_completed)

        # Calculate current position in cycle (0 to 1)
        cos_progress = cycle_step / cycle_steps

        # Apply cosine formula
        cos_out = math.cos(math.pi * cos_progress) + 1

        # Return LR based on position in cycle
        return [self.min_lr + 0.5 * (max_lr - self.min_lr) * cos_out for _ in self.base_lrs]

    def step(self, epoch=None):
        """
        Step the scheduler with optional epoch parameter.

        Args:
            epoch (int, optional): Epoch to step to
        """
        # Set the flag for PyTorch compatibility
        self._get_lr_called_within_step = True

        if epoch is None:
            self.last_epoch += 1
        else:
            self.last_epoch = epoch

        # Check if we need to restart
        if self.last_epoch > self.warmup_steps:
            cycle_epoch = self.last_epoch - self.warmup_steps
            cycle_steps = self.first_cycle_steps

            cycles_completed = 0
            while cycle_epoch > cycle_steps:
                cycle_epoch -= cycle_steps
                cycle_steps = int(cycle_steps * self.cycle_mult)
                cycles_completed += 1

            # If we just completed a cycle and hard restart is enabled
            if cycle_epoch == 0 and self.hard_restart and self.last_epoch > self.warmup_steps + self.first_cycle_steps:
                # Reset learning rate to max_lr * gamma^cycles_completed
                self.max_lr = self.base_max_lr * (self.gamma ** cycles_completed)

                # Log restart
                logger.info(f"CosineAnnealingWarmupRestarts: Restarting cycle at epoch {self.last_epoch}, "
                           f"new max_lr = {self.max_lr:.6f}")

        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr

        self._last_lr = [group['lr'] for group in self.optimizer.param_groups]

        # Reset the flag
        self._get_lr_called_within_step = False


class GradualWarmupScheduler(_LRScheduler):
    """
    Gradually warm-up learning rate in optimizer.

    Args:
        optimizer (torch.optim.Optimizer): Optimizer
        multiplier (float): Target learning rate = base lr * multiplier if multiplier > 1.0. if multiplier = 1.0, lr starts from 0 and ends up with the base_lr.
        warmup_epochs (int): Number of warmup epochs
        after_scheduler (torch.optim.lr_scheduler._LRScheduler): Scheduler after warmup
        last_epoch (int): Last epoch
        verbose (bool): Whether to print learning rate
    """
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        multiplier: float,
        warmup_epochs: int,
        after_scheduler: _LRScheduler,
        last_epoch: int = -1,
        verbose: bool = False,
    ):
        self.multiplier = multiplier
        if self.multiplier < 1.0:
            raise ValueError('multiplier should be greater than or equal to 1.')
        self.warmup_epochs = warmup_epochs
        self.after_scheduler = after_scheduler
        self.finished = False
        super().__init__(optimizer, last_epoch, verbose)

        # PyTorch compatibility fix
        self._get_lr_called_within_step = False

    def get_lr(self) -> List[float]:
        """
        Get learning rate based on current epoch.

        Returns:
            List[float]: Learning rates for each parameter group
        """
        if not self._get_lr_called_within_step:
            import warnings
            warnings.warn("To get the last learning rate computed by the scheduler, "
                          "please use `get_last_lr()`.", UserWarning)

        # After warmup, use the after_scheduler
        if self.last_epoch >= self.warmup_epochs:
            if not self.finished:
                self.after_scheduler.base_lrs = [base_lr * self.multiplier for base_lr in self.base_lrs]
                self.finished = True
            return self.after_scheduler.get_lr()

        # During warmup
        if self.multiplier == 1.0:
            # Linear warmup from 0 to base_lr
            return [base_lr * float(self.last_epoch + 1) / self.warmup_epochs for base_lr in self.base_lrs]
        else:
            # Linear warmup from base_lr to base_lr * multiplier
            return [base_lr * (1.0 + (self.multiplier - 1.0) * float(self.last_epoch + 1) / self.warmup_epochs) for base_lr in self.base_lrs]

    def step(self, epoch: Optional[int] = None) -> None:
        """
        Step the scheduler.

        Args:
            epoch (int, optional): Epoch to step to
        """
        if self.finished and self.after_scheduler:
            if epoch is None:
                self.after_scheduler.step(None)
            else:
                self.after_scheduler.step(epoch - self.warmup_epochs)
            self._last_lr = self.after_scheduler.get_last_lr()
        else:
            return super().step(epoch)


class CurriculumCosineAnnealingLR(_LRScheduler):
    """
    Cosine Annealing LR with Curriculum Learning integration.

    This scheduler adjusts the learning rate based on both:
    1. Cosine annealing schedule
    2. Curriculum learning stage transitions

    It ensures smooth transitions between curriculum stages by gradually
    adjusting the learning rate when stage changes are detected.

    Args:
        optimizer (torch.optim.Optimizer): Optimizer
        T_max (int): Maximum number of iterations
        eta_min (float): Minimum learning rate
        warmup_epochs (int): Number of warmup epochs
        curriculum_stage_factors (List[float]): LR factors for each curriculum stage
        last_epoch (int): The index of last epoch
        verbose (bool): Whether to print learning rate
    """
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        T_max: int,
        eta_min: float = 0,
        warmup_epochs: int = 0,
        curriculum_stage_factors: List[float] = None,
        last_epoch: int = -1,
        verbose: bool = False,
    ):
        self.T_max = T_max
        self.eta_min = eta_min
        self.warmup_epochs = warmup_epochs

        # Default curriculum stage factors if not provided
        # For 6-stage curriculum: [1.0, 0.8, 0.6, 0.4, 0.2, 0.1]
        # For 3-stage curriculum: [1.0, 0.5, 0.25]
        if curriculum_stage_factors is None:
            self.curriculum_stage_factors = [1.0, 0.5, 0.25]
        else:
            self.curriculum_stage_factors = curriculum_stage_factors

        # Current curriculum state
        self.current_stage = 0
        self.stage_changed = False
        self.stage_transition_progress = 0.0
        self.prev_stage_factor = 1.0
        self.next_stage_factor = 1.0
        self.transition_window = 10  # Number of epochs for smooth transition

        super().__init__(optimizer, last_epoch, verbose)

        # PyTorch compatibility fix
        self._get_lr_called_within_step = False

    def get_lr(self) -> List[float]:
        """
        Calculate learning rate based on cosine schedule and curriculum stage.

        Returns:
            List[float]: Learning rates for each parameter group
        """
        if not self._get_lr_called_within_step:
            import warnings
            warnings.warn("To get the last learning rate computed by the scheduler, "
                          "please use `get_last_lr()`.", UserWarning)

        # Handle warmup phase
        if self.last_epoch < self.warmup_epochs:
            # Linear warmup from 0.1*base_lr to base_lr
            factor = 0.1 + 0.9 * (self.last_epoch / self.warmup_epochs)
            return [base_lr * factor for base_lr in self.base_lrs]

        # Calculate cosine annealing factor
        if self.last_epoch <= self.warmup_epochs:
            # Just finished warmup
            cosine_factor = 1.0
        else:
            # Apply cosine annealing
            cosine_progress = (self.last_epoch - self.warmup_epochs) / (self.T_max - self.warmup_epochs)
            cosine_progress = min(1.0, max(0.0, cosine_progress))
            cosine_factor = 0.5 * (1 + math.cos(math.pi * cosine_progress))

        # Apply curriculum stage factor with smooth transition
        if self.stage_changed and self.stage_transition_progress < 1.0:
            # We're in a transition between curriculum stages
            # Interpolate between previous and next stage factors
            curriculum_factor = (
                self.prev_stage_factor * (1 - self.stage_transition_progress) +
                self.next_stage_factor * self.stage_transition_progress
            )
        else:
            # Use current stage factor
            stage_idx = min(self.current_stage, len(self.curriculum_stage_factors) - 1)
            curriculum_factor = self.curriculum_stage_factors[stage_idx]

        # Combine cosine annealing with curriculum factor
        return [
            self.eta_min + (base_lr - self.eta_min) * cosine_factor * curriculum_factor
            for base_lr in self.base_lrs
        ]

    def update_curriculum_stage(self, stage: int, in_transition: bool = False, transition_progress: float = 0.0) -> None:
        """
        Update the curriculum stage and transition state.

        Args:
            stage (int): New curriculum stage
            in_transition (bool): Whether we're in a transition between stages
            transition_progress (float): Progress of the transition (0-1)
        """
        if stage != self.current_stage:
            # Stage is changing
            self.stage_changed = True
            self.prev_stage_factor = self.curriculum_stage_factors[min(self.current_stage, len(self.curriculum_stage_factors) - 1)]
            self.next_stage_factor = self.curriculum_stage_factors[min(stage, len(self.curriculum_stage_factors) - 1)]
            self.current_stage = stage
            logger.info(f"CurriculumCosineAnnealingLR: Stage changed to {stage}, "
                       f"LR factor changing from {self.prev_stage_factor:.2f} to {self.next_stage_factor:.2f}")

        # Update transition state
        if in_transition:
            self.stage_transition_progress = transition_progress
        elif self.stage_changed and self.stage_transition_progress < 1.0:
            # Increment transition progress
            self.stage_transition_progress += 1.0 / self.transition_window
            if self.stage_transition_progress >= 1.0:
                # Transition complete
                self.stage_transition_progress = 1.0
                self.stage_changed = False
                logger.info(f"CurriculumCosineAnnealingLR: Transition to stage {stage} complete")

    def set_transition_window(self, window: int) -> None:
        """
        Set the number of epochs for smooth transition between curriculum stages.

        Args:
            window (int): Number of epochs for transition
        """
        self.transition_window = max(1, window)
        logger.info(f"CurriculumCosineAnnealingLR: Transition window set to {self.transition_window} epochs")


def visualize_lr_schedule(
    scheduler: _LRScheduler,
    num_epochs: int,
    save_path: str = None,
    title: str = "Learning Rate Schedule",
    curriculum_transitions: List[int] = None,
    show_plot: bool = False,
) -> None:
    """
    Visualize learning rate schedule.

    Args:
        scheduler (_LRScheduler): Learning rate scheduler
        num_epochs (int): Number of epochs to visualize
        save_path (str, optional): Path to save the visualization
        title (str): Title of the plot
        curriculum_transitions (List[int], optional): List of curriculum transition epochs
        show_plot (bool): Whether to show the plot (set to False for headless environments)
    """
    # Store original epoch
    original_epoch = scheduler.last_epoch

    # Get optimizer's initial learning rate
    initial_lr = scheduler.optimizer.param_groups[0]['lr']

    # Reset scheduler to epoch -1
    scheduler.last_epoch = -1
    for param_group in scheduler.optimizer.param_groups:
        param_group['lr'] = initial_lr

    # Collect learning rates
    lrs = []
    for epoch in range(num_epochs):
        scheduler.step()
        lrs.append(scheduler.get_last_lr()[0])

    # Reset scheduler to original epoch
    scheduler.last_epoch = original_epoch - 1
    scheduler.step()

    # Create plot
    plt.figure(figsize=(10, 6))
    plt.plot(range(num_epochs), lrs, marker='o', markersize=3)
    plt.xlabel('Epoch')
    plt.ylabel('Learning Rate')
    plt.title(title)
    plt.grid(True)

    # Add curriculum transitions if provided
    if curriculum_transitions:
        for transition in curriculum_transitions:
            if transition < num_epochs:
                plt.axvline(x=transition, color='r', linestyle='--', alpha=0.5)
                plt.text(transition, max(lrs), f'Stage',
                         horizontalalignment='center', verticalalignment='bottom')

    # Save plot if path is provided
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        logger.info(f"Learning rate schedule visualization saved to {save_path}")

    # Show plot if requested
    if show_plot:
        plt.show()

    plt.close()


def get_scheduler(
    scheduler_type: str,
    optimizer: torch.optim.Optimizer,
    warmup_epochs: int,
    max_epochs: int,
    min_lr: float = 1e-6,
    last_epoch: int = -1,
    verbose: bool = False,
    curriculum_stage_factors: List[float] = None,
    visualize: bool = False,
    visualization_path: str = None,
    # Cosine annealing restart parameters
    t_0: int = 400,
    t_mult: float = 1.2,
    restart_decay: float = 0.8,
) -> _LRScheduler:
    """
    Get learning rate scheduler.

    Args:
        scheduler_type (str): Type of scheduler ('cosine', 'warmup_cosine', 'step', 'warmup_step',
                             'warmup_cosine_restart', 'cosine_warmup_restarts', 'curriculum_cosine', 'cosine_restart')
        optimizer (torch.optim.Optimizer): Optimizer
        warmup_epochs (int): Number of warmup epochs
        max_epochs (int): Total number of epochs
        min_lr (float): Minimum learning rate
        last_epoch (int): Last epoch
        verbose (bool): Whether to print learning rate
        curriculum_stage_factors (List[float], optional): Learning rate factors for each curriculum stage
        visualize (bool): Whether to visualize the learning rate schedule
        visualization_path (str, optional): Path to save the visualization
        t_0 (int): Initial restart period for CosineAnnealingWarmRestarts scheduler
        t_mult (float): Period multiplier factor for CosineAnnealingWarmRestarts scheduler
        restart_decay (float): Learning rate decay factor on restart for CosineAnnealingWarmRestarts scheduler

    Returns:
        _LRScheduler: Learning rate scheduler
    """
    scheduler = None

    if scheduler_type == 'cosine':
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=max_epochs,
            eta_min=min_lr,
            last_epoch=last_epoch,
            verbose=verbose,
        )
    elif scheduler_type == 'warmup_cosine':
        scheduler = WarmupCosineScheduler(
            optimizer,
            warmup_epochs=warmup_epochs,
            max_epochs=max_epochs,
            min_lr=min_lr,
            last_epoch=last_epoch,
            verbose=verbose,
        )
    elif scheduler_type == 'step':
        scheduler = torch.optim.lr_scheduler.StepLR(
            optimizer,
            step_size=max_epochs // 3,
            gamma=0.1,
            last_epoch=last_epoch,
            verbose=verbose,
        )
    elif scheduler_type == 'warmup_step':
        after_scheduler = torch.optim.lr_scheduler.StepLR(
            optimizer,
            step_size=max_epochs // 3,
            gamma=0.1,
            last_epoch=last_epoch,
            verbose=verbose,
        )
        scheduler = GradualWarmupScheduler(
            optimizer,
            multiplier=1.0,
            warmup_epochs=warmup_epochs,
            after_scheduler=after_scheduler,
            last_epoch=last_epoch,
            verbose=verbose,
        )
    elif scheduler_type == 'warmup_cosine_restart':
        after_scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=max_epochs // 3,
            T_mult=2,
            eta_min=min_lr,
            last_epoch=last_epoch,
            verbose=verbose,
        )
        scheduler = GradualWarmupScheduler(
            optimizer,
            multiplier=1.0,
            warmup_epochs=warmup_epochs,
            after_scheduler=after_scheduler,
            last_epoch=last_epoch,
            verbose=verbose,
        )
    elif scheduler_type == 'cosine_warmup_restarts':
        scheduler = CosineAnnealingWarmupRestarts(
            optimizer,
            first_cycle_steps=max_epochs // 2,  # First cycle is half of total epochs
            cycle_mult=1.0,  # Don't increase cycle length
            max_lr=optimizer.param_groups[0]['lr'],  # Use initial LR as max
            min_lr=min_lr,
            warmup_steps=warmup_epochs,
            gamma=0.5,  # Reduce max LR by half each cycle
            last_epoch=last_epoch,
            verbose=verbose,
        )
    elif scheduler_type == 'curriculum_cosine':
        # Default curriculum stage factors if not provided
        if curriculum_stage_factors is None:
            # Check if we can determine the number of stages from the factors
            if hasattr(optimizer, 'param_groups') and len(optimizer.param_groups) > 0:
                # Try to get the number of stages from the optimizer
                num_stages = optimizer.param_groups[0].get('num_stages', 3)
                if num_stages == 6:
                    # 6-stage curriculum
                    curriculum_stage_factors = [1.0, 0.8, 0.6, 0.4, 0.2, 0.1]
                else:
                    # 3-stage curriculum
                    curriculum_stage_factors = [1.0, 0.5, 0.25]
            else:
                # Default to 3-stage curriculum
                curriculum_stage_factors = [1.0, 0.5, 0.25]

        scheduler = CurriculumCosineAnnealingLR(
            optimizer,
            T_max=max_epochs,
            eta_min=min_lr,
            warmup_epochs=warmup_epochs,
            curriculum_stage_factors=curriculum_stage_factors,
            last_epoch=last_epoch,
            verbose=verbose,
        )
    elif scheduler_type == 'cosine_restart':
        # Direct CosineAnnealingWarmRestarts without warmup wrapper
        # This is the new scheduler type for A100 optimization
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=t_0,
            T_mult=int(t_mult),  # T_mult must be integer
            eta_min=min_lr,
            last_epoch=last_epoch,
            verbose=verbose,
        )
        logger.info(f"Created CosineAnnealingWarmRestarts scheduler: T_0={t_0}, T_mult={t_mult}, eta_min={min_lr}")
    else:
        raise ValueError(f"Unknown scheduler type: {scheduler_type}")

    # Visualize learning rate schedule if requested
    if visualize and scheduler is not None:
        if visualization_path is None:
            # Default path
            visualization_path = "lr_schedule.png"

        # Create a copy of the scheduler for visualization to avoid affecting the original
        vis_optimizer = torch.optim.SGD([torch.nn.Parameter(torch.zeros(1))], lr=optimizer.param_groups[0]['lr'])

        if scheduler_type == 'cosine':
            vis_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                vis_optimizer, T_max=max_epochs, eta_min=min_lr)
        elif scheduler_type == 'warmup_cosine':
            vis_scheduler = WarmupCosineScheduler(
                vis_optimizer, warmup_epochs=warmup_epochs, max_epochs=max_epochs, min_lr=min_lr)
        elif scheduler_type == 'curriculum_cosine':
            vis_scheduler = CurriculumCosineAnnealingLR(
                vis_optimizer, T_max=max_epochs, eta_min=min_lr, warmup_epochs=warmup_epochs,
                curriculum_stage_factors=curriculum_stage_factors)
        elif scheduler_type == 'cosine_warmup_restarts':
            vis_scheduler = CosineAnnealingWarmupRestarts(
                vis_optimizer, first_cycle_steps=max_epochs // 2, cycle_mult=1.0,
                max_lr=optimizer.param_groups[0]['lr'], min_lr=min_lr,
                warmup_steps=warmup_epochs, gamma=0.5)
        else:
            # For other scheduler types, use the original scheduler
            vis_scheduler = scheduler

        # Determine curriculum transitions for visualization
        curriculum_transitions = None
        if hasattr(optimizer, 'param_groups') and len(optimizer.param_groups) > 0:
            curriculum_transitions = optimizer.param_groups[0].get('curriculum_transitions', None)

        visualize_lr_schedule(
            vis_scheduler,
            num_epochs=max_epochs,
            save_path=visualization_path,
            title=f"Learning Rate Schedule ({scheduler_type})",
            curriculum_transitions=curriculum_transitions,
            show_plot=False  # Don't show plot in headless environment
        )

    return scheduler
