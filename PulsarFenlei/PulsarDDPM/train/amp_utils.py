"""
Utilities for Automatic Mixed Precision (AMP) training.
"""
import torch
import logging
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class AMPScaler:
    """
    Gradient scaler for Automatic Mixed Precision (AMP) training.
    
    This class provides a simple implementation of gradient scaling for mixed precision training,
    similar to torch.cuda.amp.GradScaler but with more detailed logging and control.
    
    Args:
        enabled (bool): Whether to enable mixed precision training
        init_scale (float): Initial scale factor
        growth_factor (float): Factor by which to grow scale after successful steps
        backoff_factor (float): Factor by which to reduce scale after NaN/Inf gradients
        growth_interval (int): Number of consecutive successful steps before growing scale
        max_scale (float): Maximum scale factor
    """
    def __init__(
        self,
        enabled: bool = True,
        init_scale: float = 2.**16,
        growth_factor: float = 2.0,
        backoff_factor: float = 0.5,
        growth_interval: int = 2000,
        max_scale: float = 2.**24
    ):
        self.enabled = enabled and torch.cuda.is_available()
        self.scale = init_scale
        self.growth_factor = growth_factor
        self.backoff_factor = backoff_factor
        self.growth_interval = growth_interval
        self.max_scale = max_scale
        
        self.successful_steps = 0
        self.total_steps = 0
        self.nan_inf_steps = 0
        
        if self.enabled:
            logger.info(f"AMP training enabled with initial scale: {self.scale}")
        else:
            logger.info("AMP training disabled")
    
    def scale_loss(self, loss: torch.Tensor) -> torch.Tensor:
        """
        Scale the loss for mixed precision training.
        
        Args:
            loss (torch.Tensor): Loss tensor
            
        Returns:
            torch.Tensor: Scaled loss tensor
        """
        if not self.enabled:
            return loss
        
        return loss * self.scale
    
    def unscale_gradients(self, optimizer: torch.optim.Optimizer) -> bool:
        """
        Unscale gradients for mixed precision training.
        
        Args:
            optimizer (torch.optim.Optimizer): Optimizer
            
        Returns:
            bool: True if gradients were successfully unscaled, False if NaN/Inf detected
        """
        if not self.enabled:
            return True
        
        self.total_steps += 1
        
        # Check for NaN/Inf gradients
        for param_group in optimizer.param_groups:
            for param in param_group['params']:
                if param.grad is not None:
                    if torch.isnan(param.grad).any() or torch.isinf(param.grad).any():
                        self._update_scale(success=False)
                        self.nan_inf_steps += 1
                        
                        # Log NaN/Inf statistics
                        if self.nan_inf_steps % 10 == 0:
                            logger.warning(f"NaN/Inf gradients detected ({self.nan_inf_steps}/{self.total_steps} steps). "
                                          f"Current scale: {self.scale}")
                        
                        return False
        
        # Unscale gradients
        inv_scale = 1.0 / self.scale
        for param_group in optimizer.param_groups:
            for param in param_group['params']:
                if param.grad is not None:
                    param.grad.mul_(inv_scale)
        
        self._update_scale(success=True)
        return True
    
    def _update_scale(self, success: bool) -> None:
        """
        Update scale factor based on step success.
        
        Args:
            success (bool): Whether the step was successful
        """
        if not self.enabled:
            return
        
        if success:
            self.successful_steps += 1
            
            # Grow scale after growth_interval successful steps
            if self.successful_steps >= self.growth_interval:
                old_scale = self.scale
                self.scale = min(self.scale * self.growth_factor, self.max_scale)
                self.successful_steps = 0
                
                # Log scale growth
                if old_scale != self.scale:
                    logger.info(f"AMP scale increased: {old_scale} -> {self.scale}")
        else:
            # Reduce scale after NaN/Inf gradients
            old_scale = self.scale
            self.scale = max(self.scale * self.backoff_factor, 1.0)
            self.successful_steps = 0
            
            # Log scale reduction
            logger.warning(f"AMP scale decreased: {old_scale} -> {self.scale}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the scaler.
        
        Returns:
            Dict[str, Any]: Statistics about the scaler
        """
        return {
            "enabled": self.enabled,
            "scale": self.scale,
            "successful_steps": self.successful_steps,
            "total_steps": self.total_steps,
            "nan_inf_steps": self.nan_inf_steps,
            "success_rate": (self.total_steps - self.nan_inf_steps) / max(1, self.total_steps)
        }


def setup_amp(enabled: bool = True) -> Tuple[AMPScaler, Dict[str, Any]]:
    """
    Setup Automatic Mixed Precision (AMP) training.
    
    Args:
        enabled (bool): Whether to enable mixed precision training
        
    Returns:
        Tuple[AMPScaler, Dict[str, Any]]: Gradient scaler and AMP configuration
    """
    # Check if CUDA is available
    if not torch.cuda.is_available():
        logger.warning("CUDA not available, disabling AMP")
        enabled = False
    
    # Check if we're using an A100 GPU
    if enabled and torch.cuda.is_available():
        device_name = torch.cuda.get_device_name()
        if "A100" in device_name:
            logger.info(f"Detected {device_name}, optimizing AMP settings")
            # A100-specific optimizations
            amp_config = {
                "init_scale": 2.**16,
                "growth_factor": 2.0,
                "backoff_factor": 0.5,
                "growth_interval": 1000,  # Grow scale more frequently on A100
                "max_scale": 2.**32,      # Allow higher max scale on A100
            }
        else:
            logger.info(f"Using {device_name}, using standard AMP settings")
            # Standard settings for other GPUs
            amp_config = {
                "init_scale": 2.**16,
                "growth_factor": 2.0,
                "backoff_factor": 0.5,
                "growth_interval": 2000,
                "max_scale": 2.**24,
            }
    else:
        amp_config = {}
    
    # Create gradient scaler
    scaler = AMPScaler(enabled=enabled, **amp_config)
    
    return scaler, amp_config
