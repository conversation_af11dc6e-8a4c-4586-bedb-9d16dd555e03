#!/usr/bin/env python3
"""
渐进式三阶段训练器
为PulsarTransformerDDPM实现渐进式训练策略
"""

import os
import time
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple, Any
from tqdm import tqdm

from .trainer import Trainer
from .loss_integration_adapter import LossIntegrationAdapter, create_integrated_loss

logger = logging.getLogger(__name__)


class ProgressiveTrainer(Trainer):
    """
    渐进式三阶段训练器

    阶段1：基础扩散训练（标准DDPM损失）
    阶段2：物理约束增强训练（加入通道特定损失）
    阶段3：精细调优训练（小学习率微调）
    """

    def __init__(
        self,
        diffusion_model: nn.Module,
        train_dataloader: torch.utils.data.DataLoader,
        val_dataloader: Optional[torch.utils.data.DataLoader] = None,
        # 基础参数
        lr: float = 5e-5,
        weight_decay: float = 1e-5,
        gradient_clip_val: float = 0.5,
        device: str = "cuda",
        log_dir: str = "logs",
        checkpoint_dir: str = "checkpoints",
        # 渐进式训练参数
        stage_epochs: List[int] = [150, 150, 100],  # 每个阶段的训练轮数
        stage_lr_factors: List[float] = [1.0, 0.5, 0.1],  # 每个阶段的学习率因子
        # 物理约束损失参数
        physics_loss_config: Dict[str, float] = None,
        # 小样本学习优化
        data_augmentation: bool = True,
        early_stopping_patience: int = 3,
        warmup_epochs: int = 5,
        # PulsarAdaptiveSampler参数
        external_sampler: Optional[Any] = None,
        num_inference_steps: int = 20,
        **kwargs
    ):
        # 初始化优化的物理约束损失
        if physics_loss_config is None:
            physics_loss_config = {
                'mse_weight': 0.8,
                'consistency_weight': 0.15,
                'physics_weight': 0.05,
                'spectral_weight': 0.0,
                'channel_diffusion_weight': 0.0
            }

        # 创建集成损失函数配置
        loss_config = {
            'physics_loss_config': physics_loss_config,
            'use_optimized_loss': True,
            'enable_loss_scaling': True,
            'enable_adaptive_weights': True,
            'num_timesteps': 150
        }

        # 使用create_integrated_loss创建损失函数
        self.physics_loss = create_integrated_loss(loss_config, device)

        # 渐进式训练参数
        self.stage_epochs = stage_epochs
        self.stage_lr_factors = stage_lr_factors
        self.total_epochs = sum(stage_epochs)
        self.data_augmentation = data_augmentation

        # PulsarAdaptiveSampler参数
        self.external_sampler = external_sampler
        self.num_inference_steps = num_inference_steps

        # 当前阶段信息
        self.current_stage = 0
        self.stage_start_epoch = 0
        self.stage_names = ["basic", "physics", "fine_tune"]

        # 调用父类初始化 - 显式传递所有重要参数
        super().__init__(
            diffusion_model=diffusion_model,
            train_dataloader=train_dataloader,
            val_dataloader=val_dataloader,
            lr=lr,
            weight_decay=weight_decay,
            max_epochs=self.total_epochs,
            gradient_clip_val=gradient_clip_val,
            device=device,
            log_dir=log_dir,
            checkpoint_dir=checkpoint_dir,
            warmup_epochs=warmup_epochs,
            # 确保评估参数正确传递
            eval_every=kwargs.get('eval_every', 50),  # 默认50而非10
            save_every=kwargs.get('save_every', 50),  # 默认50而非10
            enable_auto_eval=kwargs.get('enable_auto_eval', True),
            data_dir=kwargs.get('data_dir', '/Pulsar/PulsarFenlei/data/htru1-batches-py'),
            inception_model_path=kwargs.get('inception_model_path', 'evaluation/inception_v3_google-0cc3c7bd.pth'),
            eval_num_samples=kwargs.get('eval_num_samples', 995),
            eval_batch_size=kwargs.get('eval_batch_size', 50),
            # PulsarAdaptiveSampler参数
            external_sampler=external_sampler,
            num_inference_steps=num_inference_steps,
            **{k: v for k, v in kwargs.items() if k not in [
                'eval_every', 'save_every', 'enable_auto_eval', 'data_dir',
                'inception_model_path', 'eval_num_samples', 'eval_batch_size',
                'external_sampler', 'num_inference_steps', 'max_epochs'
            ]}
        )

        # 保存初始学习率
        self.base_lr = lr

        # 设置初始训练阶段 (优化损失函数无需设置阶段)
        logger.info(f"使用优化损失函数，初始阶段: {self.stage_names[0]}")

        logger.info("=" * 60)
        logger.info("渐进式三阶段训练器初始化完成")
        logger.info("=" * 60)
        logger.info(f"阶段1 (基础扩散): {self.stage_epochs[0]} epochs, LR factor: {self.stage_lr_factors[0]}")
        logger.info(f"阶段2 (物理约束): {self.stage_epochs[1]} epochs, LR factor: {self.stage_lr_factors[1]}")
        logger.info(f"阶段3 (精细调优): {self.stage_epochs[2]} epochs, LR factor: {self.stage_lr_factors[2]}")
        logger.info(f"总训练轮数: {self.total_epochs}")
        logger.info("=" * 60)

    def update_training_stage(self, epoch: int) -> bool:
        """
        更新训练阶段

        Args:
            epoch: 当前训练轮数
        Returns:
            是否切换了阶段
        """
        # 计算当前应该在哪个阶段
        cumulative_epochs = 0
        new_stage = 0

        for i, stage_epoch in enumerate(self.stage_epochs):
            cumulative_epochs += stage_epoch
            if epoch < cumulative_epochs:
                new_stage = i
                break

        # 检查是否需要切换阶段
        if new_stage != self.current_stage:
            old_stage = self.current_stage
            self.current_stage = new_stage

            # 更新阶段开始轮数
            self.stage_start_epoch = sum(self.stage_epochs[:new_stage])

            # 更新物理损失的训练阶段 (优化损失函数无需设置阶段)
            stage_name = self.stage_names[new_stage]
            logger.info(f"使用优化损失函数，切换到阶段: {stage_name}")

            # 调整学习率
            new_lr = self.base_lr * self.stage_lr_factors[new_stage]
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = new_lr

            logger.info("=" * 60)
            logger.info(f"训练阶段切换: 阶段{old_stage+1} → 阶段{new_stage+1}")
            logger.info(f"新阶段: {stage_name}")
            logger.info(f"学习率调整: {self.base_lr * self.stage_lr_factors[old_stage]:.6f} → {new_lr:.6f}")
            logger.info(f"物理损失权重: 优化损失函数激活")
            logger.info("=" * 60)

            return True

        return False

    def train_epoch(self, epoch: int, resolution: int = 32) -> Dict[str, float]:
        """
        训练一个epoch（重写父类方法以使用物理约束损失）

        Args:
            epoch: 当前epoch
            resolution: 当前分辨率
        Returns:
            训练指标字典
        """
        # 更新训练阶段
        stage_changed = self.update_training_stage(epoch)

        self.diffusion_model.train()

        # 初始化损失统计
        epoch_losses = {
            'total_loss': 0.0,
            'mse_loss': 0.0,
            'consistency_loss': 0.0,
            'physics_loss': 0.0,
            'spectral_loss': 0.0
        }

        num_batches = len(self.train_dataloader)

        # 进度条描述
        stage_info = f"Stage {self.current_stage+1}/{len(self.stage_epochs)} ({self.stage_names[self.current_stage]})"
        progress_desc = f"Epoch {epoch}/{self.max_epochs} - {stage_info}"

        progress_bar = tqdm(self.train_dataloader, desc=progress_desc)

        for batch_idx, (x, _) in enumerate(progress_bar):
            # 数据增强（仅在阶段1和2使用）
            if self.data_augmentation and self.current_stage < 2:
                x = self.apply_data_augmentation(x)

            # 移动数据到设备
            x = x.to(self.device)

            # 清零梯度
            self.optimizer.zero_grad()

            # 前向传播 - 使用SimplifiedCNNUNet进行DDPM训练
            batch_size = x.shape[0]

            # 采样随机时间步
            timesteps = torch.randint(0, 1000, (batch_size,), device=x.device)

            # 添加噪声（标准DDPM前向过程）
            noise = torch.randn_like(x)

            # 使用标准的线性beta调度
            betas = torch.linspace(0.0001, 0.02, 1000, device=x.device)
            alphas = 1.0 - betas
            alphas_cumprod = torch.cumprod(alphas, dim=0)

            # 获取时间步对应的系数
            sqrt_alphas_cumprod_t = torch.sqrt(alphas_cumprod[timesteps]).view(-1, 1, 1, 1)
            sqrt_one_minus_alphas_cumprod_t = torch.sqrt(1.0 - alphas_cumprod[timesteps]).view(-1, 1, 1, 1)

            # 添加噪声：x_t = sqrt(α_t) * x_0 + sqrt(1-α_t) * ε
            x_noisy = sqrt_alphas_cumprod_t * x + sqrt_one_minus_alphas_cumprod_t * noise

            # 模型预测噪声
            pred_noise = self.diffusion_model(x_noisy, timesteps)

            # 使用物理约束损失
            total_loss, physics_loss_dict = self.physics_loss(pred_noise, noise)

            # 反向传播
            total_loss.backward()

            # 梯度裁剪
            if self.gradient_clip_val > 0:
                torch.nn.utils.clip_grad_norm_(self.diffusion_model.parameters(), self.gradient_clip_val)

            # 更新权重
            self.optimizer.step()

            # 更新学习率
            self.scheduler.step()

            # 更新EMA
            if hasattr(self.diffusion_model, 'update_ema'):
                self.diffusion_model.update_ema()

            # 累积损失
            for key in epoch_losses:
                if key in physics_loss_dict:
                    epoch_losses[key] += physics_loss_dict[key]

            # 更新进度条
            progress_bar.set_postfix({
                "total_loss": physics_loss_dict.get('total_loss', 0.0),
                "mse_loss": physics_loss_dict.get('mse_loss', 0.0),
                "lr": self.scheduler.get_last_lr()[0],
                "stage": f"{self.current_stage+1}/{len(self.stage_epochs)}"
            })

            # 定期日志
            if batch_idx % 50 == 0 or batch_idx == 0 or batch_idx == num_batches - 1:
                logger.info(
                    f"[{batch_idx}/{num_batches}] Epoch {epoch}/{self.max_epochs} "
                    f"Stage {self.current_stage+1} lr:{self.scheduler.get_last_lr()[0]:.6f} "
                    f"total_loss:{physics_loss_dict.get('total_loss', 0.0):.6f}"
                )

            self.global_step += 1

        # 计算平均损失
        for key in epoch_losses:
            epoch_losses[key] /= num_batches

        # 确保返回格式与Trainer基类兼容
        epoch_losses['loss'] = epoch_losses['total_loss']

        return epoch_losses

    def apply_data_augmentation(self, x: torch.Tensor) -> torch.Tensor:
        """
        应用数据增强（针对小样本学习优化）

        Args:
            x: 输入数据 (B, C, H, W)
        Returns:
            增强后的数据
        """
        # 随机水平翻转
        if torch.rand(1) > 0.5:
            x = torch.flip(x, dims=[3])

        # 随机垂直翻转
        if torch.rand(1) > 0.5:
            x = torch.flip(x, dims=[2])

        # 随机旋转90度的倍数
        if torch.rand(1) > 0.7:
            k = torch.randint(1, 4, (1,)).item()
            x = torch.rot90(x, k, dims=[2, 3])

        # 轻微的噪声注入（仅在阶段1使用）
        if self.current_stage == 0 and torch.rand(1) > 0.8:
            noise_scale = 0.01
            x = x + torch.randn_like(x) * noise_scale

        return x

    def get_training_info(self) -> Dict[str, Any]:
        """获取当前训练状态信息"""
        stage_progress = (self.current_epoch - self.stage_start_epoch) / self.stage_epochs[self.current_stage]
        total_progress = self.current_epoch / self.total_epochs

        return {
            'current_stage': self.current_stage + 1,
            'stage_name': self.stage_names[self.current_stage],
            'stage_progress': stage_progress,
            'total_progress': total_progress,
            'stage_epochs_remaining': self.stage_epochs[self.current_stage] - (self.current_epoch - self.stage_start_epoch),
            'total_epochs_remaining': self.total_epochs - self.current_epoch,
            'physics_loss_weights': 'optimized_loss_active'
        }

    def save_checkpoint(self, epoch: int, metrics: Dict[str, float], is_best: bool = False):
        """保存检查点（扩展以包含阶段信息）"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.diffusion_model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'metrics': metrics,
            'current_stage': self.current_stage,
            'stage_start_epoch': self.stage_start_epoch,
            'training_info': self.get_training_info()
        }

        # 保存常规检查点
        checkpoint_path = os.path.join(self.checkpoint_dir, f"checkpoint_epoch_{epoch}.pt")
        torch.save(checkpoint, checkpoint_path)

        # 保存最佳检查点
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, "best_checkpoint.pt")
            torch.save(checkpoint, best_path)

        logger.info(f"检查点已保存: {checkpoint_path}")

    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """加载检查点（扩展以恢复阶段信息）"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        # 加载模型和优化器状态
        self.diffusion_model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        # 恢复训练状态
        self.current_epoch = checkpoint['epoch']
        self.current_stage = checkpoint.get('current_stage', 0)
        self.stage_start_epoch = checkpoint.get('stage_start_epoch', 0)

        # 恢复物理损失阶段 (优化损失函数无需设置阶段)
        logger.info(f"使用优化损失函数，当前阶段: {self.stage_names[self.current_stage] if self.current_stage < len(self.stage_names) else 'unknown'}")

        logger.info(f"检查点已加载: {checkpoint_path}")
        logger.info(f"恢复到 Epoch {self.current_epoch}, Stage {self.current_stage + 1}")

        return checkpoint
