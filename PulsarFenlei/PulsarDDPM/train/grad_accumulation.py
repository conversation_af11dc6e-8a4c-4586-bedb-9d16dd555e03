"""
Gradient accumulation utilities for training with larger effective batch sizes.
"""
import torch
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class GradientAccumulator:
    """
    Gradient accumulation helper for training with larger effective batch sizes.
    
    This class helps manage gradient accumulation, allowing training with larger
    effective batch sizes than would fit in GPU memory.
    
    Args:
        steps (int): Number of steps to accumulate gradients over
        optimizer (torch.optim.Optimizer): Optimizer to use
        scheduler (Optional[Any]): Learning rate scheduler
        clip_grad_value (float): Value for gradient clipping (0 to disable)
    """
    def __init__(
        self,
        steps: int,
        optimizer: torch.optim.Optimizer,
        scheduler: Optional[Any] = None,
        clip_grad_value: float = 0.0
    ):
        self.steps = max(1, steps)  # Ensure at least 1 step
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.clip_grad_value = clip_grad_value
        
        self.current_step = 0
        self.loss_sum = 0.0
        self.loss_count = 0
        
        logger.info(f"Gradient accumulation initialized with {self.steps} steps")
        if self.steps > 1:
            logger.info(f"Effective batch size will be {self.steps}x larger than actual batch size")
    
    def backward(self, loss: torch.Tensor) -> None:
        """
        Perform backward pass with gradient accumulation.
        
        Args:
            loss (torch.Tensor): Loss tensor
        """
        # Scale loss by number of accumulation steps
        scaled_loss = loss / self.steps
        
        # Backward pass
        scaled_loss.backward()
        
        # Accumulate loss for reporting
        self.loss_sum += loss.item()
        self.loss_count += 1
        
        # Increment step counter
        self.current_step += 1
    
    def step(self) -> Dict[str, float]:
        """
        Perform optimizer step after accumulating gradients.
        
        Returns:
            Dict[str, float]: Dictionary with average loss
        """
        # Only update weights after accumulating gradients for specified number of steps
        if self.current_step % self.steps == 0:
            # Clip gradients if enabled
            if self.clip_grad_value > 0:
                torch.nn.utils.clip_grad_norm_(
                    self._get_parameters(), 
                    self.clip_grad_value
                )
            
            # Update weights
            self.optimizer.step()
            
            # Zero gradients
            self.optimizer.zero_grad()
            
            # Step scheduler if provided
            if self.scheduler is not None:
                self.scheduler.step()
        
        # Calculate average loss
        avg_loss = self.loss_sum / max(1, self.loss_count)
        
        # Reset accumulators if we've completed a full accumulation cycle
        if self.current_step % self.steps == 0:
            self.loss_sum = 0.0
            self.loss_count = 0
        
        return {"loss": avg_loss}
    
    def _get_parameters(self):
        """Get all parameters from optimizer."""
        params = []
        for param_group in self.optimizer.param_groups:
            params.extend(param_group['params'])
        return params
    
    def zero_grad(self) -> None:
        """Zero gradients in optimizer."""
        self.optimizer.zero_grad()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the accumulator.
        
        Returns:
            Dict[str, Any]: Statistics about the accumulator
        """
        return {
            "accumulation_steps": self.steps,
            "current_step": self.current_step,
            "effective_batch_size_multiplier": self.steps,
        }


def setup_grad_accumulation(
    optimizer: torch.optim.Optimizer,
    accumulation_steps: int = 1,
    scheduler: Optional[Any] = None,
    clip_grad_value: float = 0.0
) -> GradientAccumulator:
    """
    Setup gradient accumulation.
    
    Args:
        optimizer (torch.optim.Optimizer): Optimizer to use
        accumulation_steps (int): Number of steps to accumulate gradients over
        scheduler (Optional[Any]): Learning rate scheduler
        clip_grad_value (float): Value for gradient clipping (0 to disable)
        
    Returns:
        GradientAccumulator: Gradient accumulator
    """
    return GradientAccumulator(
        steps=accumulation_steps,
        optimizer=optimizer,
        scheduler=scheduler,
        clip_grad_value=clip_grad_value
    )
