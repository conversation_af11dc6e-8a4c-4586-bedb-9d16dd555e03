#!/usr/bin/env python3
"""
PulsarDDPM训练监控脚本 (优化版本)
专为SimplifiedCNNUNet + OptimizedPulsarLoss设计的实时监控系统
"""

import os
import re
import argparse
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import time
import glob
import json
from typing import Dict, List, Tuple, Optional


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="监控PulsarDDPM优化版本训练进度")
    parser.add_argument("--log_dir", type=str, default="outputs_a100",
                        help="训练日志目录")
    parser.add_argument("--refresh_interval", type=int, default=300,
                        help="刷新间隔(秒)")
    parser.add_argument("--output_dir", type=str, default=None,
                        help="图表保存目录")
    parser.add_argument("--auto_refresh", action="store_true", default=True,
                        help="自动刷新监控")
    return parser.parse_args()


def find_latest_log(log_dir: str) -> Optional[str]:
    """查找最新的训练日志文件"""
    log_patterns = [
        os.path.join(log_dir, "training_*.log"),
        os.path.join(log_dir, "**", "training_*.log"),
        os.path.join(log_dir, "*.log"),
        os.path.join(log_dir, "**", "*.log")
    ]

    log_files = []
    for pattern in log_patterns:
        log_files.extend(glob.glob(pattern, recursive=True))

    if not log_files:
        return None

    # 按修改时间排序
    log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    return log_files[0]


def parse_pulsar_log(log_file: str) -> Dict:
    """解析PulsarDDPM优化版本训练日志"""
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        return {}

    with open(log_file, 'r', encoding='utf-8') as f:
        log_content = f.read()

    metrics = {
        'epochs': [],
        'training_losses': [],
        'training_stages': [],
        'learning_rates': [],
        'physics_losses': [],
        'mse_losses': [],
        'consistency_losses': [],
        'spectral_losses': [],
        'channel_diffusion_losses': [],
        'fid_scores': [],
        'is_scores': [],
        'eval_epochs': [],
        'ddim_sampling_times': [],
        'pulsar_adaptive_sampling_times': [],
        'gpu_memory_usage': [],
        'training_times': []
    }

    # 解析训练损失和阶段信息
    # 匹配格式: "Epoch 1/400 - Stage: basic - Loss: 1.234567"
    epoch_pattern = r'Epoch (\d+)/\d+ - Stage: (\w+) - Loss: ([0-9.]+)'
    for match in re.finditer(epoch_pattern, log_content):
        epoch = int(match.group(1))
        stage = match.group(2)
        loss = float(match.group(3))

        metrics['epochs'].append(epoch)
        metrics['training_stages'].append(stage)
        metrics['training_losses'].append(loss)

    # 解析学习率
    # 匹配格式: "Learning Rate: 5.000000e-05"
    lr_pattern = r'Learning Rate: ([0-9.e\-+]+)'
    lr_matches = re.findall(lr_pattern, log_content)

    # 将学习率与epoch对应
    for i, lr_str in enumerate(lr_matches):
        if i < len(metrics['epochs']):
            metrics['learning_rates'].append(float(lr_str))

    # 补齐学习率数据
    while len(metrics['learning_rates']) < len(metrics['epochs']):
        if metrics['learning_rates']:
            metrics['learning_rates'].append(metrics['learning_rates'][-1])
        else:
            metrics['learning_rates'].append(5e-5)  # 默认学习率

    # 解析物理约束损失组件
    # 匹配格式: "Physics Loss Components: {'mse_loss': 1.234, 'consistency_loss': 0.123, ...}"
    physics_pattern = r"Physics Loss Components: \{([^}]+)\}"
    for match in re.finditer(physics_pattern, log_content):
        components_str = match.group(1)

        # 解析各组件损失
        mse_match = re.search(r"'mse_loss': ([0-9.]+)", components_str)
        consistency_match = re.search(r"'consistency_loss': ([0-9.]+)", components_str)
        physics_match = re.search(r"'physics_loss': ([0-9.]+)", components_str)
        spectral_match = re.search(r"'spectral_loss': ([0-9.]+)", components_str)
        channel_diff_match = re.search(r"'channel_diffusion_loss': ([0-9.]+)", components_str)

        if mse_match:
            metrics['mse_losses'].append(float(mse_match.group(1)))
        if consistency_match:
            metrics['consistency_losses'].append(float(consistency_match.group(1)))
        if physics_match:
            metrics['physics_losses'].append(float(physics_match.group(1)))
        if spectral_match:
            metrics['spectral_losses'].append(float(spectral_match.group(1)))
        if channel_diff_match:
            metrics['channel_diffusion_losses'].append(float(channel_diff_match.group(1)))

    # 解析FID和IS评估结果
    # 匹配格式: "Evaluation Results - Epoch 50: FID=75.23, IS=2.45"
    eval_pattern = r'Evaluation Results - Epoch (\d+): FID=([0-9.]+), IS=([0-9.]+)'
    for match in re.finditer(eval_pattern, log_content):
        epoch = int(match.group(1))
        fid = float(match.group(2))
        is_score = float(match.group(3))

        metrics['eval_epochs'].append(epoch)
        metrics['fid_scores'].append(fid)
        metrics['is_scores'].append(is_score)

    # 解析DDIM采样时间
    # 匹配格式: "DDIM Sampling completed in 1.23s"
    ddim_pattern = r'DDIM Sampling completed in ([0-9.]+)s'
    ddim_times = [float(match.group(1)) for match in re.finditer(ddim_pattern, log_content)]
    metrics['ddim_sampling_times'] = ddim_times

    # 解析PulsarAdaptiveSampler采样时间
    # 匹配格式: "PulsarAdaptiveSampler completed in 0.85s" 或 "Using PulsarAdaptiveSampler for evaluation"
    pulsar_adaptive_pattern = r'PulsarAdaptiveSampler.*?completed in ([0-9.]+)s'
    pulsar_adaptive_times = [float(match.group(1)) for match in re.finditer(pulsar_adaptive_pattern, log_content)]
    metrics['pulsar_adaptive_sampling_times'] = pulsar_adaptive_times

    # 解析GPU内存使用
    # 匹配格式: "GPU Memory: 12.34GB / 40.00GB"
    memory_pattern = r'GPU Memory: ([0-9.]+)GB / ([0-9.]+)GB'
    for match in re.finditer(memory_pattern, log_content):
        used_memory = float(match.group(1))
        metrics['gpu_memory_usage'].append(used_memory)

    # 解析训练时间
    # 匹配格式: "Epoch completed in 123.45s"
    time_pattern = r'Epoch completed in ([0-9.]+)s'
    epoch_times = [float(match.group(1)) for match in re.finditer(time_pattern, log_content)]
    metrics['training_times'] = epoch_times

    return metrics


def plot_training_progress(metrics: Dict, output_dir: str):
    """绘制训练进度图表"""
    if not metrics or not metrics['epochs']:
        print("没有可绘制的数据")
        return

    os.makedirs(output_dir, exist_ok=True)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    epochs = metrics['epochs']

    # 1. 训练损失和阶段
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # 训练损失
    ax1.plot(epochs, metrics['training_losses'], 'b-', linewidth=2, label='Training Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('PulsarTransformerDDPM Training Loss')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 训练阶段
    stage_colors = {'basic': 'green', 'physics': 'orange', 'fine_tune': 'red'}
    stage_y = []
    stage_colors_list = []

    for stage in metrics['training_stages']:
        if stage == 'basic':
            stage_y.append(0)
        elif stage == 'physics':
            stage_y.append(1)
        elif stage == 'fine_tune':
            stage_y.append(2)
        else:
            stage_y.append(0)
        stage_colors_list.append(stage_colors.get(stage, 'blue'))

    ax2.scatter(epochs, stage_y, c=stage_colors_list, s=20, alpha=0.7)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Training Stage')
    ax2.set_title('Three-Stage Progressive Training')
    ax2.set_yticks([0, 1, 2])
    ax2.set_yticklabels(['Basic', 'Physics', 'Fine-tune'])
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'training_progress.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 物理约束损失组件
    if metrics['mse_losses']:
        plt.figure(figsize=(12, 8))

        # 确保所有损失组件长度一致
        min_len = min(len(metrics['mse_losses']),
                     len(metrics['consistency_losses']),
                     len(metrics['physics_losses']),
                     len(metrics['spectral_losses']),
                     len(metrics['channel_diffusion_losses']))

        if min_len > 0:
            x_indices = range(min_len)

            plt.plot(x_indices, metrics['mse_losses'][:min_len], 'b-', label='MSE Loss', linewidth=2)
            plt.plot(x_indices, metrics['consistency_losses'][:min_len], 'g-', label='Consistency Loss', linewidth=2)
            plt.plot(x_indices, metrics['physics_losses'][:min_len], 'r-', label='Physics Loss', linewidth=2)
            plt.plot(x_indices, metrics['spectral_losses'][:min_len], 'm-', label='Spectral Loss', linewidth=2)
            plt.plot(x_indices, metrics['channel_diffusion_losses'][:min_len], 'c-', label='Channel Diffusion Loss', linewidth=2)

            plt.xlabel('Training Step')
            plt.ylabel('Loss Value')
            plt.title('Physics-Constrained Loss Components')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.yscale('log')  # 使用对数刻度

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'physics_loss_components.png'), dpi=300, bbox_inches='tight')
            plt.close()

    # 3. 学习率调度
    if metrics['learning_rates']:
        plt.figure(figsize=(12, 6))
        plt.plot(epochs, metrics['learning_rates'], 'purple', linewidth=2)
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.title('Learning Rate Schedule')
        plt.grid(True, alpha=0.3)
        plt.yscale('log')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'learning_rate.png'), dpi=300, bbox_inches='tight')
        plt.close()

    # 4. FID和IS评估结果
    if metrics['fid_scores'] and metrics['is_scores']:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # FID分数
        ax1.plot(metrics['eval_epochs'], metrics['fid_scores'], 'ro-', linewidth=2, markersize=6)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('FID Score')
        ax1.set_title('FID Score (Lower is Better)')
        ax1.grid(True, alpha=0.3)

        # 添加目标线
        ax1.axhline(y=78, color='green', linestyle='--', alpha=0.7, label='Target FID (78)')
        ax1.legend()

        # IS分数
        ax2.plot(metrics['eval_epochs'], metrics['is_scores'], 'bo-', linewidth=2, markersize=6)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('IS Score')
        ax2.set_title('Inception Score (Higher is Better)')
        ax2.grid(True, alpha=0.3)

        # 添加目标线
        ax2.axhline(y=2.0, color='green', linestyle='--', alpha=0.7, label='Target IS (2.0)')
        ax2.axhline(y=2.5, color='green', linestyle='--', alpha=0.7, label='Target IS (2.5)')
        ax2.legend()

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'evaluation_metrics.png'), dpi=300, bbox_inches='tight')
        plt.close()

    # 5. 性能监控
    if metrics['ddim_sampling_times'] or metrics['pulsar_adaptive_sampling_times'] or metrics['gpu_memory_usage'] or metrics['training_times']:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 采样时间对比
        if metrics['ddim_sampling_times'] or metrics['pulsar_adaptive_sampling_times']:
            if metrics['ddim_sampling_times']:
                axes[0, 0].plot(range(len(metrics['ddim_sampling_times'])), metrics['ddim_sampling_times'], 'g-', linewidth=2, label='DDIM')
            if metrics['pulsar_adaptive_sampling_times']:
                axes[0, 0].plot(range(len(metrics['pulsar_adaptive_sampling_times'])), metrics['pulsar_adaptive_sampling_times'], 'b-', linewidth=2, label='PulsarAdaptive')
            axes[0, 0].set_title('Sampling Time Comparison')
            axes[0, 0].set_xlabel('Evaluation')
            axes[0, 0].set_ylabel('Time (s)')
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].legend()

        # GPU内存使用
        if metrics['gpu_memory_usage']:
            axes[0, 1].plot(range(len(metrics['gpu_memory_usage'])), metrics['gpu_memory_usage'], 'r-', linewidth=2)
            axes[0, 1].axhline(y=40, color='red', linestyle='--', alpha=0.7, label='A100 Total (40GB)')
            axes[0, 1].set_title('GPU Memory Usage')
            axes[0, 1].set_xlabel('Training Step')
            axes[0, 1].set_ylabel('Memory (GB)')
            axes[0, 1].grid(True, alpha=0.3)
            axes[0, 1].legend()

        # 训练时间
        if metrics['training_times']:
            axes[1, 0].plot(range(len(metrics['training_times'])), metrics['training_times'], 'b-', linewidth=2)
            axes[1, 0].set_title('Epoch Training Time')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Time (s)')
            axes[1, 0].grid(True, alpha=0.3)

        # 训练效率统计
        if metrics['training_times']:
            avg_time = np.mean(metrics['training_times'])
            axes[1, 1].bar(['Avg Epoch Time', 'Min Epoch Time', 'Max Epoch Time'],
                          [avg_time, min(metrics['training_times']), max(metrics['training_times'])],
                          color=['blue', 'green', 'red'], alpha=0.7)
            axes[1, 1].set_title('Training Time Statistics')
            axes[1, 1].set_ylabel('Time (s)')
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'performance_monitoring.png'), dpi=300, bbox_inches='tight')
        plt.close()

    print(f"图表已保存到: {output_dir}")


def print_training_summary(metrics: Dict):
    """打印训练摘要"""
    if not metrics or not metrics['epochs']:
        print("没有训练数据")
        return

    print("\n" + "=" * 60)
    print("🚀 PulsarTransformerDDPM 训练摘要")
    print("=" * 60)

    # 基本信息
    total_epochs = len(metrics['epochs'])
    current_epoch = max(metrics['epochs']) if metrics['epochs'] else 0
    current_loss = metrics['training_losses'][-1] if metrics['training_losses'] else 0
    current_stage = metrics['training_stages'][-1] if metrics['training_stages'] else "unknown"

    print(f"当前进度: Epoch {current_epoch}")
    print(f"训练阶段: {current_stage}")
    print(f"当前损失: {current_loss:.6f}")

    # 学习率
    if metrics['learning_rates']:
        current_lr = metrics['learning_rates'][-1]
        print(f"当前学习率: {current_lr:.2e}")

    # 评估结果
    if metrics['fid_scores'] and metrics['is_scores']:
        latest_fid = metrics['fid_scores'][-1]
        latest_is = metrics['is_scores'][-1]
        latest_eval_epoch = metrics['eval_epochs'][-1]

        print(f"\n最新评估结果 (Epoch {latest_eval_epoch}):")
        print(f"  FID Score: {latest_fid:.2f} (目标: 78±8)")
        print(f"  IS Score: {latest_is:.2f} (目标: 2.0-2.5)")

        # 目标达成情况
        fid_target_met = 70 <= latest_fid <= 86
        is_target_met = 2.0 <= latest_is <= 2.5

        print(f"  FID目标达成: {'✅' if fid_target_met else '❌'}")
        print(f"  IS目标达成: {'✅' if is_target_met else '❌'}")

    # 性能统计
    if metrics['training_times']:
        avg_epoch_time = np.mean(metrics['training_times'])
        print(f"\n性能统计:")
        print(f"  平均每轮训练时间: {avg_epoch_time:.1f}s")

        if metrics['ddim_sampling_times']:
            avg_ddim_time = np.mean(metrics['ddim_sampling_times'])
            print(f"  平均DDIM采样时间: {avg_ddim_time:.2f}s")

        if metrics['pulsar_adaptive_sampling_times']:
            avg_pulsar_time = np.mean(metrics['pulsar_adaptive_sampling_times'])
            print(f"  平均PulsarAdaptive采样时间: {avg_pulsar_time:.2f}s")

            # 如果两种采样方法都有数据，显示性能对比
            if metrics['ddim_sampling_times']:
                speedup = avg_ddim_time / avg_pulsar_time if avg_pulsar_time > 0 else 0
                print(f"  PulsarAdaptive加速比: {speedup:.1f}x")

    print("=" * 60)


def main():
    """主函数"""
    args = parse_args()

    # 设置输出目录
    output_dir = args.output_dir if args.output_dir else os.path.join(args.log_dir, "monitoring_plots")

    print("🔍 PulsarTransformerDDPM 训练监控启动")
    print(f"监控目录: {args.log_dir}")
    print(f"图表输出: {output_dir}")
    print(f"刷新间隔: {args.refresh_interval}s")
    print("-" * 60)

    while True:
        try:
            # 查找最新日志文件
            log_file = find_latest_log(args.log_dir)
            if not log_file:
                print(f"在 {args.log_dir} 中未找到日志文件")
                if not args.auto_refresh:
                    break
                time.sleep(args.refresh_interval)
                continue

            print(f"监控日志文件: {log_file}")

            # 解析日志
            metrics = parse_pulsar_log(log_file)

            if metrics and metrics['epochs']:
                # 绘制图表
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                plot_dir = os.path.join(output_dir, f"plots_{timestamp}")
                plot_training_progress(metrics, plot_dir)

                # 打印摘要
                print_training_summary(metrics)
            else:
                print("日志文件中暂无训练数据")

            if not args.auto_refresh:
                break

            print(f"\n等待 {args.refresh_interval} 秒后刷新...")
            time.sleep(args.refresh_interval)

        except KeyboardInterrupt:
            print("\n监控已停止")
            break
        except Exception as e:
            print(f"监控过程中发生错误: {e}")
            if not args.auto_refresh:
                break
            time.sleep(args.refresh_interval)


if __name__ == "__main__":
    main()
