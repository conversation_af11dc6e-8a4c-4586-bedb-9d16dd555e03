#!/usr/bin/env python3
"""
使用PulsarAdaptiveSampler的评估脚本
专门为PulsarTransformerDDPM + PulsarAdaptiveSampler组合设计的评估工具
"""

import os
import sys
import torch
import argparse
import logging
import time
from pathlib import Path

# 添加项目路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入模块
from models import PulsarTransformerDDPM, PulsarAdaptiveSampler
from evaluation.metrics import evaluate_model, generate_samples_for_evaluation
from data.dataloader import get_dataloader

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="PulsarAdaptiveSampler评估脚本")
    
    # 模型参数
    parser.add_argument("--model_path", type=str, required=True,
                       help="训练好的模型检查点路径")
    parser.add_argument("--data_dir", type=str,
                       default="/Pulsar/PulsarFenlei/data/htru1-batches-py",
                       help="HTRU1数据集路径")
    
    # 采样参数
    parser.add_argument("--num_samples", type=int, default=995,
                       help="生成样本数量 (默认995匹配HTRU1训练集正样本)")
    parser.add_argument("--batch_size", type=int, default=32,
                       help="采样批次大小")
    parser.add_argument("--num_inference_steps", type=int, default=20,
                       help="推理步数")
    
    # 评估参数
    parser.add_argument("--eval_batch_size", type=int, default=32,
                       help="评估批次大小")
    parser.add_argument("--inception_model_path", type=str,
                       default="evaluation/inception_v3_google-0cc3c7bd.pth",
                       help="Inception模型路径")
    
    # 输出参数
    parser.add_argument("--output_dir", type=str, default="evaluation_results",
                       help="输出目录")
    parser.add_argument("--save_samples", action="store_true",
                       help="是否保存生成的样本")
    
    # 设备参数
    parser.add_argument("--device", type=str, default="cuda",
                       help="计算设备")
    parser.add_argument("--seed", type=int, default=42,
                       help="随机种子")
    
    return parser.parse_args()


def setup_device_and_seed(device: str, seed: int):
    """设置设备和随机种子"""
    # 设置随机种子
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    
    # 设置设备
    if device == "cuda" and torch.cuda.is_available():
        device = torch.device("cuda")
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"使用GPU: {gpu_name} ({gpu_memory:.1f}GB)")
    else:
        device = torch.device("cpu")
        logger.warning("使用CPU，性能可能较慢")
    
    return device


def load_model_and_sampler(model_path: str, device: torch.device):
    """加载模型和创建采样器"""
    logger.info(f"加载模型: {model_path}")
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location=device)
    
    # 获取模型配置
    if 'model_config' in checkpoint:
        config = checkpoint['model_config']
    else:
        # 使用默认配置
        logger.warning("检查点中未找到模型配置，使用默认配置")
        config = {
            'img_channels': 3,
            'base_channels': 48,
            'channel_mults': (1, 2, 3, 4),
            'num_res_blocks': 2,
            'transformer_dim': 96,
            'num_heads': 4,
            'num_timesteps': 50,
            'dropout': 0.1
        }
    
    # 创建模型
    model = PulsarTransformerDDPM(
        img_channels=config.get('img_channels', 3),
        base_channels=config.get('base_channels', 48),
        channel_mults=config.get('channel_mults', (1, 2, 3, 4)),
        num_res_blocks=config.get('num_res_blocks', 2),
        transformer_dim=config.get('transformer_dim', 96),
        num_heads=config.get('num_heads', 4),
        num_timesteps=config.get('num_timesteps', 50),
        dropout=config.get('dropout', 0.1),
        device=device
    ).to(device)
    
    # 加载模型权重
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    elif 'state_dict' in checkpoint:
        model.load_state_dict(checkpoint['state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    
    # 获取模型信息
    model_info = model.get_model_info()
    logger.info(f"模型参数量: {model_info['total_parameters']:,} ({model_info['total_parameters_M']:.2f}M)")
    
    # 创建PulsarAdaptiveSampler
    sampler = PulsarAdaptiveSampler(
        model=model,
        num_timesteps=config.get('num_timesteps', 50),
        device=device,
        numerical_stability=True,
        pulsar_optimization=True,
        channel_aware=True
    )
    
    logger.info("PulsarAdaptiveSampler创建成功")
    
    return model, sampler


def load_real_data(data_dir: str, num_samples: int, device: torch.device):
    """加载真实数据用于FID计算"""
    logger.info(f"加载真实数据: {data_dir}")
    
    # 创建数据加载器
    dataloader = get_dataloader(
        root=data_dir,
        batch_size=64,
        train=True,
        augment=False,
        num_workers=4,
        shuffle=False,
        drop_last=False,
        resolution=32
    )
    
    # 收集真实样本
    real_samples = []
    total_collected = 0
    
    for batch_data, batch_labels in dataloader:
        # 只使用正样本 (label=0 是脉冲星)
        positive_mask = (batch_labels == 0)
        positive_samples = batch_data[positive_mask]
        
        if len(positive_samples) > 0:
            real_samples.append(positive_samples)
            total_collected += len(positive_samples)
            
            if total_collected >= num_samples:
                break
    
    if real_samples:
        real_samples = torch.cat(real_samples, dim=0)[:num_samples]
        logger.info(f"收集到 {len(real_samples)} 个真实正样本")
    else:
        logger.error("未找到正样本")
        raise ValueError("数据集中没有正样本")
    
    return real_samples.to(device)


def main():
    """主评估函数"""
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置设备和随机种子
    device = setup_device_and_seed(args.device, args.seed)
    
    logger.info("=" * 60)
    logger.info("🚀 PulsarAdaptiveSampler评估开始")
    logger.info("=" * 60)
    logger.info(f"模型路径: {args.model_path}")
    logger.info(f"数据路径: {args.data_dir}")
    logger.info(f"生成样本数: {args.num_samples}")
    logger.info(f"推理步数: {args.num_inference_steps}")
    logger.info(f"输出目录: {args.output_dir}")
    
    try:
        # 1. 加载模型和创建采样器
        model, sampler = load_model_and_sampler(args.model_path, device)
        
        # 2. 加载真实数据
        real_images = load_real_data(args.data_dir, args.num_samples, device)
        
        # 3. 生成样本
        logger.info("开始生成样本...")
        start_time = time.time()
        
        generated_images = generate_samples_for_evaluation(
            model=model,
            num_samples=args.num_samples,
            batch_size=args.batch_size,
            device=str(device),
            sampler=sampler,
            num_inference_steps=args.num_inference_steps
        )
        
        generation_time = time.time() - start_time
        logger.info(f"样本生成完成，耗时: {generation_time:.2f}秒")
        logger.info(f"生成速度: {args.num_samples/generation_time:.2f} samples/s")
        
        # 4. 保存样本（如果需要）
        if args.save_samples:
            samples_path = os.path.join(args.output_dir, "generated_samples.pt")
            torch.save(generated_images, samples_path)
            logger.info(f"样本已保存到: {samples_path}")
        
        # 5. 评估指标
        logger.info("开始计算FID和IS指标...")
        evaluation_start = time.time()
        
        results = evaluate_model(
            real_images=real_images,
            generated_images=generated_images,
            inception_model_path=args.inception_model_path,
            batch_size=args.eval_batch_size,
            device=str(device),
            num_samples=args.num_samples
        )
        
        evaluation_time = time.time() - evaluation_start
        
        # 6. 输出结果
        logger.info("=" * 60)
        logger.info("🎯 评估结果")
        logger.info("=" * 60)
        logger.info(f"FID分数: {results['fid']:.4f}")
        logger.info(f"IS分数: {results['is_mean']:.4f} ± {results['is_std']:.4f}")
        logger.info(f"生成时间: {generation_time:.2f}秒")
        logger.info(f"评估时间: {evaluation_time:.2f}秒")
        logger.info(f"总时间: {generation_time + evaluation_time:.2f}秒")
        
        # 7. 保存结果
        results_path = os.path.join(args.output_dir, "evaluation_results.txt")
        with open(results_path, 'w') as f:
            f.write("PulsarAdaptiveSampler评估结果\n")
            f.write("=" * 40 + "\n")
            f.write(f"模型路径: {args.model_path}\n")
            f.write(f"生成样本数: {args.num_samples}\n")
            f.write(f"推理步数: {args.num_inference_steps}\n")
            f.write(f"FID分数: {results['fid']:.4f}\n")
            f.write(f"IS分数: {results['is_mean']:.4f} ± {results['is_std']:.4f}\n")
            f.write(f"生成时间: {generation_time:.2f}秒\n")
            f.write(f"评估时间: {evaluation_time:.2f}秒\n")
            f.write(f"生成速度: {args.num_samples/generation_time:.2f} samples/s\n")
        
        logger.info(f"结果已保存到: {results_path}")
        
        # 8. 性能评估
        logger.info("\n📊 性能评估:")
        if results['fid'] < 40:
            logger.info("✅ FID分数优秀 (< 40)")
        elif results['fid'] < 80:
            logger.info("⚠️ FID分数一般 (40-80)")
        else:
            logger.info("❌ FID分数需要改进 (> 80)")
        
        if results['is_mean'] > 5:
            logger.info("✅ IS分数优秀 (> 5)")
        elif results['is_mean'] > 2:
            logger.info("⚠️ IS分数一般 (2-5)")
        else:
            logger.info("❌ IS分数需要改进 (< 2)")
        
        logger.info("=" * 60)
        logger.info("🎉 评估完成！")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"评估过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise
    
    finally:
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


if __name__ == "__main__":
    main()
