#!/usr/bin/env python3
"""
调试版训练脚本
用于诊断阶段2训练冻结问题
"""

import sys
import os
sys.path.insert(0, os.getcwd())

import torch
import time
import signal
from datetime import datetime

from training.full_trainer import TrainingConfig, create_training_pipeline
from utils.random_seed import set_random_seed

class TimeoutException(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutException("操作超时")

def debug_train_step(trainer, batch, step_timeout=30):
    """带超时的训练步骤"""
    print(f"    🔄 开始训练步骤 (超时: {step_timeout}s)")
    
    # 设置超时
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(step_timeout)
    
    try:
        start_time = time.time()
        metrics = trainer.train_step(batch)
        elapsed = time.time() - start_time
        
        print(f"    ✅ 训练步骤完成 ({elapsed:.2f}s)")
        print(f"    📊 指标: {list(metrics.keys())}")
        
        # 取消超时
        signal.alarm(0)
        return metrics
        
    except TimeoutException:
        print(f"    ❌ 训练步骤超时 (>{step_timeout}s)")
        signal.alarm(0)
        raise
    except Exception as e:
        print(f"    💥 训练步骤出错: {e}")
        signal.alarm(0)
        raise

def debug_training():
    """调试训练流程"""
    print("🔍 开始调试训练流程")
    print("=" * 60)
    
    # 设置随机种子
    set_random_seed(42)
    print("✅ 随机种子设置完成")
    
    # 创建配置
    config = TrainingConfig(
        latent_dim=64,
        batch_size=2,  # 使用更小的批次
        device='cuda',
        random_seed=42,
        vae_lr=5e-5,
        discriminator_lr=1e-4,
        stage1_epochs=1,
        stage2_epochs=1,
        stage3_epochs=1,
        data_augment=False,
        augment_factor=1,
        num_workers=0,  # 避免多进程问题
        save_dir='./debug_checkpoints',
        save_interval=100,  # 不保存检查点
        log_interval=1
    )
    print("✅ 训练配置创建完成")
    
    # 创建训练管道
    print("\n🏗️ 创建训练管道...")
    pipeline = create_training_pipeline(config)
    
    # 设置数据
    print("\n📊 设置数据加载器...")
    try:
        pipeline.setup_data()
        print(f"✅ 数据加载器设置完成: {len(pipeline.dataloader)} 批次")
    except Exception as e:
        print(f"❌ 数据设置失败: {e}")
        return False
    
    # 设置模型
    print("\n🧠 设置模型...")
    try:
        pipeline.setup_model()
        print(f"✅ 模型设置完成: {pipeline.model.total_params/1e6:.2f}M 参数")
    except Exception as e:
        print(f"❌ 模型设置失败: {e}")
        return False
    
    # 调试三阶段训练
    stages = [
        (1, "VAE预训练"),
        (2, "WGAN-GP集成"), 
        (3, "联合优化")
    ]
    
    for stage_num, stage_name in stages:
        print(f"\n🎯 调试阶段{stage_num}: {stage_name}")
        print("-" * 40)
        
        try:
            # 设置训练阶段
            print(f"  🔧 设置训练阶段{stage_num}...")
            pipeline.trainer.set_stage(stage_num)
            print(f"  ✅ 阶段{stage_num}设置完成")
            
            # 获取一个批次进行测试
            print(f"  📦 获取测试批次...")
            test_batch = next(iter(pipeline.dataloader))
            print(f"  ✅ 测试批次获取完成: {test_batch.shape}")
            
            # 执行几个训练步骤
            for step in range(3):  # 只测试3步
                print(f"  📍 步骤 {step+1}/3:")
                
                try:
                    metrics = debug_train_step(pipeline.trainer, test_batch, step_timeout=60)
                    
                    # 打印关键指标
                    key_metrics = {}
                    for key, value in metrics.items():
                        if isinstance(value, (int, float)):
                            key_metrics[key] = f"{value:.6f}"
                    
                    print(f"    📈 关键指标: {key_metrics}")
                    
                except TimeoutException:
                    print(f"    ⏰ 阶段{stage_num}步骤{step+1}超时，可能存在死锁")
                    return False
                except Exception as e:
                    print(f"    💥 阶段{stage_num}步骤{step+1}出错: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            
            print(f"  🎉 阶段{stage_num}调试完成")
            
        except Exception as e:
            print(f"  💥 阶段{stage_num}设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    print("\n🎉 所有阶段调试完成！")
    return True

def main():
    """主函数"""
    print(f"🚀 开始WGAN-GP+VAE训练调试")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"🖥️ GPU: {torch.cuda.get_device_name(0)}")
        print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB")
    else:
        print("⚠️ 使用CPU训练")
    
    try:
        success = debug_training()
        
        if success:
            print("\n✅ 调试成功！训练流程正常")
            return 0
        else:
            print("\n❌ 调试失败！发现问题")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 调试被用户中断")
        return 130
    except Exception as e:
        print(f"\n💥 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
