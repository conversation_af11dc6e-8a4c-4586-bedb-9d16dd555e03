2025-05-27 15:13:43,881 - __main__ - INFO - 日志保存到: outputs_a100_fixed/training_20250527_151343.log
2025-05-27 15:13:43,892 - __main__ - INFO - 配置保存到: outputs_a100_fixed/config.yaml
2025-05-27 15:13:43,898 - utils.seed_utils - INFO - Setting random seed to 42
2025-05-27 15:13:46,956 - utils.seed_utils - INFO - Random seed 42 set successfully for all libraries
2025-05-27 15:13:46,957 - utils.seed_utils - INFO - Deterministic mode enabled - results should be reproducible
2025-05-27 15:13:46,957 - __main__ - INFO - 使用统一随机种子管理工具设置种子: 42
2025-05-27 15:13:46,957 - __main__ - INFO - 确定性模式已启用，禁用CUDNN基准模式以确保可重现性
2025-05-27 15:13:47,117 - __main__ - INFO - 使用GPU: NVIDIA A100-SXM4-40GB (39.4GB)
2025-05-27 15:13:47,118 - __main__ - INFO - ✅ 已启用TF32优化 (A100 GPU加速20-50%)
2025-05-27 15:13:47,118 - __main__ - INFO - 设备: cuda, 随机种子: 42
2025-05-27 15:13:47,118 - __main__ - INFO - ================================================================================
2025-05-27 15:13:47,118 - __main__ - INFO - 🚀 SimplifiedCNNUNet A100 GPU训练开始
2025-05-27 15:13:47,118 - __main__ - INFO - ================================================================================
2025-05-27 15:13:47,119 - __main__ - INFO - 输出目录: outputs_a100_fixed
2025-05-27 15:13:47,119 - __main__ - INFO - 数据目录: /Pulsar/PulsarFenlei/data/htru1-batches-py
2025-05-27 15:13:47,119 - __main__ - INFO - 最大训练轮数: 400
2025-05-27 15:13:47,119 - __main__ - INFO - 批次大小: 16
2025-05-27 15:13:47,119 - __main__ - INFO - 混合精度: True
2025-05-27 15:13:47,119 - __main__ - INFO - 创建SimplifiedCNNUNet模型...
2025-05-27 15:13:52,352 - __main__ - INFO - 模型参数量: 2,295,030 (2.30M)
2025-05-27 15:13:52,352 - __main__ - INFO - 模型架构: SimplifiedCNNUNet
2025-05-27 15:13:52,353 - __main__ - INFO -   输入通道: 3
2025-05-27 15:13:52,353 - __main__ - INFO -   基础通道: 48
2025-05-27 15:13:52,353 - __main__ - INFO -   时间嵌入维度: 120
2025-05-27 15:13:52,353 - __main__ - INFO -   Dropout: 0.2
2025-05-27 15:13:52,354 - __main__ - INFO - 可训练参数: 2,295,030 (2.30M)
2025-05-27 15:13:54,498 - __main__ - INFO - 模型编译成功 (PyTorch 2.0+)
2025-05-27 15:13:54,498 - __main__ - INFO - 创建数据加载器...
2025-05-27 15:13:54,498 - __main__ - WARNING - 指定的数据路径不存在: /Pulsar/PulsarFenlei/data/htru1-batches-py
2025-05-27 15:13:54,498 - __main__ - INFO - 尝试自动查找HTRU1数据集...
2025-05-27 15:13:54,499 - __main__ - INFO - ✅ 找到完整的HTRU1数据集: /yanyb/jmy/Pulsar/PulsarFenlei/data/htru1-batches-py
2025-05-27 15:13:54,499 - __main__ - INFO - 自动找到数据路径: /yanyb/jmy/Pulsar/PulsarFenlei/data/htru1-batches-py
2025-05-27 15:13:55,473 - __main__ - INFO - 🚀 集成数据处理优化组件...
2025-05-27 15:13:55,473 - data.integrated_data_pipeline - INFO - 集成数据处理器初始化完成
2025-05-27 15:13:55,473 - data.integrated_data_pipeline - INFO -   归一化方法: positive_sample_based
2025-05-27 15:13:55,473 - data.integrated_data_pipeline - INFO -   数据增强: 启用
2025-05-27 15:13:55,473 - data.integrated_data_pipeline - INFO -   物理特性保持: 启用
2025-05-27 15:13:55,473 - data.integrated_data_pipeline - INFO - 增强版HTRU1数据集初始化完成
2025-05-27 15:13:55,473 - data.integrated_data_pipeline - INFO -   原始数据集大小: 995
2025-05-27 15:13:55,473 - data.integrated_data_pipeline - INFO - 增强版数据加载器创建成功
2025-05-27 15:13:55,474 - data.integrated_data_pipeline - INFO -   批次大小: 16
2025-05-27 15:13:55,474 - data.integrated_data_pipeline - INFO -   工作进程数: 8
2025-05-27 15:13:55,474 - __main__ - INFO - ✅ 使用增强版数据处理管道
2025-05-27 15:13:55,474 - __main__ - INFO - 训练数据: 62 批次, 批次大小: 16
2025-05-27 15:13:55,474 - __main__ - INFO - 总样本数: 995
2025-05-27 15:13:55,474 - __main__ - INFO - 🚀 使用OptimizedPulsarLoss (203倍改善)
2025-05-27 15:13:55,474 - train.loss_integration_adapter - INFO - 🚀 使用OptimizedPulsarLoss (203倍改善)
2025-05-27 15:13:55,474 - train.loss_integration_adapter - INFO -   优化权重配置: MSE=0.8, Physics=0.05, Consistency=0.15
2025-05-27 15:13:55,474 - train.loss_integration_adapter - INFO -   损失缩放: True
2025-05-27 15:13:55,475 - train.loss_integration_adapter - INFO -   自适应权重: True
2025-05-27 15:13:55,475 - train.loss_integration_adapter - INFO - ✅ 损失函数集成适配器创建成功
2025-05-27 15:13:55,475 - train.loss_integration_adapter - INFO -    优化模式: 启用
2025-05-27 15:13:55,475 - train.loss_integration_adapter - INFO -    设备: cuda
2025-05-27 15:13:55,570 - models.pulsar_adaptive_sampler - INFO - 噪声调度初始化: alpha_cumprod范围[0.000010, 0.999844]
2025-05-27 15:13:55,570 - models.pulsar_adaptive_sampler - INFO - 调整设备一致性: cuda -> cuda:0
2025-05-27 15:13:55,571 - models.pulsar_adaptive_sampler - INFO - PulsarAdaptiveSampler初始化完成: timesteps=300, device=cuda
2025-05-27 15:13:55,571 - __main__ - INFO - PulsarAdaptiveSampler创建成功，将替代DPM-Solver++
2025-05-27 15:13:55,571 - train.loss_integration_adapter - INFO - 🚀 使用OptimizedPulsarLoss (203倍改善)
2025-05-27 15:13:55,571 - train.loss_integration_adapter - INFO -   优化权重配置: MSE=0.8, Physics=0.05, Consistency=0.15
2025-05-27 15:13:55,572 - train.loss_integration_adapter - INFO -   损失缩放: True
2025-05-27 15:13:55,572 - train.loss_integration_adapter - INFO -   自适应权重: True
2025-05-27 15:13:55,572 - train.loss_integration_adapter - INFO - ✅ 损失函数集成适配器创建成功
2025-05-27 15:13:55,572 - train.loss_integration_adapter - INFO -    优化模式: 启用
2025-05-27 15:13:55,572 - train.loss_integration_adapter - INFO -    设备: cuda
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - 使用优化损失函数，初始阶段: basic
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - ============================================================
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - 渐进式三阶段训练器初始化完成
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - ============================================================
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - 阶段1 (基础扩散): 150 epochs, LR factor: 1.0
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - 阶段2 (物理约束): 150 epochs, LR factor: 0.9
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - 阶段3 (精细调优): 100 epochs, LR factor: 0.8
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - 总训练轮数: 400
2025-05-27 15:13:55,581 - train.progressive_trainer - INFO - ============================================================
2025-05-27 15:13:55,581 - __main__ - INFO - 开始三阶段渐进式训练...
2025-05-27 15:14:54,887 - train.progressive_trainer - INFO - [0/62] Epoch 0/400 Stage 1 lr:0.000016 total_loss:0.959724
2025-05-27 15:15:25,862 - train.progressive_trainer - INFO - [50/62] Epoch 0/400 Stage 1 lr:0.000078 total_loss:0.503506
2025-05-27 15:15:27,933 - train.progressive_trainer - INFO - [61/62] Epoch 0/400 Stage 1 lr:0.000077 total_loss:0.438796
2025-05-27 15:15:29,658 - train.progressive_trainer - INFO - [0/62] Epoch 1/400 Stage 1 lr:0.000077 total_loss:0.446136
2025-05-27 15:15:39,116 - train.progressive_trainer - INFO - [50/62] Epoch 1/400 Stage 1 lr:0.000069 total_loss:0.209089
2025-05-27 15:15:40,918 - train.progressive_trainer - INFO - [61/62] Epoch 1/400 Stage 1 lr:0.000067 total_loss:0.188549
2025-05-27 15:15:42,021 - train.progressive_trainer - INFO - [0/62] Epoch 2/400 Stage 1 lr:0.000066 total_loss:0.186396
2025-05-27 15:15:50,818 - train.progressive_trainer - INFO - [50/62] Epoch 2/400 Stage 1 lr:0.000055 total_loss:0.147236
2025-05-27 15:15:52,513 - train.progressive_trainer - INFO - [61/62] Epoch 2/400 Stage 1 lr:0.000052 total_loss:0.144030
2025-05-27 15:15:53,756 - train.progressive_trainer - INFO - [0/62] Epoch 3/400 Stage 1 lr:0.000052 total_loss:0.162190
2025-05-27 15:16:02,184 - train.progressive_trainer - INFO - [50/62] Epoch 3/400 Stage 1 lr:0.000039 total_loss:0.125683
2025-05-27 15:16:04,065 - train.progressive_trainer - INFO - [61/62] Epoch 3/400 Stage 1 lr:0.000037 total_loss:0.126622
2025-05-27 15:16:05,454 - train.progressive_trainer - INFO - [0/62] Epoch 4/400 Stage 1 lr:0.000036 total_loss:0.130125
2025-05-27 15:16:14,312 - train.progressive_trainer - INFO - [50/62] Epoch 4/400 Stage 1 lr:0.000026 total_loss:0.114819
2025-05-27 15:16:16,420 - train.progressive_trainer - INFO - [61/62] Epoch 4/400 Stage 1 lr:0.000024 total_loss:0.135015
2025-05-27 15:16:17,247 - train.progressive_trainer - INFO - [0/62] Epoch 5/400 Stage 1 lr:0.000024 total_loss:0.114507
2025-05-27 15:16:25,716 - train.progressive_trainer - INFO - [50/62] Epoch 5/400 Stage 1 lr:0.000018 total_loss:0.109813
2025-05-27 15:16:27,617 - train.progressive_trainer - INFO - [61/62] Epoch 5/400 Stage 1 lr:0.000017 total_loss:0.106404
2025-05-27 15:16:28,366 - train.progressive_trainer - INFO - [0/62] Epoch 6/400 Stage 1 lr:0.000017 total_loss:0.108758
2025-05-27 15:16:37,513 - train.progressive_trainer - INFO - [50/62] Epoch 6/400 Stage 1 lr:0.000016 total_loss:0.109308
2025-05-27 15:16:39,609 - train.progressive_trainer - INFO - [61/62] Epoch 6/400 Stage 1 lr:0.000016 total_loss:0.104265
2025-05-27 15:16:40,622 - train.progressive_trainer - INFO - [0/62] Epoch 7/400 Stage 1 lr:0.000016 total_loss:0.104799
2025-05-27 15:16:49,913 - train.progressive_trainer - INFO - [50/62] Epoch 7/400 Stage 1 lr:0.000016 total_loss:0.111284
2025-05-27 15:16:51,705 - train.progressive_trainer - INFO - [61/62] Epoch 7/400 Stage 1 lr:0.000016 total_loss:0.109080
2025-05-27 15:16:52,758 - train.progressive_trainer - INFO - [0/62] Epoch 8/400 Stage 1 lr:0.000016 total_loss:0.113299
2025-05-27 15:17:01,613 - train.progressive_trainer - INFO - [50/62] Epoch 8/400 Stage 1 lr:0.000016 total_loss:0.099977
2025-05-27 15:17:03,727 - train.progressive_trainer - INFO - [61/62] Epoch 8/400 Stage 1 lr:0.000016 total_loss:0.095429
2025-05-27 15:17:05,520 - train.progressive_trainer - INFO - [0/62] Epoch 9/400 Stage 1 lr:0.000016 total_loss:0.096754
2025-05-27 15:17:13,720 - train.progressive_trainer - INFO - [50/62] Epoch 9/400 Stage 1 lr:0.000016 total_loss:0.105015
2025-05-27 15:17:15,615 - train.progressive_trainer - INFO - [61/62] Epoch 9/400 Stage 1 lr:0.000016 total_loss:0.093613
2025-05-27 15:17:16,459 - train.progressive_trainer - INFO - [0/62] Epoch 10/400 Stage 1 lr:0.000016 total_loss:0.092114
2025-05-27 15:17:24,324 - train.progressive_trainer - INFO - [50/62] Epoch 10/400 Stage 1 lr:0.000016 total_loss:0.101954
2025-05-27 15:17:26,068 - train.progressive_trainer - INFO - [61/62] Epoch 10/400 Stage 1 lr:0.000016 total_loss:0.100370
2025-05-27 15:17:27,257 - train.progressive_trainer - INFO - [0/62] Epoch 11/400 Stage 1 lr:0.000016 total_loss:0.091569
2025-05-27 15:17:35,822 - train.progressive_trainer - INFO - [50/62] Epoch 11/400 Stage 1 lr:0.000016 total_loss:0.100025
2025-05-27 15:17:37,912 - train.progressive_trainer - INFO - [61/62] Epoch 11/400 Stage 1 lr:0.000016 total_loss:0.085506
2025-05-27 15:17:38,958 - train.progressive_trainer - INFO - [0/62] Epoch 12/400 Stage 1 lr:0.000016 total_loss:0.087633
2025-05-27 15:17:47,205 - train.progressive_trainer - INFO - [50/62] Epoch 12/400 Stage 1 lr:0.000016 total_loss:0.086815
2025-05-27 15:17:49,130 - train.progressive_trainer - INFO - [61/62] Epoch 12/400 Stage 1 lr:0.000016 total_loss:0.083230
2025-05-27 15:17:50,279 - train.progressive_trainer - INFO - [0/62] Epoch 13/400 Stage 1 lr:0.000016 total_loss:0.082332
2025-05-27 15:17:58,314 - train.progressive_trainer - INFO - [50/62] Epoch 13/400 Stage 1 lr:0.000016 total_loss:0.089650
2025-05-27 15:17:59,948 - train.progressive_trainer - INFO - [61/62] Epoch 13/400 Stage 1 lr:0.000016 total_loss:0.080877
2025-05-27 15:18:01,323 - train.progressive_trainer - INFO - [0/62] Epoch 14/400 Stage 1 lr:0.000016 total_loss:0.080066
2025-05-27 15:18:09,813 - train.progressive_trainer - INFO - [50/62] Epoch 14/400 Stage 1 lr:0.000016 total_loss:0.100667
2025-05-27 15:18:11,899 - train.progressive_trainer - INFO - [61/62] Epoch 14/400 Stage 1 lr:0.000016 total_loss:0.076520
2025-05-27 15:18:12,850 - train.progressive_trainer - INFO - [0/62] Epoch 15/400 Stage 1 lr:0.000016 total_loss:0.075391
2025-05-27 15:18:21,436 - train.progressive_trainer - INFO - [50/62] Epoch 15/400 Stage 1 lr:0.000016 total_loss:0.077528
2025-05-27 15:18:23,116 - train.progressive_trainer - INFO - [61/62] Epoch 15/400 Stage 1 lr:0.000016 total_loss:0.075459
2025-05-27 15:18:23,918 - train.progressive_trainer - INFO - [0/62] Epoch 16/400 Stage 1 lr:0.000016 total_loss:0.074503
2025-05-27 15:18:33,311 - train.progressive_trainer - INFO - [50/62] Epoch 16/400 Stage 1 lr:0.000016 total_loss:0.074285
2025-05-27 15:18:35,226 - train.progressive_trainer - INFO - [61/62] Epoch 16/400 Stage 1 lr:0.000016 total_loss:0.073417
2025-05-27 15:18:36,460 - train.progressive_trainer - INFO - [0/62] Epoch 17/400 Stage 1 lr:0.000016 total_loss:0.070231
2025-05-27 15:18:44,707 - train.progressive_trainer - INFO - [50/62] Epoch 17/400 Stage 1 lr:0.000016 total_loss:0.068885
2025-05-27 15:18:45,984 - train.progressive_trainer - INFO - [61/62] Epoch 17/400 Stage 1 lr:0.000016 total_loss:0.069475
2025-05-27 15:18:47,006 - train.progressive_trainer - INFO - [0/62] Epoch 18/400 Stage 1 lr:0.000016 total_loss:0.074681
2025-05-27 15:18:56,715 - train.progressive_trainer - INFO - [50/62] Epoch 18/400 Stage 1 lr:0.000016 total_loss:0.065746
2025-05-27 15:18:58,130 - train.progressive_trainer - INFO - [61/62] Epoch 18/400 Stage 1 lr:0.000016 total_loss:0.067560
2025-05-27 15:18:59,853 - train.progressive_trainer - INFO - [0/62] Epoch 19/400 Stage 1 lr:0.000016 total_loss:0.080869
2025-05-27 15:19:08,520 - train.progressive_trainer - INFO - [50/62] Epoch 19/400 Stage 1 lr:0.000016 total_loss:0.064351
2025-05-27 15:19:10,513 - train.progressive_trainer - INFO - [61/62] Epoch 19/400 Stage 1 lr:0.000016 total_loss:0.074694
2025-05-27 15:19:11,683 - train.progressive_trainer - INFO - [0/62] Epoch 20/400 Stage 1 lr:0.000016 total_loss:0.066817
2025-05-27 15:19:21,052 - train.progressive_trainer - INFO - [50/62] Epoch 20/400 Stage 1 lr:0.000016 total_loss:0.063607
2025-05-27 15:19:22,737 - train.progressive_trainer - INFO - [61/62] Epoch 20/400 Stage 1 lr:0.000016 total_loss:0.069961
2025-05-27 15:19:23,958 - train.progressive_trainer - INFO - [0/62] Epoch 21/400 Stage 1 lr:0.000016 total_loss:0.066745
2025-05-27 15:19:32,615 - train.progressive_trainer - INFO - [50/62] Epoch 21/400 Stage 1 lr:0.000016 total_loss:0.061277
2025-05-27 15:19:34,385 - train.progressive_trainer - INFO - [61/62] Epoch 21/400 Stage 1 lr:0.000016 total_loss:0.061113
2025-05-27 15:19:35,754 - train.progressive_trainer - INFO - [0/62] Epoch 22/400 Stage 1 lr:0.000016 total_loss:0.062321
2025-05-27 15:19:43,683 - train.progressive_trainer - INFO - [50/62] Epoch 22/400 Stage 1 lr:0.000016 total_loss:0.058884
2025-05-27 15:19:45,724 - train.progressive_trainer - INFO - [61/62] Epoch 22/400 Stage 1 lr:0.000016 total_loss:0.057911
2025-05-27 15:19:47,354 - train.progressive_trainer - INFO - [0/62] Epoch 23/400 Stage 1 lr:0.000016 total_loss:0.070438
2025-05-27 15:19:55,812 - train.progressive_trainer - INFO - [50/62] Epoch 23/400 Stage 1 lr:0.000016 total_loss:0.057476
2025-05-27 15:19:57,610 - train.progressive_trainer - INFO - [61/62] Epoch 23/400 Stage 1 lr:0.000016 total_loss:0.056283
2025-05-27 15:19:59,960 - train.progressive_trainer - INFO - [0/62] Epoch 24/400 Stage 1 lr:0.000016 total_loss:0.056344
2025-05-27 15:20:07,713 - train.progressive_trainer - INFO - [50/62] Epoch 24/400 Stage 1 lr:0.000016 total_loss:0.055530
2025-05-27 15:20:09,610 - train.progressive_trainer - INFO - [61/62] Epoch 24/400 Stage 1 lr:0.000016 total_loss:0.061251
2025-05-27 15:20:10,755 - train.progressive_trainer - INFO - [0/62] Epoch 25/400 Stage 1 lr:0.000016 total_loss:0.056517
2025-05-27 15:20:19,513 - train.progressive_trainer - INFO - [50/62] Epoch 25/400 Stage 1 lr:0.000016 total_loss:0.053169
2025-05-27 15:20:21,120 - train.progressive_trainer - INFO - [61/62] Epoch 25/400 Stage 1 lr:0.000016 total_loss:0.053805
2025-05-27 15:20:22,540 - train.progressive_trainer - INFO - [0/62] Epoch 26/400 Stage 1 lr:0.000016 total_loss:0.052833
2025-05-27 15:20:31,314 - train.progressive_trainer - INFO - [50/62] Epoch 26/400 Stage 1 lr:0.000016 total_loss:0.052170
2025-05-27 15:20:33,311 - train.progressive_trainer - INFO - [61/62] Epoch 26/400 Stage 1 lr:0.000016 total_loss:0.053520
2025-05-27 15:20:35,254 - train.progressive_trainer - INFO - [0/62] Epoch 27/400 Stage 1 lr:0.000016 total_loss:0.052254
2025-05-27 15:20:43,719 - train.progressive_trainer - INFO - [50/62] Epoch 27/400 Stage 1 lr:0.000016 total_loss:0.052647
2025-05-27 15:20:45,619 - train.progressive_trainer - INFO - [61/62] Epoch 27/400 Stage 1 lr:0.000016 total_loss:0.056022
2025-05-27 15:20:46,941 - train.progressive_trainer - INFO - [0/62] Epoch 28/400 Stage 1 lr:0.000016 total_loss:0.051741
2025-05-27 15:20:55,211 - train.progressive_trainer - INFO - [50/62] Epoch 28/400 Stage 1 lr:0.000016 total_loss:0.048836
2025-05-27 15:20:56,912 - train.progressive_trainer - INFO - [61/62] Epoch 28/400 Stage 1 lr:0.000016 total_loss:0.048339
2025-05-27 15:20:58,424 - train.progressive_trainer - INFO - [0/62] Epoch 29/400 Stage 1 lr:0.000016 total_loss:0.048297
2025-05-27 15:21:05,827 - train.progressive_trainer - INFO - [50/62] Epoch 29/400 Stage 1 lr:0.000016 total_loss:0.048200
2025-05-27 15:21:07,907 - train.progressive_trainer - INFO - [61/62] Epoch 29/400 Stage 1 lr:0.000016 total_loss:0.049792
2025-05-27 15:21:08,856 - train.progressive_trainer - INFO - [0/62] Epoch 30/400 Stage 1 lr:0.000016 total_loss:0.048999
2025-05-27 15:21:17,910 - train.progressive_trainer - INFO - [50/62] Epoch 30/400 Stage 1 lr:0.000016 total_loss:0.050208
2025-05-27 15:21:19,525 - train.progressive_trainer - INFO - [61/62] Epoch 30/400 Stage 1 lr:0.000016 total_loss:0.047875
2025-05-27 15:21:20,408 - train.progressive_trainer - INFO - [0/62] Epoch 31/400 Stage 1 lr:0.000016 total_loss:0.046597
2025-05-27 15:21:29,011 - train.progressive_trainer - INFO - [50/62] Epoch 31/400 Stage 1 lr:0.000016 total_loss:0.046107
2025-05-27 15:21:30,511 - train.progressive_trainer - INFO - [61/62] Epoch 31/400 Stage 1 lr:0.000016 total_loss:0.047882
2025-05-27 15:21:31,459 - train.progressive_trainer - INFO - [0/62] Epoch 32/400 Stage 1 lr:0.000016 total_loss:0.045463
2025-05-27 15:21:41,008 - train.progressive_trainer - INFO - [50/62] Epoch 32/400 Stage 1 lr:0.000016 total_loss:0.045407
2025-05-27 15:21:42,995 - train.progressive_trainer - INFO - [61/62] Epoch 32/400 Stage 1 lr:0.000016 total_loss:0.047611
2025-05-27 15:21:44,560 - train.progressive_trainer - INFO - [0/62] Epoch 33/400 Stage 1 lr:0.000016 total_loss:0.049459
2025-05-27 15:21:53,022 - train.progressive_trainer - INFO - [50/62] Epoch 33/400 Stage 1 lr:0.000016 total_loss:0.042477
2025-05-27 15:21:54,926 - train.progressive_trainer - INFO - [61/62] Epoch 33/400 Stage 1 lr:0.000016 total_loss:0.042541
2025-05-27 15:21:56,647 - train.progressive_trainer - INFO - [0/62] Epoch 34/400 Stage 1 lr:0.000016 total_loss:0.042302
2025-05-27 15:22:05,611 - train.progressive_trainer - INFO - [50/62] Epoch 34/400 Stage 1 lr:0.000016 total_loss:0.042860
2025-05-27 15:22:07,716 - train.progressive_trainer - INFO - [61/62] Epoch 34/400 Stage 1 lr:0.000016 total_loss:0.042294
2025-05-27 15:22:09,160 - train.progressive_trainer - INFO - [0/62] Epoch 35/400 Stage 1 lr:0.000016 total_loss:0.041499
2025-05-27 15:22:17,925 - train.progressive_trainer - INFO - [50/62] Epoch 35/400 Stage 1 lr:0.000016 total_loss:0.042634
2025-05-27 15:22:20,018 - train.progressive_trainer - INFO - [61/62] Epoch 35/400 Stage 1 lr:0.000016 total_loss:0.041912
2025-05-27 15:22:21,165 - train.progressive_trainer - INFO - [0/62] Epoch 36/400 Stage 1 lr:0.000016 total_loss:0.041111
2025-05-27 15:22:29,921 - train.progressive_trainer - INFO - [50/62] Epoch 36/400 Stage 1 lr:0.000016 total_loss:0.040649
2025-05-27 15:22:32,019 - train.progressive_trainer - INFO - [61/62] Epoch 36/400 Stage 1 lr:0.000016 total_loss:0.039907
2025-05-27 15:22:33,049 - train.progressive_trainer - INFO - [0/62] Epoch 37/400 Stage 1 lr:0.000016 total_loss:0.040902
2025-05-27 15:22:41,916 - train.progressive_trainer - INFO - [50/62] Epoch 37/400 Stage 1 lr:0.000016 total_loss:0.039528
2025-05-27 15:22:43,717 - train.progressive_trainer - INFO - [61/62] Epoch 37/400 Stage 1 lr:0.000016 total_loss:0.038543
2025-05-27 15:22:45,855 - train.progressive_trainer - INFO - [0/62] Epoch 38/400 Stage 1 lr:0.000016 total_loss:0.040003
2025-05-27 15:22:54,007 - train.progressive_trainer - INFO - [50/62] Epoch 38/400 Stage 1 lr:0.000016 total_loss:0.042696
2025-05-27 15:22:55,819 - train.progressive_trainer - INFO - [61/62] Epoch 38/400 Stage 1 lr:0.000016 total_loss:0.038603
2025-05-27 15:22:56,434 - train.progressive_trainer - INFO - [0/62] Epoch 39/400 Stage 1 lr:0.000016 total_loss:0.037964
2025-05-27 15:23:05,261 - train.progressive_trainer - INFO - [50/62] Epoch 39/400 Stage 1 lr:0.000016 total_loss:0.058711
2025-05-27 15:23:07,012 - train.progressive_trainer - INFO - [61/62] Epoch 39/400 Stage 1 lr:0.000016 total_loss:0.040095
2025-05-27 15:23:08,053 - train.progressive_trainer - INFO - [0/62] Epoch 40/400 Stage 1 lr:0.000016 total_loss:0.037236
2025-05-27 15:23:16,912 - train.progressive_trainer - INFO - [50/62] Epoch 40/400 Stage 1 lr:0.000016 total_loss:0.036024
2025-05-27 15:23:18,618 - train.progressive_trainer - INFO - [61/62] Epoch 40/400 Stage 1 lr:0.000016 total_loss:0.036470
2025-05-27 15:23:20,243 - train.progressive_trainer - INFO - [0/62] Epoch 41/400 Stage 1 lr:0.000016 total_loss:0.036611
2025-05-27 15:23:29,008 - train.progressive_trainer - INFO - [50/62] Epoch 41/400 Stage 1 lr:0.000016 total_loss:0.034986
2025-05-27 15:23:30,797 - train.progressive_trainer - INFO - [61/62] Epoch 41/400 Stage 1 lr:0.000016 total_loss:0.036200
2025-05-27 15:23:31,672 - train.progressive_trainer - INFO - [0/62] Epoch 42/400 Stage 1 lr:0.000016 total_loss:0.035265
2025-05-27 15:23:39,608 - train.progressive_trainer - INFO - [50/62] Epoch 42/400 Stage 1 lr:0.000016 total_loss:0.034706
2025-05-27 15:23:41,360 - train.progressive_trainer - INFO - [61/62] Epoch 42/400 Stage 1 lr:0.000016 total_loss:0.034731
2025-05-27 15:23:42,283 - train.progressive_trainer - INFO - [0/62] Epoch 43/400 Stage 1 lr:0.000016 total_loss:0.039940
2025-05-27 15:23:50,328 - train.progressive_trainer - INFO - [50/62] Epoch 43/400 Stage 1 lr:0.000016 total_loss:0.038392
2025-05-27 15:23:52,104 - train.progressive_trainer - INFO - [61/62] Epoch 43/400 Stage 1 lr:0.000016 total_loss:0.034947
2025-05-27 15:23:52,999 - train.progressive_trainer - INFO - [0/62] Epoch 44/400 Stage 1 lr:0.000016 total_loss:0.036213
2025-05-27 15:24:01,911 - train.progressive_trainer - INFO - [50/62] Epoch 44/400 Stage 1 lr:0.000016 total_loss:0.032889
2025-05-27 15:24:03,612 - train.progressive_trainer - INFO - [61/62] Epoch 44/400 Stage 1 lr:0.000016 total_loss:0.033276
2025-05-27 15:24:05,460 - train.progressive_trainer - INFO - [0/62] Epoch 45/400 Stage 1 lr:0.000016 total_loss:0.033380
2025-05-27 15:24:13,717 - train.progressive_trainer - INFO - [50/62] Epoch 45/400 Stage 1 lr:0.000016 total_loss:0.049954
2025-05-27 15:24:15,507 - train.progressive_trainer - INFO - [61/62] Epoch 45/400 Stage 1 lr:0.000016 total_loss:0.033145
2025-05-27 15:24:16,459 - train.progressive_trainer - INFO - [0/62] Epoch 46/400 Stage 1 lr:0.000016 total_loss:0.035768
2025-05-27 15:24:24,511 - train.progressive_trainer - INFO - [50/62] Epoch 46/400 Stage 1 lr:0.000016 total_loss:0.032294
2025-05-27 15:24:26,209 - train.progressive_trainer - INFO - [61/62] Epoch 46/400 Stage 1 lr:0.000016 total_loss:0.031673
2025-05-27 15:24:27,321 - train.progressive_trainer - INFO - [0/62] Epoch 47/400 Stage 1 lr:0.000016 total_loss:0.032173
2025-05-27 15:24:36,526 - train.progressive_trainer - INFO - [50/62] Epoch 47/400 Stage 1 lr:0.000016 total_loss:0.034473
2025-05-27 15:24:38,211 - train.progressive_trainer - INFO - [61/62] Epoch 47/400 Stage 1 lr:0.000016 total_loss:0.032452
2025-05-27 15:24:39,056 - train.progressive_trainer - INFO - [0/62] Epoch 48/400 Stage 1 lr:0.000016 total_loss:0.032780
2025-05-27 15:24:48,094 - train.progressive_trainer - INFO - [50/62] Epoch 48/400 Stage 1 lr:0.000016 total_loss:0.031830
2025-05-27 15:24:49,914 - train.progressive_trainer - INFO - [61/62] Epoch 48/400 Stage 1 lr:0.000016 total_loss:0.032220
2025-05-27 15:24:50,952 - train.progressive_trainer - INFO - [0/62] Epoch 49/400 Stage 1 lr:0.000016 total_loss:0.034443
2025-05-27 15:25:00,114 - train.progressive_trainer - INFO - [50/62] Epoch 49/400 Stage 1 lr:0.000016 total_loss:0.030245
2025-05-27 15:25:01,617 - train.progressive_trainer - INFO - [61/62] Epoch 49/400 Stage 1 lr:0.000016 total_loss:0.040104
2025-05-27 15:25:02,361 - train.progressive_trainer - INFO - [0/62] Epoch 50/400 Stage 1 lr:0.000016 total_loss:0.030719
2025-05-27 15:25:11,411 - train.progressive_trainer - INFO - [50/62] Epoch 50/400 Stage 1 lr:0.000016 total_loss:0.030602
2025-05-27 15:25:13,212 - train.progressive_trainer - INFO - [61/62] Epoch 50/400 Stage 1 lr:0.000016 total_loss:0.028929
2025-05-27 15:25:13,281 - train.trainer - INFO - Running scheduled evaluation at epoch 50 (eval_every=50)...
2025-05-27 15:25:13,281 - train.trainer - INFO - Evaluating model at epoch 50...
2025-05-27 15:25:13,291 - train.trainer - INFO - Evaluation logs will be saved to outputs_a100_fixed/logs/eval/eval_epoch_50.log
2025-05-27 15:25:13,293 - train.trainer - INFO - Generating 995 samples for evaluation...
2025-05-27 15:25:13,293 - train.trainer - INFO - Using PulsarAdaptiveSampler for evaluation
2025-05-27 15:25:13,295 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:13,296 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0009, std=0.9998
2025-05-27 15:25:28,228 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1864, std=0.1506, range=[-0.7073, 0.5380]
2025-05-27 15:25:30,966 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:30,966 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0059, std=0.9990
2025-05-27 15:25:31,065 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1760, std=0.1526, range=[-0.4960, 0.5916]
2025-05-27 15:25:31,066 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:31,066 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0026, std=0.9968
2025-05-27 15:25:31,260 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1839, std=0.1407, range=[-0.5387, 0.5595]
2025-05-27 15:25:31,261 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:31,261 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0022, std=0.9962
2025-05-27 15:25:31,457 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1943, std=0.1395, range=[-0.6350, 0.4281]
2025-05-27 15:25:31,458 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:31,458 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0015, std=0.9951
2025-05-27 15:25:31,648 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1830, std=0.1651, range=[-0.7051, 0.7052]
2025-05-27 15:25:31,650 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:31,650 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0024, std=0.9947
2025-05-27 15:25:31,855 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1893, std=0.1459, range=[-0.6779, 0.4858]
2025-05-27 15:25:31,857 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:31,857 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0024, std=1.0008
2025-05-27 15:25:32,042 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1840, std=0.1518, range=[-0.5356, 0.5634]
2025-05-27 15:25:32,043 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:32,044 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0033, std=1.0018
2025-05-27 15:25:32,233 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1995, std=0.1364, range=[-0.5045, 0.5077]
2025-05-27 15:25:32,234 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:32,234 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0019, std=0.9985
2025-05-27 15:25:32,451 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2015, std=0.1427, range=[-0.5298, 0.4645]
2025-05-27 15:25:32,452 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:32,452 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0022, std=0.9983
2025-05-27 15:25:32,658 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1879, std=0.1325, range=[-0.5967, 0.3570]
2025-05-27 15:25:32,659 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:32,659 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0014, std=1.0009
2025-05-27 15:25:32,844 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1919, std=0.1279, range=[-0.5330, 0.4106]
2025-05-27 15:25:32,848 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:32,848 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0035, std=0.9968
2025-05-27 15:25:33,063 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1918, std=0.1413, range=[-0.4981, 0.5220]
2025-05-27 15:25:33,064 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:33,064 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0018, std=0.9960
2025-05-27 15:25:33,253 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1970, std=0.1421, range=[-0.7165, 0.4999]
2025-05-27 15:25:33,255 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:33,255 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0011, std=0.9998
2025-05-27 15:25:33,452 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1971, std=0.1484, range=[-0.6356, 0.5986]
2025-05-27 15:25:33,453 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:33,453 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0059, std=0.9990
2025-05-27 15:25:33,655 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1733, std=0.1547, range=[-0.5698, 0.4462]
2025-05-27 15:25:33,656 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:33,656 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0024, std=0.9967
2025-05-27 15:25:33,846 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1947, std=0.1309, range=[-0.4886, 0.3954]
2025-05-27 15:25:33,847 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:33,847 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0012, std=0.9965
2025-05-27 15:25:34,062 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1962, std=0.1505, range=[-0.6096, 0.3762]
2025-05-27 15:25:34,326 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:34,326 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0014, std=0.9999
2025-05-27 15:25:34,530 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1897, std=0.1251, range=[-0.4856, 0.3969]
2025-05-27 15:25:34,532 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:25:34,532 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0005, std=0.9964
2025-05-27 15:25:34,751 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1932, std=0.1455, range=[-0.5909, 0.4325]
2025-05-27 15:25:34,752 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=45, steps=20
2025-05-27 15:25:34,752 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0010, std=0.9983
2025-05-27 15:25:34,994 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1892, std=0.1277, range=[-0.4764, 0.3224]
2025-05-27 15:25:35,179 - train.trainer - INFO - Generated 995 images with shape torch.Size([995, 3, 32, 32])
2025-05-27 15:25:35,393 - train.trainer - INFO - Saved 100 samples to outputs_a100_fixed/checkpoints/eval_epoch_50/generated_samples.pt
2025-05-27 15:25:35,393 - train.trainer - INFO - Loading real images for evaluation...
2025-05-27 15:25:37,301 - train.trainer - INFO - Loaded 995 real images with shape torch.Size([995, 3, 32, 32])
2025-05-27 15:25:37,302 - train.trainer - INFO - Calculating overall FID and IS metrics...
2025-05-27 15:26:54,067 - train.trainer - INFO - Overall evaluation completed: FID=269.1612, IS=2.0116±0.0932
2025-05-27 15:26:54,067 - train.trainer - INFO - Calculating channel-specific metrics...
2025-05-27 15:31:02,749 - train.trainer - INFO - Channel-specific evaluation completed
2025-05-27 15:31:02,750 - train.trainer - INFO - ==================================================
2025-05-27 15:31:02,750 - train.trainer - INFO - EVALUATION RESULTS (Epoch 50):
2025-05-27 15:31:02,750 - train.trainer - INFO - ==================================================
2025-05-27 15:31:02,750 - train.trainer - INFO - Overall FID: 269.1612
2025-05-27 15:31:02,750 - train.trainer - INFO - Overall IS: 2.0116 ± 0.0932
2025-05-27 15:31:02,750 - train.trainer - INFO - --------------------------------------------------
2025-05-27 15:31:02,750 - train.trainer - INFO - Period-DM surface:
2025-05-27 15:31:02,750 - train.trainer - INFO -   FID = 446.9159
2025-05-27 15:31:02,750 - train.trainer - INFO -   IS  = 1.7853 ± 0.0970
2025-05-27 15:31:02,750 - train.trainer - INFO - --------------------------------------------------
2025-05-27 15:31:02,750 - train.trainer - INFO - Phase-Subband surface:
2025-05-27 15:31:02,750 - train.trainer - INFO -   FID = 287.9080
2025-05-27 15:31:02,750 - train.trainer - INFO -   IS  = 2.3556 ± 0.0629
2025-05-27 15:31:02,750 - train.trainer - INFO - --------------------------------------------------
2025-05-27 15:31:02,750 - train.trainer - INFO - Phase-Subintegration surface:
2025-05-27 15:31:02,750 - train.trainer - INFO -   FID = 316.0285
2025-05-27 15:31:02,750 - train.trainer - INFO -   IS  = 1.5271 ± 0.0373
2025-05-27 15:31:02,750 - train.trainer - INFO - --------------------------------------------------
2025-05-27 15:31:02,750 - train.trainer - INFO - Evaluation completed in 349.47 seconds
2025-05-27 15:31:02,753 - train.trainer - INFO - Saved evaluation results to outputs_a100_fixed/logs/eval/results_epoch_50.json
2025-05-27 15:31:02,753 - train.trainer - INFO - Removed evaluation log handler
2025-05-27 15:31:02,755 - train.trainer - INFO - Evaluation metrics at epoch 50:
2025-05-27 15:31:02,755 - train.trainer - INFO -   FID: 269.16117753391416
2025-05-27 15:31:02,755 - train.trainer - INFO -   IS: 2.011580228805542 ± 0.0931696891784668
2025-05-27 15:31:02,755 - train.trainer - INFO - Early stopping: New best FID score 269.1612 at epoch 50 (improvement: inf)
2025-05-27 15:31:02,834 - train.progressive_trainer - INFO - 检查点已保存: outputs_a100_fixed/checkpoints/checkpoint_epoch_50.pt
2025-05-27 15:31:04,127 - train.progressive_trainer - INFO - [0/62] Epoch 51/400 Stage 1 lr:0.000016 total_loss:0.032751
2025-05-27 15:31:12,716 - train.progressive_trainer - INFO - [50/62] Epoch 51/400 Stage 1 lr:0.000016 total_loss:0.030315
2025-05-27 15:31:14,522 - train.progressive_trainer - INFO - [61/62] Epoch 51/400 Stage 1 lr:0.000016 total_loss:0.030680
2025-05-27 15:31:15,853 - train.progressive_trainer - INFO - [0/62] Epoch 52/400 Stage 1 lr:0.000016 total_loss:0.030914
2025-05-27 15:31:24,420 - train.progressive_trainer - INFO - [50/62] Epoch 52/400 Stage 1 lr:0.000016 total_loss:0.031557
2025-05-27 15:31:26,416 - train.progressive_trainer - INFO - [61/62] Epoch 52/400 Stage 1 lr:0.000016 total_loss:0.027785
2025-05-27 15:31:27,161 - train.progressive_trainer - INFO - [0/62] Epoch 53/400 Stage 1 lr:0.000016 total_loss:0.028178
2025-05-27 15:31:35,623 - train.progressive_trainer - INFO - [50/62] Epoch 53/400 Stage 1 lr:0.000016 total_loss:0.027451
2025-05-27 15:31:37,713 - train.progressive_trainer - INFO - [61/62] Epoch 53/400 Stage 1 lr:0.000016 total_loss:0.030600
2025-05-27 15:31:38,360 - train.progressive_trainer - INFO - [0/62] Epoch 54/400 Stage 1 lr:0.000016 total_loss:0.027363
2025-05-27 15:31:47,427 - train.progressive_trainer - INFO - [50/62] Epoch 54/400 Stage 1 lr:0.000016 total_loss:0.026625
2025-05-27 15:31:49,214 - train.progressive_trainer - INFO - [61/62] Epoch 54/400 Stage 1 lr:0.000016 total_loss:0.026459
2025-05-27 15:31:50,253 - train.progressive_trainer - INFO - [0/62] Epoch 55/400 Stage 1 lr:0.000016 total_loss:0.027549
2025-05-27 15:31:59,316 - train.progressive_trainer - INFO - [50/62] Epoch 55/400 Stage 1 lr:0.000016 total_loss:0.027083
2025-05-27 15:32:00,870 - train.progressive_trainer - INFO - [61/62] Epoch 55/400 Stage 1 lr:0.000016 total_loss:0.026468
2025-05-27 15:32:01,556 - train.progressive_trainer - INFO - [0/62] Epoch 56/400 Stage 1 lr:0.000016 total_loss:0.026482
2025-05-27 15:32:10,612 - train.progressive_trainer - INFO - [50/62] Epoch 56/400 Stage 1 lr:0.000016 total_loss:0.025574
2025-05-27 15:32:12,207 - train.progressive_trainer - INFO - [61/62] Epoch 56/400 Stage 1 lr:0.000016 total_loss:0.026525
2025-05-27 15:32:12,861 - train.progressive_trainer - INFO - [0/62] Epoch 57/400 Stage 1 lr:0.000016 total_loss:0.042692
2025-05-27 15:32:21,524 - train.progressive_trainer - INFO - [50/62] Epoch 57/400 Stage 1 lr:0.000016 total_loss:0.025530
2025-05-27 15:32:23,618 - train.progressive_trainer - INFO - [61/62] Epoch 57/400 Stage 1 lr:0.000016 total_loss:0.025568
2025-05-27 15:32:24,514 - train.progressive_trainer - INFO - [0/62] Epoch 58/400 Stage 1 lr:0.000016 total_loss:0.025919
2025-05-27 15:32:32,815 - train.progressive_trainer - INFO - [50/62] Epoch 58/400 Stage 1 lr:0.000016 total_loss:0.024837
2025-05-27 15:32:34,714 - train.progressive_trainer - INFO - [61/62] Epoch 58/400 Stage 1 lr:0.000016 total_loss:0.024778
2025-05-27 15:32:35,942 - train.progressive_trainer - INFO - [0/62] Epoch 59/400 Stage 1 lr:0.000016 total_loss:0.025058
2025-05-27 15:32:44,818 - train.progressive_trainer - INFO - [50/62] Epoch 59/400 Stage 1 lr:0.000016 total_loss:0.024474
2025-05-27 15:32:46,570 - train.progressive_trainer - INFO - [61/62] Epoch 59/400 Stage 1 lr:0.000016 total_loss:0.025029
2025-05-27 15:32:47,249 - train.progressive_trainer - INFO - [0/62] Epoch 60/400 Stage 1 lr:0.000016 total_loss:0.051450
2025-05-27 15:32:55,713 - train.progressive_trainer - INFO - [50/62] Epoch 60/400 Stage 1 lr:0.000016 total_loss:0.024641
2025-05-27 15:32:57,705 - train.progressive_trainer - INFO - [61/62] Epoch 60/400 Stage 1 lr:0.000016 total_loss:0.024296
2025-05-27 15:32:58,461 - train.progressive_trainer - INFO - [0/62] Epoch 61/400 Stage 1 lr:0.000016 total_loss:0.027181
2025-05-27 15:33:07,218 - train.progressive_trainer - INFO - [50/62] Epoch 61/400 Stage 1 lr:0.000016 total_loss:0.024193
2025-05-27 15:33:09,315 - train.progressive_trainer - INFO - [61/62] Epoch 61/400 Stage 1 lr:0.000016 total_loss:0.042037
2025-05-27 15:33:10,054 - train.progressive_trainer - INFO - [0/62] Epoch 62/400 Stage 1 lr:0.000016 total_loss:0.027552
2025-05-27 15:33:19,010 - train.progressive_trainer - INFO - [50/62] Epoch 62/400 Stage 1 lr:0.000016 total_loss:0.023358
2025-05-27 15:33:20,424 - train.progressive_trainer - INFO - [61/62] Epoch 62/400 Stage 1 lr:0.000016 total_loss:0.022930
2025-05-27 15:33:21,259 - train.progressive_trainer - INFO - [0/62] Epoch 63/400 Stage 1 lr:0.000016 total_loss:0.022481
2025-05-27 15:33:30,323 - train.progressive_trainer - INFO - [50/62] Epoch 63/400 Stage 1 lr:0.000016 total_loss:0.026987
2025-05-27 15:33:32,124 - train.progressive_trainer - INFO - [61/62] Epoch 63/400 Stage 1 lr:0.000016 total_loss:0.022910
2025-05-27 15:33:32,719 - train.progressive_trainer - INFO - [0/62] Epoch 64/400 Stage 1 lr:0.000016 total_loss:0.023146
2025-05-27 15:33:41,918 - train.progressive_trainer - INFO - [50/62] Epoch 64/400 Stage 1 lr:0.000016 total_loss:0.022744
2025-05-27 15:33:43,916 - train.progressive_trainer - INFO - [61/62] Epoch 64/400 Stage 1 lr:0.000016 total_loss:0.023589
2025-05-27 15:33:45,459 - train.progressive_trainer - INFO - [0/62] Epoch 65/400 Stage 1 lr:0.000016 total_loss:0.050203
2025-05-27 15:33:53,615 - train.progressive_trainer - INFO - [50/62] Epoch 65/400 Stage 1 lr:0.000016 total_loss:0.021963
2025-05-27 15:33:55,416 - train.progressive_trainer - INFO - [61/62] Epoch 65/400 Stage 1 lr:0.000016 total_loss:0.024288
2025-05-27 15:33:56,161 - train.progressive_trainer - INFO - [0/62] Epoch 66/400 Stage 1 lr:0.000016 total_loss:0.022796
2025-05-27 15:34:05,417 - train.progressive_trainer - INFO - [50/62] Epoch 66/400 Stage 1 lr:0.000016 total_loss:0.021570
2025-05-27 15:34:06,968 - train.progressive_trainer - INFO - [61/62] Epoch 66/400 Stage 1 lr:0.000016 total_loss:0.021329
2025-05-27 15:34:07,814 - train.progressive_trainer - INFO - [0/62] Epoch 67/400 Stage 1 lr:0.000016 total_loss:0.021609
2025-05-27 15:34:16,419 - train.progressive_trainer - INFO - [50/62] Epoch 67/400 Stage 1 lr:0.000016 total_loss:0.020909
2025-05-27 15:34:18,417 - train.progressive_trainer - INFO - [61/62] Epoch 67/400 Stage 1 lr:0.000016 total_loss:0.025596
2025-05-27 15:34:20,150 - train.progressive_trainer - INFO - [0/62] Epoch 68/400 Stage 1 lr:0.000016 total_loss:0.021022
2025-05-27 15:34:28,713 - train.progressive_trainer - INFO - [50/62] Epoch 68/400 Stage 1 lr:0.000016 total_loss:0.020527
2025-05-27 15:34:30,219 - train.progressive_trainer - INFO - [61/62] Epoch 68/400 Stage 1 lr:0.000016 total_loss:0.020492
2025-05-27 15:34:31,256 - train.progressive_trainer - INFO - [0/62] Epoch 69/400 Stage 1 lr:0.000016 total_loss:0.020415
2025-05-27 15:34:39,999 - train.progressive_trainer - INFO - [50/62] Epoch 69/400 Stage 1 lr:0.000016 total_loss:0.021031
2025-05-27 15:34:41,938 - train.progressive_trainer - INFO - [61/62] Epoch 69/400 Stage 1 lr:0.000016 total_loss:0.020387
2025-05-27 15:34:42,664 - train.progressive_trainer - INFO - [0/62] Epoch 70/400 Stage 1 lr:0.000016 total_loss:0.021118
2025-05-27 15:34:51,021 - train.progressive_trainer - INFO - [50/62] Epoch 70/400 Stage 1 lr:0.000016 total_loss:0.021202
2025-05-27 15:34:53,023 - train.progressive_trainer - INFO - [61/62] Epoch 70/400 Stage 1 lr:0.000016 total_loss:0.020359
2025-05-27 15:34:53,759 - train.progressive_trainer - INFO - [0/62] Epoch 71/400 Stage 1 lr:0.000016 total_loss:0.022782
2025-05-27 15:35:01,927 - train.progressive_trainer - INFO - [50/62] Epoch 71/400 Stage 1 lr:0.000016 total_loss:0.019804
2025-05-27 15:35:03,825 - train.progressive_trainer - INFO - [61/62] Epoch 71/400 Stage 1 lr:0.000016 total_loss:0.019513
2025-05-27 15:35:05,355 - train.progressive_trainer - INFO - [0/62] Epoch 72/400 Stage 1 lr:0.000016 total_loss:0.019074
2025-05-27 15:35:13,623 - train.progressive_trainer - INFO - [50/62] Epoch 72/400 Stage 1 lr:0.000016 total_loss:0.020578
2025-05-27 15:35:15,601 - train.progressive_trainer - INFO - [61/62] Epoch 72/400 Stage 1 lr:0.000016 total_loss:0.021360
2025-05-27 15:35:16,415 - train.progressive_trainer - INFO - [0/62] Epoch 73/400 Stage 1 lr:0.000016 total_loss:0.020912
2025-05-27 15:35:24,619 - train.progressive_trainer - INFO - [50/62] Epoch 73/400 Stage 1 lr:0.000016 total_loss:0.018607
2025-05-27 15:35:26,427 - train.progressive_trainer - INFO - [61/62] Epoch 73/400 Stage 1 lr:0.000016 total_loss:0.018831
2025-05-27 15:35:27,163 - train.progressive_trainer - INFO - [0/62] Epoch 74/400 Stage 1 lr:0.000016 total_loss:0.025096
2025-05-27 15:35:36,517 - train.progressive_trainer - INFO - [50/62] Epoch 74/400 Stage 1 lr:0.000016 total_loss:0.019267
2025-05-27 15:35:38,228 - train.progressive_trainer - INFO - [61/62] Epoch 74/400 Stage 1 lr:0.000016 total_loss:0.018183
2025-05-27 15:35:39,159 - train.progressive_trainer - INFO - [0/62] Epoch 75/400 Stage 1 lr:0.000016 total_loss:0.018502
2025-05-27 15:35:47,784 - train.progressive_trainer - INFO - [50/62] Epoch 75/400 Stage 1 lr:0.000016 total_loss:0.024077
2025-05-27 15:35:49,808 - train.progressive_trainer - INFO - [61/62] Epoch 75/400 Stage 1 lr:0.000016 total_loss:0.018416
2025-05-27 15:35:50,561 - train.progressive_trainer - INFO - [0/62] Epoch 76/400 Stage 1 lr:0.000016 total_loss:0.018627
2025-05-27 15:36:00,013 - train.progressive_trainer - INFO - [50/62] Epoch 76/400 Stage 1 lr:0.000016 total_loss:0.018068
2025-05-27 15:36:01,486 - train.progressive_trainer - INFO - [61/62] Epoch 76/400 Stage 1 lr:0.000016 total_loss:0.019432
2025-05-27 15:36:02,258 - train.progressive_trainer - INFO - [0/62] Epoch 77/400 Stage 1 lr:0.000016 total_loss:0.018063
2025-05-27 15:36:10,974 - train.progressive_trainer - INFO - [50/62] Epoch 77/400 Stage 1 lr:0.000016 total_loss:0.017539
2025-05-27 15:36:12,725 - train.progressive_trainer - INFO - [61/62] Epoch 77/400 Stage 1 lr:0.000016 total_loss:0.018240
2025-05-27 15:36:13,593 - train.progressive_trainer - INFO - [0/62] Epoch 78/400 Stage 1 lr:0.000016 total_loss:0.017715
2025-05-27 15:36:22,617 - train.progressive_trainer - INFO - [50/62] Epoch 78/400 Stage 1 lr:0.000016 total_loss:0.017244
2025-05-27 15:36:24,416 - train.progressive_trainer - INFO - [61/62] Epoch 78/400 Stage 1 lr:0.000016 total_loss:0.017438
2025-05-27 15:36:25,359 - train.progressive_trainer - INFO - [0/62] Epoch 79/400 Stage 1 lr:0.000016 total_loss:0.017843
2025-05-27 15:36:34,223 - train.progressive_trainer - INFO - [50/62] Epoch 79/400 Stage 1 lr:0.000016 total_loss:0.018321
2025-05-27 15:36:36,117 - train.progressive_trainer - INFO - [61/62] Epoch 79/400 Stage 1 lr:0.000016 total_loss:0.019433
2025-05-27 15:36:37,054 - train.progressive_trainer - INFO - [0/62] Epoch 80/400 Stage 1 lr:0.000016 total_loss:0.016938
2025-05-27 15:36:45,216 - train.progressive_trainer - INFO - [50/62] Epoch 80/400 Stage 1 lr:0.000016 total_loss:0.017211
2025-05-27 15:36:47,013 - train.progressive_trainer - INFO - [61/62] Epoch 80/400 Stage 1 lr:0.000016 total_loss:0.017443
2025-05-27 15:36:48,757 - train.progressive_trainer - INFO - [0/62] Epoch 81/400 Stage 1 lr:0.000016 total_loss:0.017562
2025-05-27 15:36:57,227 - train.progressive_trainer - INFO - [50/62] Epoch 81/400 Stage 1 lr:0.000016 total_loss:0.016598
2025-05-27 15:36:59,066 - train.progressive_trainer - INFO - [61/62] Epoch 81/400 Stage 1 lr:0.000016 total_loss:0.017458
2025-05-27 15:37:00,392 - train.progressive_trainer - INFO - [0/62] Epoch 82/400 Stage 1 lr:0.000016 total_loss:0.016774
2025-05-27 15:37:09,517 - train.progressive_trainer - INFO - [50/62] Epoch 82/400 Stage 1 lr:0.000016 total_loss:0.016427
2025-05-27 15:37:11,229 - train.progressive_trainer - INFO - [61/62] Epoch 82/400 Stage 1 lr:0.000016 total_loss:0.020344
2025-05-27 15:37:12,094 - train.progressive_trainer - INFO - [0/62] Epoch 83/400 Stage 1 lr:0.000016 total_loss:0.016907
2025-05-27 15:37:20,728 - train.progressive_trainer - INFO - [50/62] Epoch 83/400 Stage 1 lr:0.000016 total_loss:0.015802
2025-05-27 15:37:22,899 - train.progressive_trainer - INFO - [61/62] Epoch 83/400 Stage 1 lr:0.000016 total_loss:0.016476
2025-05-27 15:37:23,625 - train.progressive_trainer - INFO - [0/62] Epoch 84/400 Stage 1 lr:0.000016 total_loss:0.017420
2025-05-27 15:37:32,311 - train.progressive_trainer - INFO - [50/62] Epoch 84/400 Stage 1 lr:0.000016 total_loss:0.015897
2025-05-27 15:37:34,212 - train.progressive_trainer - INFO - [61/62] Epoch 84/400 Stage 1 lr:0.000016 total_loss:0.016318
2025-05-27 15:37:35,849 - train.progressive_trainer - INFO - [0/62] Epoch 85/400 Stage 1 lr:0.000016 total_loss:0.016421
2025-05-27 15:37:44,998 - train.progressive_trainer - INFO - [50/62] Epoch 85/400 Stage 1 lr:0.000016 total_loss:0.015562
2025-05-27 15:37:46,550 - train.progressive_trainer - INFO - [61/62] Epoch 85/400 Stage 1 lr:0.000016 total_loss:0.016187
2025-05-27 15:37:47,103 - train.progressive_trainer - INFO - [0/62] Epoch 86/400 Stage 1 lr:0.000016 total_loss:0.015261
2025-05-27 15:37:55,827 - train.progressive_trainer - INFO - [50/62] Epoch 86/400 Stage 1 lr:0.000016 total_loss:0.016003
2025-05-27 15:37:58,211 - train.progressive_trainer - INFO - [61/62] Epoch 86/400 Stage 1 lr:0.000016 total_loss:0.015353
2025-05-27 15:37:59,654 - train.progressive_trainer - INFO - [0/62] Epoch 87/400 Stage 1 lr:0.000016 total_loss:0.016511
2025-05-27 15:38:08,516 - train.progressive_trainer - INFO - [50/62] Epoch 87/400 Stage 1 lr:0.000016 total_loss:0.016135
2025-05-27 15:38:10,607 - train.progressive_trainer - INFO - [61/62] Epoch 87/400 Stage 1 lr:0.000016 total_loss:0.014710
2025-05-27 15:38:11,167 - train.progressive_trainer - INFO - [0/62] Epoch 88/400 Stage 1 lr:0.000016 total_loss:0.014856
2025-05-27 15:38:19,984 - train.progressive_trainer - INFO - [50/62] Epoch 88/400 Stage 1 lr:0.000016 total_loss:0.022885
2025-05-27 15:38:21,889 - train.progressive_trainer - INFO - [61/62] Epoch 88/400 Stage 1 lr:0.000016 total_loss:0.015459
2025-05-27 15:38:22,631 - train.progressive_trainer - INFO - [0/62] Epoch 89/400 Stage 1 lr:0.000016 total_loss:0.015701
2025-05-27 15:38:32,021 - train.progressive_trainer - INFO - [50/62] Epoch 89/400 Stage 1 lr:0.000016 total_loss:0.019928
2025-05-27 15:38:33,818 - train.progressive_trainer - INFO - [61/62] Epoch 89/400 Stage 1 lr:0.000016 total_loss:0.014962
2025-05-27 15:38:35,456 - train.progressive_trainer - INFO - [0/62] Epoch 90/400 Stage 1 lr:0.000016 total_loss:0.016575
2025-05-27 15:38:44,086 - train.progressive_trainer - INFO - [50/62] Epoch 90/400 Stage 1 lr:0.000016 total_loss:0.015433
2025-05-27 15:38:45,806 - train.progressive_trainer - INFO - [61/62] Epoch 90/400 Stage 1 lr:0.000016 total_loss:0.015159
2025-05-27 15:38:46,546 - train.progressive_trainer - INFO - [0/62] Epoch 91/400 Stage 1 lr:0.000016 total_loss:0.014127
2025-05-27 15:38:55,023 - train.progressive_trainer - INFO - [50/62] Epoch 91/400 Stage 1 lr:0.000016 total_loss:0.015652
2025-05-27 15:38:56,712 - train.progressive_trainer - INFO - [61/62] Epoch 91/400 Stage 1 lr:0.000016 total_loss:0.013904
2025-05-27 15:38:57,558 - train.progressive_trainer - INFO - [0/62] Epoch 92/400 Stage 1 lr:0.000016 total_loss:0.015247
2025-05-27 15:39:05,809 - train.progressive_trainer - INFO - [50/62] Epoch 92/400 Stage 1 lr:0.000016 total_loss:0.017964
2025-05-27 15:39:07,716 - train.progressive_trainer - INFO - [61/62] Epoch 92/400 Stage 1 lr:0.000016 total_loss:0.014240
2025-05-27 15:39:08,657 - train.progressive_trainer - INFO - [0/62] Epoch 93/400 Stage 1 lr:0.000016 total_loss:0.013950
2025-05-27 15:39:17,902 - train.progressive_trainer - INFO - [50/62] Epoch 93/400 Stage 1 lr:0.000016 total_loss:0.014209
2025-05-27 15:39:19,812 - train.progressive_trainer - INFO - [61/62] Epoch 93/400 Stage 1 lr:0.000016 total_loss:0.015046
2025-05-27 15:39:20,559 - train.progressive_trainer - INFO - [0/62] Epoch 94/400 Stage 1 lr:0.000016 total_loss:0.014028
2025-05-27 15:39:29,325 - train.progressive_trainer - INFO - [50/62] Epoch 94/400 Stage 1 lr:0.000016 total_loss:0.013482
2025-05-27 15:39:31,518 - train.progressive_trainer - INFO - [61/62] Epoch 94/400 Stage 1 lr:0.000016 total_loss:0.013686
2025-05-27 15:39:32,360 - train.progressive_trainer - INFO - [0/62] Epoch 95/400 Stage 1 lr:0.000016 total_loss:0.014131
2025-05-27 15:39:43,923 - train.progressive_trainer - INFO - [50/62] Epoch 95/400 Stage 1 lr:0.000016 total_loss:0.019967
2025-05-27 15:39:45,418 - train.progressive_trainer - INFO - [61/62] Epoch 95/400 Stage 1 lr:0.000016 total_loss:0.013313
2025-05-27 15:39:46,553 - train.progressive_trainer - INFO - [0/62] Epoch 96/400 Stage 1 lr:0.000016 total_loss:0.013223
2025-05-27 15:39:55,419 - train.progressive_trainer - INFO - [50/62] Epoch 96/400 Stage 1 lr:0.000016 total_loss:0.013208
2025-05-27 15:39:57,427 - train.progressive_trainer - INFO - [61/62] Epoch 96/400 Stage 1 lr:0.000016 total_loss:0.012781
2025-05-27 15:39:58,254 - train.progressive_trainer - INFO - [0/62] Epoch 97/400 Stage 1 lr:0.000016 total_loss:0.013399
2025-05-27 15:40:08,013 - train.progressive_trainer - INFO - [50/62] Epoch 97/400 Stage 1 lr:0.000016 total_loss:0.014388
2025-05-27 15:40:09,626 - train.progressive_trainer - INFO - [61/62] Epoch 97/400 Stage 1 lr:0.000016 total_loss:0.014412
2025-05-27 15:40:10,461 - train.progressive_trainer - INFO - [0/62] Epoch 98/400 Stage 1 lr:0.000016 total_loss:0.013677
2025-05-27 15:40:19,002 - train.progressive_trainer - INFO - [50/62] Epoch 98/400 Stage 1 lr:0.000016 total_loss:0.023805
2025-05-27 15:40:20,916 - train.progressive_trainer - INFO - [61/62] Epoch 98/400 Stage 1 lr:0.000016 total_loss:0.021483
2025-05-27 15:40:21,653 - train.progressive_trainer - INFO - [0/62] Epoch 99/400 Stage 1 lr:0.000016 total_loss:0.013487
2025-05-27 15:40:30,980 - train.progressive_trainer - INFO - [50/62] Epoch 99/400 Stage 1 lr:0.000016 total_loss:0.014578
2025-05-27 15:40:32,727 - train.progressive_trainer - INFO - [61/62] Epoch 99/400 Stage 1 lr:0.000016 total_loss:0.013595
2025-05-27 15:40:34,060 - train.progressive_trainer - INFO - [0/62] Epoch 100/400 Stage 1 lr:0.000016 total_loss:0.014996
2025-05-27 15:40:42,367 - train.progressive_trainer - INFO - [50/62] Epoch 100/400 Stage 1 lr:0.000016 total_loss:0.013740
2025-05-27 15:40:43,821 - train.progressive_trainer - INFO - [61/62] Epoch 100/400 Stage 1 lr:0.000016 total_loss:0.014809
2025-05-27 15:40:43,904 - train.trainer - INFO - Running scheduled evaluation at epoch 100 (eval_every=50)...
2025-05-27 15:40:43,904 - train.trainer - INFO - Evaluating model at epoch 100...
2025-05-27 15:40:43,909 - train.trainer - INFO - Evaluation logs will be saved to outputs_a100_fixed/logs/eval/eval_epoch_100.log
2025-05-27 15:40:43,909 - train.trainer - INFO - Generating 995 samples for evaluation...
2025-05-27 15:40:43,909 - train.trainer - INFO - Using PulsarAdaptiveSampler for evaluation
2025-05-27 15:40:43,910 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:43,911 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0054, std=1.0003
2025-05-27 15:40:44,006 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2497, std=0.1420, range=[-0.6787, 0.4892]
2025-05-27 15:40:46,849 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:46,850 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0013, std=0.9972
2025-05-27 15:40:46,950 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2701, std=0.1399, range=[-0.8374, 0.7001]
2025-05-27 15:40:46,952 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:46,952 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0005, std=1.0015
2025-05-27 15:40:47,157 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2670, std=0.1179, range=[-0.5935, 0.2772]
2025-05-27 15:40:47,158 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:47,158 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0005, std=0.9990
2025-05-27 15:40:47,357 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2660, std=0.1189, range=[-0.5852, 0.2988]
2025-05-27 15:40:47,358 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:47,358 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0016, std=0.9989
2025-05-27 15:40:47,557 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2742, std=0.1408, range=[-0.7205, 0.3519]
2025-05-27 15:40:47,558 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:47,558 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0016, std=0.9990
2025-05-27 15:40:47,757 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2716, std=0.1354, range=[-0.6875, 0.3439]
2025-05-27 15:40:47,758 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:47,759 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0005, std=0.9974
2025-05-27 15:40:47,965 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2607, std=0.1395, range=[-0.6496, 0.5768]
2025-05-27 15:40:47,966 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:47,967 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0008, std=0.9941
2025-05-27 15:40:48,154 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2659, std=0.1213, range=[-0.6147, 0.2133]
2025-05-27 15:40:48,156 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:48,156 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0063, std=0.9939
2025-05-27 15:40:48,368 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2591, std=0.1546, range=[-0.7601, 0.5198]
2025-05-27 15:40:48,381 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:48,381 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0005, std=0.9985
2025-05-27 15:40:48,649 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2592, std=0.1461, range=[-0.6387, 0.4974]
2025-05-27 15:40:48,651 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:48,651 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0032, std=0.9995
2025-05-27 15:40:48,855 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2719, std=0.1165, range=[-0.6739, 0.2103]
2025-05-27 15:40:48,856 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:48,856 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0032, std=0.9983
2025-05-27 15:40:49,053 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2573, std=0.1276, range=[-0.6003, 0.4608]
2025-05-27 15:40:49,054 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:49,054 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0008, std=0.9965
2025-05-27 15:40:49,250 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2568, std=0.1605, range=[-0.7050, 0.5565]
2025-05-27 15:40:49,251 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:49,251 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0002, std=0.9960
2025-05-27 15:40:49,461 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2647, std=0.1436, range=[-0.7596, 0.3686]
2025-05-27 15:40:49,462 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:49,462 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0011, std=0.9981
2025-05-27 15:40:49,664 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2766, std=0.1791, range=[-0.7627, 0.6386]
2025-05-27 15:40:49,665 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:49,666 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0035, std=0.9960
2025-05-27 15:40:49,861 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2736, std=0.1418, range=[-0.7554, 0.4794]
2025-05-27 15:40:49,862 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:49,862 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0037, std=0.9992
2025-05-27 15:40:50,057 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2602, std=0.1443, range=[-0.7028, 0.3463]
2025-05-27 15:40:50,080 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:50,080 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0033, std=0.9961
2025-05-27 15:40:50,330 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2590, std=0.1299, range=[-0.6702, 0.6263]
2025-05-27 15:40:50,331 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:40:50,331 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0020, std=0.9966
2025-05-27 15:40:50,551 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2468, std=0.1492, range=[-0.6803, 0.4602]
2025-05-27 15:40:50,552 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=45, steps=20
2025-05-27 15:40:50,552 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0020, std=0.9980
2025-05-27 15:40:50,761 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2691, std=0.1231, range=[-0.6808, 0.3755]
2025-05-27 15:40:50,785 - train.trainer - INFO - Generated 995 images with shape torch.Size([995, 3, 32, 32])
2025-05-27 15:40:50,889 - train.trainer - INFO - Saved 100 samples to outputs_a100_fixed/checkpoints/eval_epoch_100/generated_samples.pt
2025-05-27 15:40:50,890 - train.trainer - INFO - Loading real images for evaluation...
2025-05-27 15:40:52,583 - train.trainer - INFO - Loaded 995 real images with shape torch.Size([995, 3, 32, 32])
2025-05-27 15:40:52,583 - train.trainer - INFO - Calculating overall FID and IS metrics...
2025-05-27 15:42:16,555 - train.trainer - INFO - Overall evaluation completed: FID=282.0493, IS=1.8701±0.1049
2025-05-27 15:42:16,556 - train.trainer - INFO - Calculating channel-specific metrics...
2025-05-27 15:46:55,970 - train.trainer - INFO - Channel-specific evaluation completed
2025-05-27 15:46:55,970 - train.trainer - INFO - ==================================================
2025-05-27 15:46:55,970 - train.trainer - INFO - EVALUATION RESULTS (Epoch 100):
2025-05-27 15:46:55,970 - train.trainer - INFO - ==================================================
2025-05-27 15:46:55,971 - train.trainer - INFO - Overall FID: 282.0493
2025-05-27 15:46:55,971 - train.trainer - INFO - Overall IS: 1.8701 ± 0.1049
2025-05-27 15:46:55,971 - train.trainer - INFO - --------------------------------------------------
2025-05-27 15:46:55,971 - train.trainer - INFO - Period-DM surface:
2025-05-27 15:46:55,971 - train.trainer - INFO -   FID = 359.2363
2025-05-27 15:46:55,971 - train.trainer - INFO -   IS  = 1.9826 ± 0.1201
2025-05-27 15:46:55,971 - train.trainer - INFO - --------------------------------------------------
2025-05-27 15:46:55,971 - train.trainer - INFO - Phase-Subband surface:
2025-05-27 15:46:55,971 - train.trainer - INFO -   FID = 303.1403
2025-05-27 15:46:55,971 - train.trainer - INFO -   IS  = 2.0328 ± 0.1278
2025-05-27 15:46:55,971 - train.trainer - INFO - --------------------------------------------------
2025-05-27 15:46:55,971 - train.trainer - INFO - Phase-Subintegration surface:
2025-05-27 15:46:55,971 - train.trainer - INFO -   FID = 300.8172
2025-05-27 15:46:55,971 - train.trainer - INFO -   IS  = 1.7191 ± 0.0486
2025-05-27 15:46:55,971 - train.trainer - INFO - --------------------------------------------------
2025-05-27 15:46:55,971 - train.trainer - INFO - Evaluation completed in 372.07 seconds
2025-05-27 15:46:55,976 - train.trainer - INFO - Saved evaluation results to outputs_a100_fixed/logs/eval/results_epoch_100.json
2025-05-27 15:46:55,976 - train.trainer - INFO - Removed evaluation log handler
2025-05-27 15:46:55,979 - train.trainer - INFO - Evaluation metrics at epoch 100:
2025-05-27 15:46:55,979 - train.trainer - INFO -   FID: 282.0493280854756
2025-05-27 15:46:55,979 - train.trainer - INFO -   IS: 1.870104193687439 ± 0.10485862195491791
2025-05-27 15:46:55,979 - train.trainer - INFO - Early stopping: No improvement at epoch 100 (current FID: 282.0493, best: 269.1612, counter: 1/3)
2025-05-27 15:46:56,025 - train.progressive_trainer - INFO - 检查点已保存: outputs_a100_fixed/checkpoints/checkpoint_epoch_100.pt
2025-05-27 15:46:56,693 - train.progressive_trainer - INFO - [0/62] Epoch 101/400 Stage 1 lr:0.000016 total_loss:0.013597
2025-05-27 15:47:06,126 - train.progressive_trainer - INFO - [50/62] Epoch 101/400 Stage 1 lr:0.000016 total_loss:0.011905
2025-05-27 15:47:07,617 - train.progressive_trainer - INFO - [61/62] Epoch 101/400 Stage 1 lr:0.000016 total_loss:0.012965
2025-05-27 15:47:08,661 - train.progressive_trainer - INFO - [0/62] Epoch 102/400 Stage 1 lr:0.000016 total_loss:0.012220
2025-05-27 15:47:18,116 - train.progressive_trainer - INFO - [50/62] Epoch 102/400 Stage 1 lr:0.000016 total_loss:0.011666
2025-05-27 15:47:20,211 - train.progressive_trainer - INFO - [61/62] Epoch 102/400 Stage 1 lr:0.000016 total_loss:0.012143
2025-05-27 15:47:21,062 - train.progressive_trainer - INFO - [0/62] Epoch 103/400 Stage 1 lr:0.000016 total_loss:0.012323
2025-05-27 15:47:30,401 - train.progressive_trainer - INFO - [50/62] Epoch 103/400 Stage 1 lr:0.000016 total_loss:0.013144
2025-05-27 15:47:32,055 - train.progressive_trainer - INFO - [61/62] Epoch 103/400 Stage 1 lr:0.000016 total_loss:0.012049
2025-05-27 15:47:32,599 - train.progressive_trainer - INFO - [0/62] Epoch 104/400 Stage 1 lr:0.000016 total_loss:0.012506
2025-05-27 15:47:41,708 - train.progressive_trainer - INFO - [50/62] Epoch 104/400 Stage 1 lr:0.000016 total_loss:0.016190
2025-05-27 15:47:43,616 - train.progressive_trainer - INFO - [61/62] Epoch 104/400 Stage 1 lr:0.000016 total_loss:0.012440
2025-05-27 15:47:45,226 - train.progressive_trainer - INFO - [0/62] Epoch 105/400 Stage 1 lr:0.000016 total_loss:0.011771
2025-05-27 15:47:54,434 - train.progressive_trainer - INFO - [50/62] Epoch 105/400 Stage 1 lr:0.000016 total_loss:0.011802
2025-05-27 15:47:56,418 - train.progressive_trainer - INFO - [61/62] Epoch 105/400 Stage 1 lr:0.000016 total_loss:0.012338
2025-05-27 15:47:57,257 - train.progressive_trainer - INFO - [0/62] Epoch 106/400 Stage 1 lr:0.000016 total_loss:0.020650
2025-05-27 15:48:06,428 - train.progressive_trainer - INFO - [50/62] Epoch 106/400 Stage 1 lr:0.000016 total_loss:0.012600
2025-05-27 15:48:08,326 - train.progressive_trainer - INFO - [61/62] Epoch 106/400 Stage 1 lr:0.000016 total_loss:0.012709
2025-05-27 15:48:09,360 - train.progressive_trainer - INFO - [0/62] Epoch 107/400 Stage 1 lr:0.000016 total_loss:0.011671
2025-05-27 15:48:18,224 - train.progressive_trainer - INFO - [50/62] Epoch 107/400 Stage 1 lr:0.000016 total_loss:0.011508
2025-05-27 15:48:20,124 - train.progressive_trainer - INFO - [61/62] Epoch 107/400 Stage 1 lr:0.000016 total_loss:0.011605
2025-05-27 15:48:20,961 - train.progressive_trainer - INFO - [0/62] Epoch 108/400 Stage 1 lr:0.000016 total_loss:0.011790
2025-05-27 15:48:29,583 - train.progressive_trainer - INFO - [50/62] Epoch 108/400 Stage 1 lr:0.000016 total_loss:0.011741
2025-05-27 15:48:31,324 - train.progressive_trainer - INFO - [61/62] Epoch 108/400 Stage 1 lr:0.000016 total_loss:0.012055
2025-05-27 15:48:32,155 - train.progressive_trainer - INFO - [0/62] Epoch 109/400 Stage 1 lr:0.000016 total_loss:0.011593
2025-05-27 15:48:41,185 - train.progressive_trainer - INFO - [50/62] Epoch 109/400 Stage 1 lr:0.000016 total_loss:0.011508
2025-05-27 15:48:43,108 - train.progressive_trainer - INFO - [61/62] Epoch 109/400 Stage 1 lr:0.000016 total_loss:0.025591
2025-05-27 15:48:44,633 - train.progressive_trainer - INFO - [0/62] Epoch 110/400 Stage 1 lr:0.000016 total_loss:0.010818
2025-05-27 15:48:54,117 - train.progressive_trainer - INFO - [50/62] Epoch 110/400 Stage 1 lr:0.000016 total_loss:0.013192
2025-05-27 15:48:56,080 - train.progressive_trainer - INFO - [61/62] Epoch 110/400 Stage 1 lr:0.000016 total_loss:0.010710
2025-05-27 15:48:56,892 - train.progressive_trainer - INFO - [0/62] Epoch 111/400 Stage 1 lr:0.000016 total_loss:0.010481
2025-05-27 15:49:05,714 - train.progressive_trainer - INFO - [50/62] Epoch 111/400 Stage 1 lr:0.000016 total_loss:0.010775
2025-05-27 15:49:07,819 - train.progressive_trainer - INFO - [61/62] Epoch 111/400 Stage 1 lr:0.000016 total_loss:0.010540
2025-05-27 15:49:08,854 - train.progressive_trainer - INFO - [0/62] Epoch 112/400 Stage 1 lr:0.000016 total_loss:0.011436
2025-05-27 15:49:17,283 - train.progressive_trainer - INFO - [50/62] Epoch 112/400 Stage 1 lr:0.000016 total_loss:0.011761
2025-05-27 15:49:19,118 - train.progressive_trainer - INFO - [61/62] Epoch 112/400 Stage 1 lr:0.000016 total_loss:0.011166
2025-05-27 15:49:20,356 - train.progressive_trainer - INFO - [0/62] Epoch 113/400 Stage 1 lr:0.000016 total_loss:0.010475
2025-05-27 15:49:28,525 - train.progressive_trainer - INFO - [50/62] Epoch 113/400 Stage 1 lr:0.000016 total_loss:0.010487
2025-05-27 15:49:30,326 - train.progressive_trainer - INFO - [61/62] Epoch 113/400 Stage 1 lr:0.000016 total_loss:0.010676
2025-05-27 15:49:31,352 - train.progressive_trainer - INFO - [0/62] Epoch 114/400 Stage 1 lr:0.000016 total_loss:0.010468
2025-05-27 15:49:39,572 - train.progressive_trainer - INFO - [50/62] Epoch 114/400 Stage 1 lr:0.000016 total_loss:0.013757
2025-05-27 15:49:41,286 - train.progressive_trainer - INFO - [61/62] Epoch 114/400 Stage 1 lr:0.000016 total_loss:0.010192
2025-05-27 15:49:42,053 - train.progressive_trainer - INFO - [0/62] Epoch 115/400 Stage 1 lr:0.000016 total_loss:0.013051
2025-05-27 15:49:51,215 - train.progressive_trainer - INFO - [50/62] Epoch 115/400 Stage 1 lr:0.000016 total_loss:0.010652
2025-05-27 15:49:52,824 - train.progressive_trainer - INFO - [61/62] Epoch 115/400 Stage 1 lr:0.000016 total_loss:0.010748
2025-05-27 15:49:53,683 - train.progressive_trainer - INFO - [0/62] Epoch 116/400 Stage 1 lr:0.000016 total_loss:0.010491
2025-05-27 15:50:02,522 - train.progressive_trainer - INFO - [50/62] Epoch 116/400 Stage 1 lr:0.000016 total_loss:0.009966
2025-05-27 15:50:04,312 - train.progressive_trainer - INFO - [61/62] Epoch 116/400 Stage 1 lr:0.000016 total_loss:0.010694
2025-05-27 15:50:05,659 - train.progressive_trainer - INFO - [0/62] Epoch 117/400 Stage 1 lr:0.000016 total_loss:0.010481
2025-05-27 15:50:15,126 - train.progressive_trainer - INFO - [50/62] Epoch 117/400 Stage 1 lr:0.000016 total_loss:0.011880
2025-05-27 15:50:17,020 - train.progressive_trainer - INFO - [61/62] Epoch 117/400 Stage 1 lr:0.000016 total_loss:0.009951
2025-05-27 15:50:17,746 - train.progressive_trainer - INFO - [0/62] Epoch 118/400 Stage 1 lr:0.000016 total_loss:0.010530
2025-05-27 15:50:26,471 - train.progressive_trainer - INFO - [50/62] Epoch 118/400 Stage 1 lr:0.000016 total_loss:0.010931
2025-05-27 15:50:28,422 - train.progressive_trainer - INFO - [61/62] Epoch 118/400 Stage 1 lr:0.000016 total_loss:0.010397
2025-05-27 15:50:29,860 - train.progressive_trainer - INFO - [0/62] Epoch 119/400 Stage 1 lr:0.000016 total_loss:0.023214
2025-05-27 15:50:38,474 - train.progressive_trainer - INFO - [50/62] Epoch 119/400 Stage 1 lr:0.000016 total_loss:0.009629
2025-05-27 15:50:40,523 - train.progressive_trainer - INFO - [61/62] Epoch 119/400 Stage 1 lr:0.000016 total_loss:0.009609
2025-05-27 15:50:41,354 - train.progressive_trainer - INFO - [0/62] Epoch 120/400 Stage 1 lr:0.000016 total_loss:0.010672
2025-05-27 15:50:50,030 - train.progressive_trainer - INFO - [50/62] Epoch 120/400 Stage 1 lr:0.000016 total_loss:0.009779
2025-05-27 15:50:51,819 - train.progressive_trainer - INFO - [61/62] Epoch 120/400 Stage 1 lr:0.000016 total_loss:0.010152
2025-05-27 15:50:52,564 - train.progressive_trainer - INFO - [0/62] Epoch 121/400 Stage 1 lr:0.000016 total_loss:0.011731
2025-05-27 15:51:01,608 - train.progressive_trainer - INFO - [50/62] Epoch 121/400 Stage 1 lr:0.000016 total_loss:0.009574
2025-05-27 15:51:03,605 - train.progressive_trainer - INFO - [61/62] Epoch 121/400 Stage 1 lr:0.000016 total_loss:0.009372
2025-05-27 15:51:04,856 - train.progressive_trainer - INFO - [0/62] Epoch 122/400 Stage 1 lr:0.000016 total_loss:0.010330
2025-05-27 15:51:12,815 - train.progressive_trainer - INFO - [50/62] Epoch 122/400 Stage 1 lr:0.000016 total_loss:0.009511
2025-05-27 15:51:14,916 - train.progressive_trainer - INFO - [61/62] Epoch 122/400 Stage 1 lr:0.000016 total_loss:0.020440
2025-05-27 15:51:16,357 - train.progressive_trainer - INFO - [0/62] Epoch 123/400 Stage 1 lr:0.000016 total_loss:0.009532
2025-05-27 15:51:25,705 - train.progressive_trainer - INFO - [50/62] Epoch 123/400 Stage 1 lr:0.000016 total_loss:0.010321
2025-05-27 15:51:27,424 - train.progressive_trainer - INFO - [61/62] Epoch 123/400 Stage 1 lr:0.000016 total_loss:0.014817
2025-05-27 15:51:28,257 - train.progressive_trainer - INFO - [0/62] Epoch 124/400 Stage 1 lr:0.000016 total_loss:0.010018
2025-05-27 15:51:36,823 - train.progressive_trainer - INFO - [50/62] Epoch 124/400 Stage 1 lr:0.000016 total_loss:0.009307
2025-05-27 15:51:38,623 - train.progressive_trainer - INFO - [61/62] Epoch 124/400 Stage 1 lr:0.000016 total_loss:0.009379
2025-05-27 15:51:39,636 - train.progressive_trainer - INFO - [0/62] Epoch 125/400 Stage 1 lr:0.000016 total_loss:0.009592
2025-05-27 15:51:47,823 - train.progressive_trainer - INFO - [50/62] Epoch 125/400 Stage 1 lr:0.000016 total_loss:0.009281
2025-05-27 15:51:49,798 - train.progressive_trainer - INFO - [61/62] Epoch 125/400 Stage 1 lr:0.000016 total_loss:0.009880
2025-05-27 15:51:50,557 - train.progressive_trainer - INFO - [0/62] Epoch 126/400 Stage 1 lr:0.000016 total_loss:0.009176
2025-05-27 15:51:59,718 - train.progressive_trainer - INFO - [50/62] Epoch 126/400 Stage 1 lr:0.000016 total_loss:0.009465
2025-05-27 15:52:01,923 - train.progressive_trainer - INFO - [61/62] Epoch 126/400 Stage 1 lr:0.000016 total_loss:0.016035
2025-05-27 15:52:02,761 - train.progressive_trainer - INFO - [0/62] Epoch 127/400 Stage 1 lr:0.000016 total_loss:0.009101
2025-05-27 15:52:11,216 - train.progressive_trainer - INFO - [50/62] Epoch 127/400 Stage 1 lr:0.000016 total_loss:0.009940
2025-05-27 15:52:12,928 - train.progressive_trainer - INFO - [61/62] Epoch 127/400 Stage 1 lr:0.000016 total_loss:0.008810
2025-05-27 15:52:13,759 - train.progressive_trainer - INFO - [0/62] Epoch 128/400 Stage 1 lr:0.000016 total_loss:0.009047
2025-05-27 15:52:22,899 - train.progressive_trainer - INFO - [50/62] Epoch 128/400 Stage 1 lr:0.000016 total_loss:0.018121
2025-05-27 15:52:24,817 - train.progressive_trainer - INFO - [61/62] Epoch 128/400 Stage 1 lr:0.000016 total_loss:0.008665
2025-05-27 15:52:25,766 - train.progressive_trainer - INFO - [0/62] Epoch 129/400 Stage 1 lr:0.000016 total_loss:0.009054
2025-05-27 15:52:34,319 - train.progressive_trainer - INFO - [50/62] Epoch 129/400 Stage 1 lr:0.000016 total_loss:0.010256
2025-05-27 15:52:36,084 - train.progressive_trainer - INFO - [61/62] Epoch 129/400 Stage 1 lr:0.000016 total_loss:0.009858
2025-05-27 15:52:37,060 - train.progressive_trainer - INFO - [0/62] Epoch 130/400 Stage 1 lr:0.000016 total_loss:0.009713
2025-05-27 15:52:45,441 - train.progressive_trainer - INFO - [50/62] Epoch 130/400 Stage 1 lr:0.000016 total_loss:0.009363
2025-05-27 15:52:47,320 - train.progressive_trainer - INFO - [61/62] Epoch 130/400 Stage 1 lr:0.000016 total_loss:0.010455
2025-05-27 15:52:48,762 - train.progressive_trainer - INFO - [0/62] Epoch 131/400 Stage 1 lr:0.000016 total_loss:0.008942
2025-05-27 15:52:57,284 - train.progressive_trainer - INFO - [50/62] Epoch 131/400 Stage 1 lr:0.000016 total_loss:0.020715
2025-05-27 15:52:59,064 - train.progressive_trainer - INFO - [61/62] Epoch 131/400 Stage 1 lr:0.000016 total_loss:0.008232
2025-05-27 15:53:00,553 - train.progressive_trainer - INFO - [0/62] Epoch 132/400 Stage 1 lr:0.000016 total_loss:0.008421
2025-05-27 15:53:09,115 - train.progressive_trainer - INFO - [50/62] Epoch 132/400 Stage 1 lr:0.000016 total_loss:0.008042
2025-05-27 15:53:11,029 - train.progressive_trainer - INFO - [61/62] Epoch 132/400 Stage 1 lr:0.000016 total_loss:0.009084
2025-05-27 15:53:11,867 - train.progressive_trainer - INFO - [0/62] Epoch 133/400 Stage 1 lr:0.000016 total_loss:0.010786
2025-05-27 15:53:21,291 - train.progressive_trainer - INFO - [50/62] Epoch 133/400 Stage 1 lr:0.000016 total_loss:0.008369
2025-05-27 15:53:23,327 - train.progressive_trainer - INFO - [61/62] Epoch 133/400 Stage 1 lr:0.000016 total_loss:0.008481
2025-05-27 15:53:24,133 - train.progressive_trainer - INFO - [0/62] Epoch 134/400 Stage 1 lr:0.000016 total_loss:0.008333
2025-05-27 15:53:32,330 - train.progressive_trainer - INFO - [50/62] Epoch 134/400 Stage 1 lr:0.000016 total_loss:0.019567
2025-05-27 15:53:34,124 - train.progressive_trainer - INFO - [61/62] Epoch 134/400 Stage 1 lr:0.000016 total_loss:0.008550
2025-05-27 15:53:35,660 - train.progressive_trainer - INFO - [0/62] Epoch 135/400 Stage 1 lr:0.000016 total_loss:0.008497
2025-05-27 15:53:43,774 - train.progressive_trainer - INFO - [50/62] Epoch 135/400 Stage 1 lr:0.000016 total_loss:0.008143
2025-05-27 15:53:45,615 - train.progressive_trainer - INFO - [61/62] Epoch 135/400 Stage 1 lr:0.000016 total_loss:0.008266
2025-05-27 15:53:46,416 - train.progressive_trainer - INFO - [0/62] Epoch 136/400 Stage 1 lr:0.000016 total_loss:0.008407
2025-05-27 15:53:54,593 - train.progressive_trainer - INFO - [50/62] Epoch 136/400 Stage 1 lr:0.000016 total_loss:0.008967
2025-05-27 15:53:56,415 - train.progressive_trainer - INFO - [61/62] Epoch 136/400 Stage 1 lr:0.000016 total_loss:0.008397
2025-05-27 15:53:57,123 - train.progressive_trainer - INFO - [0/62] Epoch 137/400 Stage 1 lr:0.000016 total_loss:0.008137
2025-05-27 15:54:04,678 - train.progressive_trainer - INFO - [50/62] Epoch 137/400 Stage 1 lr:0.000016 total_loss:0.008275
2025-05-27 15:54:06,430 - train.progressive_trainer - INFO - [61/62] Epoch 137/400 Stage 1 lr:0.000016 total_loss:0.008777
2025-05-27 15:54:07,162 - train.progressive_trainer - INFO - [0/62] Epoch 138/400 Stage 1 lr:0.000016 total_loss:0.011769
2025-05-27 15:54:16,317 - train.progressive_trainer - INFO - [50/62] Epoch 138/400 Stage 1 lr:0.000016 total_loss:0.007747
2025-05-27 15:54:18,024 - train.progressive_trainer - INFO - [61/62] Epoch 138/400 Stage 1 lr:0.000016 total_loss:0.019854
2025-05-27 15:54:19,760 - train.progressive_trainer - INFO - [0/62] Epoch 139/400 Stage 1 lr:0.000016 total_loss:0.007868
2025-05-27 15:54:28,612 - train.progressive_trainer - INFO - [50/62] Epoch 139/400 Stage 1 lr:0.000016 total_loss:0.008197
2025-05-27 15:54:30,614 - train.progressive_trainer - INFO - [61/62] Epoch 139/400 Stage 1 lr:0.000016 total_loss:0.007872
2025-05-27 15:54:31,345 - train.progressive_trainer - INFO - [0/62] Epoch 140/400 Stage 1 lr:0.000016 total_loss:0.007949
2025-05-27 15:54:40,815 - train.progressive_trainer - INFO - [50/62] Epoch 140/400 Stage 1 lr:0.000016 total_loss:0.008134
2025-05-27 15:54:42,387 - train.progressive_trainer - INFO - [61/62] Epoch 140/400 Stage 1 lr:0.000016 total_loss:0.007723
2025-05-27 15:54:43,159 - train.progressive_trainer - INFO - [0/62] Epoch 141/400 Stage 1 lr:0.000016 total_loss:0.009982
2025-05-27 15:54:52,340 - train.progressive_trainer - INFO - [50/62] Epoch 141/400 Stage 1 lr:0.000016 total_loss:0.007996
2025-05-27 15:54:53,808 - train.progressive_trainer - INFO - [61/62] Epoch 141/400 Stage 1 lr:0.000016 total_loss:0.009297
2025-05-27 15:54:54,687 - train.progressive_trainer - INFO - [0/62] Epoch 142/400 Stage 1 lr:0.000016 total_loss:0.008902
2025-05-27 15:55:03,298 - train.progressive_trainer - INFO - [50/62] Epoch 142/400 Stage 1 lr:0.000016 total_loss:0.009471
2025-05-27 15:55:04,808 - train.progressive_trainer - INFO - [61/62] Epoch 142/400 Stage 1 lr:0.000016 total_loss:0.007901
2025-05-27 15:55:05,642 - train.progressive_trainer - INFO - [0/62] Epoch 143/400 Stage 1 lr:0.000016 total_loss:0.007649
2025-05-27 15:55:13,605 - train.progressive_trainer - INFO - [50/62] Epoch 143/400 Stage 1 lr:0.000016 total_loss:0.008082
2025-05-27 15:55:15,264 - train.progressive_trainer - INFO - [61/62] Epoch 143/400 Stage 1 lr:0.000016 total_loss:0.013272
2025-05-27 15:55:16,259 - train.progressive_trainer - INFO - [0/62] Epoch 144/400 Stage 1 lr:0.000016 total_loss:0.010907
2025-05-27 15:55:24,799 - train.progressive_trainer - INFO - [50/62] Epoch 144/400 Stage 1 lr:0.000016 total_loss:0.010187
2025-05-27 15:55:26,522 - train.progressive_trainer - INFO - [61/62] Epoch 144/400 Stage 1 lr:0.000016 total_loss:0.007755
2025-05-27 15:55:27,329 - train.progressive_trainer - INFO - [0/62] Epoch 145/400 Stage 1 lr:0.000016 total_loss:0.007728
2025-05-27 15:55:36,024 - train.progressive_trainer - INFO - [50/62] Epoch 145/400 Stage 1 lr:0.000016 total_loss:0.007848
2025-05-27 15:55:37,898 - train.progressive_trainer - INFO - [61/62] Epoch 145/400 Stage 1 lr:0.000016 total_loss:0.007438
2025-05-27 15:55:38,645 - train.progressive_trainer - INFO - [0/62] Epoch 146/400 Stage 1 lr:0.000016 total_loss:0.007432
2025-05-27 15:55:47,315 - train.progressive_trainer - INFO - [50/62] Epoch 146/400 Stage 1 lr:0.000016 total_loss:0.007670
2025-05-27 15:55:48,723 - train.progressive_trainer - INFO - [61/62] Epoch 146/400 Stage 1 lr:0.000016 total_loss:0.007500
2025-05-27 15:55:50,232 - train.progressive_trainer - INFO - [0/62] Epoch 147/400 Stage 1 lr:0.000016 total_loss:0.007968
2025-05-27 15:55:58,513 - train.progressive_trainer - INFO - [50/62] Epoch 147/400 Stage 1 lr:0.000016 total_loss:0.007884
2025-05-27 15:56:00,322 - train.progressive_trainer - INFO - [61/62] Epoch 147/400 Stage 1 lr:0.000016 total_loss:0.007130
2025-05-27 15:56:01,256 - train.progressive_trainer - INFO - [0/62] Epoch 148/400 Stage 1 lr:0.000016 total_loss:0.008212
2025-05-27 15:56:10,088 - train.progressive_trainer - INFO - [50/62] Epoch 148/400 Stage 1 lr:0.000016 total_loss:0.011392
2025-05-27 15:56:12,215 - train.progressive_trainer - INFO - [61/62] Epoch 148/400 Stage 1 lr:0.000016 total_loss:0.007647
2025-05-27 15:56:13,059 - train.progressive_trainer - INFO - [0/62] Epoch 149/400 Stage 1 lr:0.000016 total_loss:0.006974
2025-05-27 15:56:21,711 - train.progressive_trainer - INFO - [50/62] Epoch 149/400 Stage 1 lr:0.000016 total_loss:0.007259
2025-05-27 15:56:23,699 - train.progressive_trainer - INFO - [61/62] Epoch 149/400 Stage 1 lr:0.000016 total_loss:0.007313
2025-05-27 15:56:23,800 - train.progressive_trainer - INFO - 使用优化损失函数，切换到阶段: physics
2025-05-27 15:56:23,800 - train.progressive_trainer - INFO - ============================================================
2025-05-27 15:56:23,800 - train.progressive_trainer - INFO - 训练阶段切换: 阶段1 → 阶段2
2025-05-27 15:56:23,801 - train.progressive_trainer - INFO - 新阶段: physics
2025-05-27 15:56:23,801 - train.progressive_trainer - INFO - 学习率调整: 0.000080 → 0.000072
2025-05-27 15:56:23,801 - train.progressive_trainer - INFO - 物理损失权重: 优化损失函数激活
2025-05-27 15:56:23,801 - train.progressive_trainer - INFO - ============================================================
2025-05-27 15:56:24,554 - train.progressive_trainer - INFO - [0/62] Epoch 150/400 Stage 2 lr:0.000016 total_loss:0.008199
2025-05-27 15:56:33,224 - train.progressive_trainer - INFO - [50/62] Epoch 150/400 Stage 2 lr:0.000016 total_loss:0.007352
2025-05-27 15:56:34,498 - train.progressive_trainer - INFO - [61/62] Epoch 150/400 Stage 2 lr:0.000016 total_loss:0.010310
2025-05-27 15:56:34,600 - train.trainer - INFO - Running scheduled evaluation at epoch 150 (eval_every=50)...
2025-05-27 15:56:34,600 - train.trainer - INFO - Evaluating model at epoch 150...
2025-05-27 15:56:34,604 - train.trainer - INFO - Evaluation logs will be saved to outputs_a100_fixed/logs/eval/eval_epoch_150.log
2025-05-27 15:56:34,605 - train.trainer - INFO - Generating 995 samples for evaluation...
2025-05-27 15:56:34,606 - train.trainer - INFO - Using PulsarAdaptiveSampler for evaluation
2025-05-27 15:56:34,608 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:34,608 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0008, std=1.0009
2025-05-27 15:56:34,708 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2490, std=0.1676, range=[-0.6742, 0.6671]
2025-05-27 15:56:36,598 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:36,599 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0002, std=0.9961
2025-05-27 15:56:36,700 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2514, std=0.1536, range=[-0.7742, 0.4155]
2025-05-27 15:56:36,701 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:36,701 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0031, std=0.9954
2025-05-27 15:56:36,929 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2257, std=0.1932, range=[-0.8554, 0.6555]
2025-05-27 15:56:36,931 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:36,931 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0029, std=0.9948
2025-05-27 15:56:37,152 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2578, std=0.1489, range=[-0.6929, 0.4739]
2025-05-27 15:56:37,153 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:37,153 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0051, std=0.9967
2025-05-27 15:56:37,362 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2558, std=0.1572, range=[-0.6643, 0.7634]
2025-05-27 15:56:37,363 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:37,364 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0024, std=0.9951
2025-05-27 15:56:37,562 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2563, std=0.1382, range=[-0.7051, 0.3498]
2025-05-27 15:56:37,563 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:37,564 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0006, std=0.9956
2025-05-27 15:56:37,769 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2411, std=0.1712, range=[-0.7114, 0.5526]
2025-05-27 15:56:37,770 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:37,770 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0045, std=0.9956
2025-05-27 15:56:37,964 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2573, std=0.1412, range=[-0.6225, 0.3904]
2025-05-27 15:56:37,965 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:37,966 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0025, std=0.9979
2025-05-27 15:56:38,161 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2675, std=0.1454, range=[-0.8530, 0.3332]
2025-05-27 15:56:38,162 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:38,162 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0020, std=0.9984
2025-05-27 15:56:38,360 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2516, std=0.1606, range=[-0.8210, 0.6778]
2025-05-27 15:56:38,361 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:38,361 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0032, std=0.9943
2025-05-27 15:56:38,558 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2597, std=0.1658, range=[-0.8136, 0.5527]
2025-05-27 15:56:38,559 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:38,560 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0039, std=0.9993
2025-05-27 15:56:38,759 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2439, std=0.1431, range=[-0.6752, 0.4761]
2025-05-27 15:56:38,760 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:38,760 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0017, std=0.9978
2025-05-27 15:56:38,958 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2555, std=0.1441, range=[-0.6875, 0.3746]
2025-05-27 15:56:38,960 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:38,960 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0009, std=0.9956
2025-05-27 15:56:39,157 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2544, std=0.1424, range=[-0.7124, 0.3928]
2025-05-27 15:56:39,159 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:39,159 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0034, std=0.9945
2025-05-27 15:56:39,363 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2393, std=0.1644, range=[-0.6924, 0.6013]
2025-05-27 15:56:39,364 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:39,364 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0008, std=1.0004
2025-05-27 15:56:39,562 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2519, std=0.1479, range=[-0.6159, 0.5245]
2025-05-27 15:56:39,563 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:39,563 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0004, std=0.9970
2025-05-27 15:56:39,768 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2423, std=0.1557, range=[-0.8540, 0.3398]
2025-05-27 15:56:39,769 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:39,770 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0000, std=0.9974
2025-05-27 15:56:39,957 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2576, std=0.1587, range=[-0.8103, 0.7836]
2025-05-27 15:56:39,958 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 15:56:39,958 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0037, std=0.9986
2025-05-27 15:56:40,173 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2544, std=0.1420, range=[-0.5853, 0.4184]
2025-05-27 15:56:40,174 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=45, steps=20
2025-05-27 15:56:40,175 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0009, std=0.9987
2025-05-27 15:56:40,394 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2487, std=0.1365, range=[-0.6317, 0.4458]
2025-05-27 15:56:40,682 - train.trainer - INFO - Generated 995 images with shape torch.Size([995, 3, 32, 32])
2025-05-27 15:56:40,794 - train.trainer - INFO - Saved 100 samples to outputs_a100_fixed/checkpoints/eval_epoch_150/generated_samples.pt
2025-05-27 15:56:40,794 - train.trainer - INFO - Loading real images for evaluation...
2025-05-27 15:56:42,596 - train.trainer - INFO - Loaded 995 real images with shape torch.Size([995, 3, 32, 32])
2025-05-27 15:56:42,596 - train.trainer - INFO - Calculating overall FID and IS metrics...
2025-05-27 15:58:02,404 - train.trainer - INFO - Overall evaluation completed: FID=320.7370, IS=1.7037±0.0673
2025-05-27 15:58:02,405 - train.trainer - INFO - Calculating channel-specific metrics...
2025-05-27 16:01:49,941 - train.trainer - INFO - Channel-specific evaluation completed
2025-05-27 16:01:49,942 - train.trainer - INFO - ==================================================
2025-05-27 16:01:49,942 - train.trainer - INFO - EVALUATION RESULTS (Epoch 150):
2025-05-27 16:01:49,942 - train.trainer - INFO - ==================================================
2025-05-27 16:01:49,942 - train.trainer - INFO - Overall FID: 320.7370
2025-05-27 16:01:49,942 - train.trainer - INFO - Overall IS: 1.7037 ± 0.0673
2025-05-27 16:01:49,942 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:01:49,942 - train.trainer - INFO - Period-DM surface:
2025-05-27 16:01:49,942 - train.trainer - INFO -   FID = 460.2098
2025-05-27 16:01:49,942 - train.trainer - INFO -   IS  = 1.3785 ± 0.0563
2025-05-27 16:01:49,942 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:01:49,942 - train.trainer - INFO - Phase-Subband surface:
2025-05-27 16:01:49,942 - train.trainer - INFO -   FID = 301.5430
2025-05-27 16:01:49,942 - train.trainer - INFO -   IS  = 1.4499 ± 0.0324
2025-05-27 16:01:49,942 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:01:49,942 - train.trainer - INFO - Phase-Subintegration surface:
2025-05-27 16:01:49,942 - train.trainer - INFO -   FID = 367.0688
2025-05-27 16:01:49,942 - train.trainer - INFO -   IS  = 1.1274 ± 0.0132
2025-05-27 16:01:49,942 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:01:49,942 - train.trainer - INFO - Evaluation completed in 315.34 seconds
2025-05-27 16:01:49,946 - train.trainer - INFO - Saved evaluation results to outputs_a100_fixed/logs/eval/results_epoch_150.json
2025-05-27 16:01:49,946 - train.trainer - INFO - Removed evaluation log handler
2025-05-27 16:01:49,947 - train.trainer - INFO - Evaluation metrics at epoch 150:
2025-05-27 16:01:49,947 - train.trainer - INFO -   FID: 320.7370375375697
2025-05-27 16:01:49,947 - train.trainer - INFO -   IS: 1.7036619186401367 ± 0.06734178215265274
2025-05-27 16:01:49,948 - train.trainer - INFO - Early stopping: No improvement at epoch 150 (current FID: 320.7370, best: 269.1612, counter: 2/3)
2025-05-27 16:01:49,992 - train.progressive_trainer - INFO - 检查点已保存: outputs_a100_fixed/checkpoints/checkpoint_epoch_150.pt
2025-05-27 16:01:50,666 - train.progressive_trainer - INFO - [0/62] Epoch 151/400 Stage 2 lr:0.000016 total_loss:0.007578
2025-05-27 16:01:58,115 - train.progressive_trainer - INFO - [50/62] Epoch 151/400 Stage 2 lr:0.000016 total_loss:0.007265
2025-05-27 16:02:00,023 - train.progressive_trainer - INFO - [61/62] Epoch 151/400 Stage 2 lr:0.000016 total_loss:0.010927
2025-05-27 16:02:01,058 - train.progressive_trainer - INFO - [0/62] Epoch 152/400 Stage 2 lr:0.000016 total_loss:0.007508
2025-05-27 16:02:09,422 - train.progressive_trainer - INFO - [50/62] Epoch 152/400 Stage 2 lr:0.000016 total_loss:0.006612
2025-05-27 16:02:11,312 - train.progressive_trainer - INFO - [61/62] Epoch 152/400 Stage 2 lr:0.000016 total_loss:0.007879
2025-05-27 16:02:12,069 - train.progressive_trainer - INFO - [0/62] Epoch 153/400 Stage 2 lr:0.000016 total_loss:0.006919
2025-05-27 16:02:20,318 - train.progressive_trainer - INFO - [50/62] Epoch 153/400 Stage 2 lr:0.000016 total_loss:0.006966
2025-05-27 16:02:21,878 - train.progressive_trainer - INFO - [61/62] Epoch 153/400 Stage 2 lr:0.000016 total_loss:0.008528
2025-05-27 16:02:22,657 - train.progressive_trainer - INFO - [0/62] Epoch 154/400 Stage 2 lr:0.000016 total_loss:0.006759
2025-05-27 16:02:30,123 - train.progressive_trainer - INFO - [50/62] Epoch 154/400 Stage 2 lr:0.000016 total_loss:0.007134
2025-05-27 16:02:31,813 - train.progressive_trainer - INFO - [61/62] Epoch 154/400 Stage 2 lr:0.000016 total_loss:0.010271
2025-05-27 16:02:32,560 - train.progressive_trainer - INFO - [0/62] Epoch 155/400 Stage 2 lr:0.000016 total_loss:0.007395
2025-05-27 16:02:40,968 - train.progressive_trainer - INFO - [50/62] Epoch 155/400 Stage 2 lr:0.000016 total_loss:0.007324
2025-05-27 16:02:43,016 - train.progressive_trainer - INFO - [61/62] Epoch 155/400 Stage 2 lr:0.000016 total_loss:0.006970
2025-05-27 16:02:44,559 - train.progressive_trainer - INFO - [0/62] Epoch 156/400 Stage 2 lr:0.000016 total_loss:0.007542
2025-05-27 16:02:52,721 - train.progressive_trainer - INFO - [50/62] Epoch 156/400 Stage 2 lr:0.000016 total_loss:0.007248
2025-05-27 16:02:54,627 - train.progressive_trainer - INFO - [61/62] Epoch 156/400 Stage 2 lr:0.000016 total_loss:0.007209
2025-05-27 16:02:55,659 - train.progressive_trainer - INFO - [0/62] Epoch 157/400 Stage 2 lr:0.000016 total_loss:0.006941
2025-05-27 16:03:04,127 - train.progressive_trainer - INFO - [50/62] Epoch 157/400 Stage 2 lr:0.000016 total_loss:0.007090
2025-05-27 16:03:06,216 - train.progressive_trainer - INFO - [61/62] Epoch 157/400 Stage 2 lr:0.000016 total_loss:0.007386
2025-05-27 16:03:06,960 - train.progressive_trainer - INFO - [0/62] Epoch 158/400 Stage 2 lr:0.000016 total_loss:0.006785
2025-05-27 16:03:15,396 - train.progressive_trainer - INFO - [50/62] Epoch 158/400 Stage 2 lr:0.000016 total_loss:0.006468
2025-05-27 16:03:17,117 - train.progressive_trainer - INFO - [61/62] Epoch 158/400 Stage 2 lr:0.000016 total_loss:0.006922
2025-05-27 16:03:18,756 - train.progressive_trainer - INFO - [0/62] Epoch 159/400 Stage 2 lr:0.000016 total_loss:0.008266
2025-05-27 16:03:27,205 - train.progressive_trainer - INFO - [50/62] Epoch 159/400 Stage 2 lr:0.000016 total_loss:0.009167
2025-05-27 16:03:29,089 - train.progressive_trainer - INFO - [61/62] Epoch 159/400 Stage 2 lr:0.000016 total_loss:0.006903
2025-05-27 16:03:30,582 - train.progressive_trainer - INFO - [0/62] Epoch 160/400 Stage 2 lr:0.000016 total_loss:0.006683
2025-05-27 16:03:39,020 - train.progressive_trainer - INFO - [50/62] Epoch 160/400 Stage 2 lr:0.000016 total_loss:0.006557
2025-05-27 16:03:41,127 - train.progressive_trainer - INFO - [61/62] Epoch 160/400 Stage 2 lr:0.000016 total_loss:0.006759
2025-05-27 16:03:41,958 - train.progressive_trainer - INFO - [0/62] Epoch 161/400 Stage 2 lr:0.000016 total_loss:0.007441
2025-05-27 16:03:49,914 - train.progressive_trainer - INFO - [50/62] Epoch 161/400 Stage 2 lr:0.000016 total_loss:0.016087
2025-05-27 16:03:52,017 - train.progressive_trainer - INFO - [61/62] Epoch 161/400 Stage 2 lr:0.000016 total_loss:0.008177
2025-05-27 16:03:53,063 - train.progressive_trainer - INFO - [0/62] Epoch 162/400 Stage 2 lr:0.000016 total_loss:0.006874
2025-05-27 16:04:01,701 - train.progressive_trainer - INFO - [50/62] Epoch 162/400 Stage 2 lr:0.000016 total_loss:0.007663
2025-05-27 16:04:03,713 - train.progressive_trainer - INFO - [61/62] Epoch 162/400 Stage 2 lr:0.000016 total_loss:0.007024
2025-05-27 16:04:05,260 - train.progressive_trainer - INFO - [0/62] Epoch 163/400 Stage 2 lr:0.000016 total_loss:0.006650
2025-05-27 16:04:14,021 - train.progressive_trainer - INFO - [50/62] Epoch 163/400 Stage 2 lr:0.000016 total_loss:0.007212
2025-05-27 16:04:15,723 - train.progressive_trainer - INFO - [61/62] Epoch 163/400 Stage 2 lr:0.000016 total_loss:0.007393
2025-05-27 16:04:16,492 - train.progressive_trainer - INFO - [0/62] Epoch 164/400 Stage 2 lr:0.000016 total_loss:0.007496
2025-05-27 16:04:24,524 - train.progressive_trainer - INFO - [50/62] Epoch 164/400 Stage 2 lr:0.000016 total_loss:0.006391
2025-05-27 16:04:26,521 - train.progressive_trainer - INFO - [61/62] Epoch 164/400 Stage 2 lr:0.000016 total_loss:0.006499
2025-05-27 16:04:27,261 - train.progressive_trainer - INFO - [0/62] Epoch 165/400 Stage 2 lr:0.000016 total_loss:0.010671
2025-05-27 16:04:35,414 - train.progressive_trainer - INFO - [50/62] Epoch 165/400 Stage 2 lr:0.000016 total_loss:0.006613
2025-05-27 16:04:37,214 - train.progressive_trainer - INFO - [61/62] Epoch 165/400 Stage 2 lr:0.000016 total_loss:0.006309
2025-05-27 16:04:38,035 - train.progressive_trainer - INFO - [0/62] Epoch 166/400 Stage 2 lr:0.000016 total_loss:0.006048
2025-05-27 16:04:47,313 - train.progressive_trainer - INFO - [50/62] Epoch 166/400 Stage 2 lr:0.000016 total_loss:0.006042
2025-05-27 16:04:48,908 - train.progressive_trainer - INFO - [61/62] Epoch 166/400 Stage 2 lr:0.000016 total_loss:0.006127
2025-05-27 16:04:50,251 - train.progressive_trainer - INFO - [0/62] Epoch 167/400 Stage 2 lr:0.000016 total_loss:0.006947
2025-05-27 16:04:58,617 - train.progressive_trainer - INFO - [50/62] Epoch 167/400 Stage 2 lr:0.000016 total_loss:0.006222
2025-05-27 16:05:00,615 - train.progressive_trainer - INFO - [61/62] Epoch 167/400 Stage 2 lr:0.000016 total_loss:0.007166
2025-05-27 16:05:01,434 - train.progressive_trainer - INFO - [0/62] Epoch 168/400 Stage 2 lr:0.000016 total_loss:0.008862
2025-05-27 16:05:10,225 - train.progressive_trainer - INFO - [50/62] Epoch 168/400 Stage 2 lr:0.000016 total_loss:0.006238
2025-05-27 16:05:11,407 - train.progressive_trainer - INFO - [61/62] Epoch 168/400 Stage 2 lr:0.000016 total_loss:0.005887
2025-05-27 16:05:12,032 - train.progressive_trainer - INFO - [0/62] Epoch 169/400 Stage 2 lr:0.000016 total_loss:0.006147
2025-05-27 16:05:19,714 - train.progressive_trainer - INFO - [50/62] Epoch 169/400 Stage 2 lr:0.000016 total_loss:0.006385
2025-05-27 16:05:21,514 - train.progressive_trainer - INFO - [61/62] Epoch 169/400 Stage 2 lr:0.000016 total_loss:0.006254
2025-05-27 16:05:22,261 - train.progressive_trainer - INFO - [0/62] Epoch 170/400 Stage 2 lr:0.000016 total_loss:0.006418
2025-05-27 16:05:30,018 - train.progressive_trainer - INFO - [50/62] Epoch 170/400 Stage 2 lr:0.000016 total_loss:0.005922
2025-05-27 16:05:31,616 - train.progressive_trainer - INFO - [61/62] Epoch 170/400 Stage 2 lr:0.000016 total_loss:0.008549
2025-05-27 16:05:32,302 - train.progressive_trainer - INFO - [0/62] Epoch 171/400 Stage 2 lr:0.000016 total_loss:0.006296
2025-05-27 16:05:40,209 - train.progressive_trainer - INFO - [50/62] Epoch 171/400 Stage 2 lr:0.000016 total_loss:0.006156
2025-05-27 16:05:41,823 - train.progressive_trainer - INFO - [61/62] Epoch 171/400 Stage 2 lr:0.000016 total_loss:0.006289
2025-05-27 16:05:42,662 - train.progressive_trainer - INFO - [0/62] Epoch 172/400 Stage 2 lr:0.000016 total_loss:0.008133
2025-05-27 16:05:50,725 - train.progressive_trainer - INFO - [50/62] Epoch 172/400 Stage 2 lr:0.000016 total_loss:0.006114
2025-05-27 16:05:52,414 - train.progressive_trainer - INFO - [61/62] Epoch 172/400 Stage 2 lr:0.000016 total_loss:0.006521
2025-05-27 16:05:53,462 - train.progressive_trainer - INFO - [0/62] Epoch 173/400 Stage 2 lr:0.000016 total_loss:0.006089
2025-05-27 16:06:01,413 - train.progressive_trainer - INFO - [50/62] Epoch 173/400 Stage 2 lr:0.000016 total_loss:0.006756
2025-05-27 16:06:03,318 - train.progressive_trainer - INFO - [61/62] Epoch 173/400 Stage 2 lr:0.000016 total_loss:0.006057
2025-05-27 16:06:05,061 - train.progressive_trainer - INFO - [0/62] Epoch 174/400 Stage 2 lr:0.000016 total_loss:0.006397
2025-05-27 16:06:13,813 - train.progressive_trainer - INFO - [50/62] Epoch 174/400 Stage 2 lr:0.000016 total_loss:0.005925
2025-05-27 16:06:15,824 - train.progressive_trainer - INFO - [61/62] Epoch 174/400 Stage 2 lr:0.000016 total_loss:0.005776
2025-05-27 16:06:16,656 - train.progressive_trainer - INFO - [0/62] Epoch 175/400 Stage 2 lr:0.000016 total_loss:0.006219
2025-05-27 16:06:24,625 - train.progressive_trainer - INFO - [50/62] Epoch 175/400 Stage 2 lr:0.000016 total_loss:0.005820
2025-05-27 16:06:26,059 - train.progressive_trainer - INFO - [61/62] Epoch 175/400 Stage 2 lr:0.000016 total_loss:0.006013
2025-05-27 16:06:26,961 - train.progressive_trainer - INFO - [0/62] Epoch 176/400 Stage 2 lr:0.000016 total_loss:0.007152
2025-05-27 16:06:34,721 - train.progressive_trainer - INFO - [50/62] Epoch 176/400 Stage 2 lr:0.000016 total_loss:0.005656
2025-05-27 16:06:36,426 - train.progressive_trainer - INFO - [61/62] Epoch 176/400 Stage 2 lr:0.000016 total_loss:0.005938
2025-05-27 16:06:37,251 - train.progressive_trainer - INFO - [0/62] Epoch 177/400 Stage 2 lr:0.000016 total_loss:0.009892
2025-05-27 16:06:44,511 - train.progressive_trainer - INFO - [50/62] Epoch 177/400 Stage 2 lr:0.000016 total_loss:0.005787
2025-05-27 16:06:46,414 - train.progressive_trainer - INFO - [61/62] Epoch 177/400 Stage 2 lr:0.000016 total_loss:0.006131
2025-05-27 16:06:47,163 - train.progressive_trainer - INFO - [0/62] Epoch 178/400 Stage 2 lr:0.000016 total_loss:0.005994
2025-05-27 16:06:55,324 - train.progressive_trainer - INFO - [50/62] Epoch 178/400 Stage 2 lr:0.000016 total_loss:0.005916
2025-05-27 16:06:56,817 - train.progressive_trainer - INFO - [61/62] Epoch 178/400 Stage 2 lr:0.000016 total_loss:0.006115
2025-05-27 16:06:57,462 - train.progressive_trainer - INFO - [0/62] Epoch 179/400 Stage 2 lr:0.000016 total_loss:0.006474
2025-05-27 16:07:05,700 - train.progressive_trainer - INFO - [50/62] Epoch 179/400 Stage 2 lr:0.000016 total_loss:0.005467
2025-05-27 16:07:07,406 - train.progressive_trainer - INFO - [61/62] Epoch 179/400 Stage 2 lr:0.000016 total_loss:0.005827
2025-05-27 16:07:08,152 - train.progressive_trainer - INFO - [0/62] Epoch 180/400 Stage 2 lr:0.000016 total_loss:0.005658
2025-05-27 16:07:16,028 - train.progressive_trainer - INFO - [50/62] Epoch 180/400 Stage 2 lr:0.000016 total_loss:0.007064
2025-05-27 16:07:18,017 - train.progressive_trainer - INFO - [61/62] Epoch 180/400 Stage 2 lr:0.000016 total_loss:0.005910
2025-05-27 16:07:19,354 - train.progressive_trainer - INFO - [0/62] Epoch 181/400 Stage 2 lr:0.000016 total_loss:0.005767
2025-05-27 16:07:27,606 - train.progressive_trainer - INFO - [50/62] Epoch 181/400 Stage 2 lr:0.000016 total_loss:0.005794
2025-05-27 16:07:29,481 - train.progressive_trainer - INFO - [61/62] Epoch 181/400 Stage 2 lr:0.000016 total_loss:0.005505
2025-05-27 16:07:31,165 - train.progressive_trainer - INFO - [0/62] Epoch 182/400 Stage 2 lr:0.000016 total_loss:0.005688
2025-05-27 16:07:40,023 - train.progressive_trainer - INFO - [50/62] Epoch 182/400 Stage 2 lr:0.000016 total_loss:0.006537
2025-05-27 16:07:41,819 - train.progressive_trainer - INFO - [61/62] Epoch 182/400 Stage 2 lr:0.000016 total_loss:0.005487
2025-05-27 16:07:42,661 - train.progressive_trainer - INFO - [0/62] Epoch 183/400 Stage 2 lr:0.000016 total_loss:0.006832
2025-05-27 16:07:50,901 - train.progressive_trainer - INFO - [50/62] Epoch 183/400 Stage 2 lr:0.000016 total_loss:0.005969
2025-05-27 16:07:52,522 - train.progressive_trainer - INFO - [61/62] Epoch 183/400 Stage 2 lr:0.000016 total_loss:0.007433
2025-05-27 16:07:53,425 - train.progressive_trainer - INFO - [0/62] Epoch 184/400 Stage 2 lr:0.000016 total_loss:0.006044
2025-05-27 16:08:02,116 - train.progressive_trainer - INFO - [50/62] Epoch 184/400 Stage 2 lr:0.000016 total_loss:0.005768
2025-05-27 16:08:03,807 - train.progressive_trainer - INFO - [61/62] Epoch 184/400 Stage 2 lr:0.000016 total_loss:0.005382
2025-05-27 16:08:05,260 - train.progressive_trainer - INFO - [0/62] Epoch 185/400 Stage 2 lr:0.000016 total_loss:0.006899
2025-05-27 16:08:13,184 - train.progressive_trainer - INFO - [50/62] Epoch 185/400 Stage 2 lr:0.000016 total_loss:0.005639
2025-05-27 16:08:15,023 - train.progressive_trainer - INFO - [61/62] Epoch 185/400 Stage 2 lr:0.000016 total_loss:0.005631
2025-05-27 16:08:16,258 - train.progressive_trainer - INFO - [0/62] Epoch 186/400 Stage 2 lr:0.000016 total_loss:0.006225
2025-05-27 16:08:24,516 - train.progressive_trainer - INFO - [50/62] Epoch 186/400 Stage 2 lr:0.000016 total_loss:0.005975
2025-05-27 16:08:26,320 - train.progressive_trainer - INFO - [61/62] Epoch 186/400 Stage 2 lr:0.000016 total_loss:0.005406
2025-05-27 16:08:27,262 - train.progressive_trainer - INFO - [0/62] Epoch 187/400 Stage 2 lr:0.000016 total_loss:0.005744
2025-05-27 16:08:35,611 - train.progressive_trainer - INFO - [50/62] Epoch 187/400 Stage 2 lr:0.000016 total_loss:0.005697
2025-05-27 16:08:37,211 - train.progressive_trainer - INFO - [61/62] Epoch 187/400 Stage 2 lr:0.000016 total_loss:0.005234
2025-05-27 16:08:38,161 - train.progressive_trainer - INFO - [0/62] Epoch 188/400 Stage 2 lr:0.000016 total_loss:0.005333
2025-05-27 16:08:46,321 - train.progressive_trainer - INFO - [50/62] Epoch 188/400 Stage 2 lr:0.000016 total_loss:0.006963
2025-05-27 16:08:48,224 - train.progressive_trainer - INFO - [61/62] Epoch 188/400 Stage 2 lr:0.000016 total_loss:0.005444
2025-05-27 16:08:49,656 - train.progressive_trainer - INFO - [0/62] Epoch 189/400 Stage 2 lr:0.000016 total_loss:0.005425
2025-05-27 16:08:58,311 - train.progressive_trainer - INFO - [50/62] Epoch 189/400 Stage 2 lr:0.000016 total_loss:0.005202
2025-05-27 16:09:00,021 - train.progressive_trainer - INFO - [61/62] Epoch 189/400 Stage 2 lr:0.000016 total_loss:0.005219
2025-05-27 16:09:01,559 - train.progressive_trainer - INFO - [0/62] Epoch 190/400 Stage 2 lr:0.000016 total_loss:0.005387
2025-05-27 16:09:09,204 - train.progressive_trainer - INFO - [50/62] Epoch 190/400 Stage 2 lr:0.000016 total_loss:0.005841
2025-05-27 16:09:10,815 - train.progressive_trainer - INFO - [61/62] Epoch 190/400 Stage 2 lr:0.000016 total_loss:0.005336
2025-05-27 16:09:11,545 - train.progressive_trainer - INFO - [0/62] Epoch 191/400 Stage 2 lr:0.000016 total_loss:0.006567
2025-05-27 16:09:19,128 - train.progressive_trainer - INFO - [50/62] Epoch 191/400 Stage 2 lr:0.000016 total_loss:0.005391
2025-05-27 16:09:20,736 - train.progressive_trainer - INFO - [61/62] Epoch 191/400 Stage 2 lr:0.000016 total_loss:0.007157
2025-05-27 16:09:21,523 - train.progressive_trainer - INFO - [0/62] Epoch 192/400 Stage 2 lr:0.000016 total_loss:0.005412
2025-05-27 16:09:30,023 - train.progressive_trainer - INFO - [50/62] Epoch 192/400 Stage 2 lr:0.000016 total_loss:0.005340
2025-05-27 16:09:32,010 - train.progressive_trainer - INFO - [61/62] Epoch 192/400 Stage 2 lr:0.000016 total_loss:0.006401
2025-05-27 16:09:32,679 - train.progressive_trainer - INFO - [0/62] Epoch 193/400 Stage 2 lr:0.000016 total_loss:0.006266
2025-05-27 16:09:43,910 - train.progressive_trainer - INFO - [50/62] Epoch 193/400 Stage 2 lr:0.000016 total_loss:0.005412
2025-05-27 16:09:45,823 - train.progressive_trainer - INFO - [61/62] Epoch 193/400 Stage 2 lr:0.000016 total_loss:0.006357
2025-05-27 16:09:46,711 - train.progressive_trainer - INFO - [0/62] Epoch 194/400 Stage 2 lr:0.000016 total_loss:0.005185
2025-05-27 16:09:54,832 - train.progressive_trainer - INFO - [50/62] Epoch 194/400 Stage 2 lr:0.000016 total_loss:0.005455
2025-05-27 16:09:56,526 - train.progressive_trainer - INFO - [61/62] Epoch 194/400 Stage 2 lr:0.000016 total_loss:0.005471
2025-05-27 16:09:57,317 - train.progressive_trainer - INFO - [0/62] Epoch 195/400 Stage 2 lr:0.000016 total_loss:0.005238
2025-05-27 16:10:04,723 - train.progressive_trainer - INFO - [50/62] Epoch 195/400 Stage 2 lr:0.000016 total_loss:0.005376
2025-05-27 16:10:06,327 - train.progressive_trainer - INFO - [61/62] Epoch 195/400 Stage 2 lr:0.000016 total_loss:0.005028
2025-05-27 16:10:07,158 - train.progressive_trainer - INFO - [0/62] Epoch 196/400 Stage 2 lr:0.000016 total_loss:0.006723
2025-05-27 16:10:15,924 - train.progressive_trainer - INFO - [50/62] Epoch 196/400 Stage 2 lr:0.000016 total_loss:0.005329
2025-05-27 16:10:17,470 - train.progressive_trainer - INFO - [61/62] Epoch 196/400 Stage 2 lr:0.000016 total_loss:0.009598
2025-05-27 16:10:19,247 - train.progressive_trainer - INFO - [0/62] Epoch 197/400 Stage 2 lr:0.000016 total_loss:0.005102
2025-05-27 16:10:28,010 - train.progressive_trainer - INFO - [50/62] Epoch 197/400 Stage 2 lr:0.000016 total_loss:0.005038
2025-05-27 16:10:29,917 - train.progressive_trainer - INFO - [61/62] Epoch 197/400 Stage 2 lr:0.000016 total_loss:0.006173
2025-05-27 16:10:31,360 - train.progressive_trainer - INFO - [0/62] Epoch 198/400 Stage 2 lr:0.000016 total_loss:0.005723
2025-05-27 16:10:43,395 - train.progressive_trainer - INFO - [50/62] Epoch 198/400 Stage 2 lr:0.000016 total_loss:0.004761
2025-05-27 16:10:45,105 - train.progressive_trainer - INFO - [61/62] Epoch 198/400 Stage 2 lr:0.000016 total_loss:0.005100
2025-05-27 16:10:46,257 - train.progressive_trainer - INFO - [0/62] Epoch 199/400 Stage 2 lr:0.000016 total_loss:0.005325
2025-05-27 16:10:54,116 - train.progressive_trainer - INFO - [50/62] Epoch 199/400 Stage 2 lr:0.000016 total_loss:0.005222
2025-05-27 16:10:56,025 - train.progressive_trainer - INFO - [61/62] Epoch 199/400 Stage 2 lr:0.000016 total_loss:0.005732
2025-05-27 16:10:57,054 - train.progressive_trainer - INFO - [0/62] Epoch 200/400 Stage 2 lr:0.000016 total_loss:0.005442
2025-05-27 16:11:04,524 - train.progressive_trainer - INFO - [50/62] Epoch 200/400 Stage 2 lr:0.000016 total_loss:0.005533
2025-05-27 16:11:06,515 - train.progressive_trainer - INFO - [61/62] Epoch 200/400 Stage 2 lr:0.000016 total_loss:0.005691
2025-05-27 16:11:06,637 - train.trainer - INFO - Running scheduled evaluation at epoch 200 (eval_every=50)...
2025-05-27 16:11:06,638 - train.trainer - INFO - Evaluating model at epoch 200...
2025-05-27 16:11:06,643 - train.trainer - INFO - Evaluation logs will be saved to outputs_a100_fixed/logs/eval/eval_epoch_200.log
2025-05-27 16:11:06,645 - train.trainer - INFO - Generating 995 samples for evaluation...
2025-05-27 16:11:06,645 - train.trainer - INFO - Using PulsarAdaptiveSampler for evaluation
2025-05-27 16:11:06,647 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:06,648 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0004, std=0.9952
2025-05-27 16:11:06,766 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2511, std=0.1386, range=[-0.7062, 0.3955]
2025-05-27 16:11:08,198 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:08,198 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0016, std=0.9978
2025-05-27 16:11:08,301 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2675, std=0.1707, range=[-0.7156, 0.8532]
2025-05-27 16:11:08,302 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:08,302 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0045, std=0.9977
2025-05-27 16:11:08,521 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2604, std=0.1695, range=[-0.8238, 0.5691]
2025-05-27 16:11:08,522 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:08,522 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0041, std=0.9960
2025-05-27 16:11:08,746 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2728, std=0.1335, range=[-0.7417, 0.2758]
2025-05-27 16:11:08,747 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:08,747 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0028, std=1.0002
2025-05-27 16:11:08,958 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2607, std=0.1741, range=[-0.7355, 0.6767]
2025-05-27 16:11:08,959 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:08,959 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0014, std=0.9961
2025-05-27 16:11:09,158 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2378, std=0.1837, range=[-0.6660, 0.6029]
2025-05-27 16:11:09,159 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:09,159 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0015, std=0.9972
2025-05-27 16:11:09,356 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2344, std=0.1702, range=[-0.7653, 0.7297]
2025-05-27 16:11:09,379 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:09,379 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0035, std=0.9978
2025-05-27 16:11:09,664 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2565, std=0.1645, range=[-0.6957, 0.4967]
2025-05-27 16:11:09,666 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:09,666 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0018, std=0.9995
2025-05-27 16:11:09,849 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2627, std=0.1667, range=[-0.6945, 0.5009]
2025-05-27 16:11:09,850 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:09,850 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0005, std=0.9954
2025-05-27 16:11:10,057 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2449, std=0.1730, range=[-0.6872, 0.8062]
2025-05-27 16:11:10,059 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:10,059 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0056, std=0.9967
2025-05-27 16:11:10,257 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2307, std=0.1515, range=[-0.6033, 0.4621]
2025-05-27 16:11:10,258 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:10,258 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0033, std=1.0001
2025-05-27 16:11:10,462 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2882, std=0.1727, range=[-0.8078, 0.3280]
2025-05-27 16:11:10,463 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:10,464 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0021, std=1.0001
2025-05-27 16:11:10,653 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2542, std=0.1804, range=[-0.7800, 0.6155]
2025-05-27 16:11:10,654 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:10,654 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0046, std=0.9979
2025-05-27 16:11:10,871 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2619, std=0.1482, range=[-0.7203, 0.4433]
2025-05-27 16:11:10,872 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:10,873 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0001, std=0.9972
2025-05-27 16:11:11,062 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2493, std=0.1718, range=[-0.7386, 0.7967]
2025-05-27 16:11:11,063 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:11,063 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0044, std=0.9967
2025-05-27 16:11:11,270 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2257, std=0.1658, range=[-0.7151, 0.6204]
2025-05-27 16:11:11,271 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:11,271 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0023, std=0.9980
2025-05-27 16:11:11,464 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2492, std=0.1558, range=[-0.6798, 0.3380]
2025-05-27 16:11:11,465 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:11,465 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0014, std=0.9961
2025-05-27 16:11:11,665 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2269, std=0.1648, range=[-0.6231, 0.6731]
2025-05-27 16:11:11,666 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:11:11,666 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0004, std=0.9983
2025-05-27 16:11:11,872 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2342, std=0.1909, range=[-0.8076, 0.7433]
2025-05-27 16:11:11,874 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=45, steps=20
2025-05-27 16:11:11,874 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0012, std=0.9987
2025-05-27 16:11:12,064 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2484, std=0.1441, range=[-0.5883, 0.5464]
2025-05-27 16:11:12,581 - train.trainer - INFO - Generated 995 images with shape torch.Size([995, 3, 32, 32])
2025-05-27 16:11:12,784 - train.trainer - INFO - Saved 100 samples to outputs_a100_fixed/checkpoints/eval_epoch_200/generated_samples.pt
2025-05-27 16:11:12,784 - train.trainer - INFO - Loading real images for evaluation...
2025-05-27 16:11:15,036 - train.trainer - INFO - Loaded 995 real images with shape torch.Size([995, 3, 32, 32])
2025-05-27 16:11:15,037 - train.trainer - INFO - Calculating overall FID and IS metrics...
2025-05-27 16:12:36,777 - train.trainer - INFO - Overall evaluation completed: FID=267.5613, IS=1.3015±0.0356
2025-05-27 16:12:36,778 - train.trainer - INFO - Calculating channel-specific metrics...
2025-05-27 16:16:32,469 - train.trainer - INFO - Channel-specific evaluation completed
2025-05-27 16:16:32,469 - train.trainer - INFO - ==================================================
2025-05-27 16:16:32,469 - train.trainer - INFO - EVALUATION RESULTS (Epoch 200):
2025-05-27 16:16:32,469 - train.trainer - INFO - ==================================================
2025-05-27 16:16:32,469 - train.trainer - INFO - Overall FID: 267.5613
2025-05-27 16:16:32,469 - train.trainer - INFO - Overall IS: 1.3015 ± 0.0356
2025-05-27 16:16:32,470 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:16:32,470 - train.trainer - INFO - Period-DM surface:
2025-05-27 16:16:32,470 - train.trainer - INFO -   FID = 343.7116
2025-05-27 16:16:32,470 - train.trainer - INFO -   IS  = 1.4827 ± 0.0508
2025-05-27 16:16:32,470 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:16:32,470 - train.trainer - INFO - Phase-Subband surface:
2025-05-27 16:16:32,470 - train.trainer - INFO -   FID = 340.9023
2025-05-27 16:16:32,470 - train.trainer - INFO -   IS  = 1.2304 ± 0.0146
2025-05-27 16:16:32,470 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:16:32,470 - train.trainer - INFO - Phase-Subintegration surface:
2025-05-27 16:16:32,470 - train.trainer - INFO -   FID = 387.5698
2025-05-27 16:16:32,470 - train.trainer - INFO -   IS  = 1.0543 ± 0.0033
2025-05-27 16:16:32,470 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:16:32,470 - train.trainer - INFO - Evaluation completed in 325.83 seconds
2025-05-27 16:16:32,472 - train.trainer - INFO - Saved evaluation results to outputs_a100_fixed/logs/eval/results_epoch_200.json
2025-05-27 16:16:32,472 - train.trainer - INFO - Removed evaluation log handler
2025-05-27 16:16:32,473 - train.trainer - INFO - Evaluation metrics at epoch 200:
2025-05-27 16:16:32,474 - train.trainer - INFO -   FID: 267.56132015590214
2025-05-27 16:16:32,474 - train.trainer - INFO -   IS: 1.3015013933181763 ± 0.03558757156133652
2025-05-27 16:16:32,474 - train.trainer - INFO - Early stopping: New best FID score 267.5613 at epoch 200 (improvement: 1.5999)
2025-05-27 16:16:32,517 - train.progressive_trainer - INFO - 检查点已保存: outputs_a100_fixed/checkpoints/checkpoint_epoch_200.pt
2025-05-27 16:16:33,212 - train.progressive_trainer - INFO - [0/62] Epoch 201/400 Stage 2 lr:0.000016 total_loss:0.007495
2025-05-27 16:16:40,550 - train.progressive_trainer - INFO - [50/62] Epoch 201/400 Stage 2 lr:0.000016 total_loss:0.005096
2025-05-27 16:16:42,325 - train.progressive_trainer - INFO - [61/62] Epoch 201/400 Stage 2 lr:0.000016 total_loss:0.013489
2025-05-27 16:16:42,976 - train.progressive_trainer - INFO - [0/62] Epoch 202/400 Stage 2 lr:0.000016 total_loss:0.005539
2025-05-27 16:16:51,027 - train.progressive_trainer - INFO - [50/62] Epoch 202/400 Stage 2 lr:0.000016 total_loss:0.005269
2025-05-27 16:16:52,925 - train.progressive_trainer - INFO - [61/62] Epoch 202/400 Stage 2 lr:0.000016 total_loss:0.004895
2025-05-27 16:16:53,958 - train.progressive_trainer - INFO - [0/62] Epoch 203/400 Stage 2 lr:0.000016 total_loss:0.004943
2025-05-27 16:17:02,819 - train.progressive_trainer - INFO - [50/62] Epoch 203/400 Stage 2 lr:0.000016 total_loss:0.006119
2025-05-27 16:17:04,731 - train.progressive_trainer - INFO - [61/62] Epoch 203/400 Stage 2 lr:0.000016 total_loss:0.005634
2025-05-27 16:17:06,458 - train.progressive_trainer - INFO - [0/62] Epoch 204/400 Stage 2 lr:0.000016 total_loss:0.005204
2025-05-27 16:17:15,016 - train.progressive_trainer - INFO - [50/62] Epoch 204/400 Stage 2 lr:0.000016 total_loss:0.005048
2025-05-27 16:17:16,926 - train.progressive_trainer - INFO - [61/62] Epoch 204/400 Stage 2 lr:0.000016 total_loss:0.004965
2025-05-27 16:17:18,358 - train.progressive_trainer - INFO - [0/62] Epoch 205/400 Stage 2 lr:0.000016 total_loss:0.005954
2025-05-27 16:17:26,518 - train.progressive_trainer - INFO - [50/62] Epoch 205/400 Stage 2 lr:0.000016 total_loss:0.004884
2025-05-27 16:17:28,269 - train.progressive_trainer - INFO - [61/62] Epoch 205/400 Stage 2 lr:0.000016 total_loss:0.005076
2025-05-27 16:17:29,355 - train.progressive_trainer - INFO - [0/62] Epoch 206/400 Stage 2 lr:0.000016 total_loss:0.004944
2025-05-27 16:17:37,109 - train.progressive_trainer - INFO - [50/62] Epoch 206/400 Stage 2 lr:0.000016 total_loss:0.004527
2025-05-27 16:17:39,110 - train.progressive_trainer - INFO - [61/62] Epoch 206/400 Stage 2 lr:0.000016 total_loss:0.004886
2025-05-27 16:17:39,861 - train.progressive_trainer - INFO - [0/62] Epoch 207/400 Stage 2 lr:0.000016 total_loss:0.012253
2025-05-27 16:17:47,923 - train.progressive_trainer - INFO - [50/62] Epoch 207/400 Stage 2 lr:0.000016 total_loss:0.006188
2025-05-27 16:17:49,725 - train.progressive_trainer - INFO - [61/62] Epoch 207/400 Stage 2 lr:0.000016 total_loss:0.004763
2025-05-27 16:17:50,554 - train.progressive_trainer - INFO - [0/62] Epoch 208/400 Stage 2 lr:0.000016 total_loss:0.007588
2025-05-27 16:17:58,922 - train.progressive_trainer - INFO - [50/62] Epoch 208/400 Stage 2 lr:0.000016 total_loss:0.005901
2025-05-27 16:18:01,025 - train.progressive_trainer - INFO - [61/62] Epoch 208/400 Stage 2 lr:0.000016 total_loss:0.004760
2025-05-27 16:18:01,684 - train.progressive_trainer - INFO - [0/62] Epoch 209/400 Stage 2 lr:0.000016 total_loss:0.004848
2025-05-27 16:18:10,099 - train.progressive_trainer - INFO - [50/62] Epoch 209/400 Stage 2 lr:0.000016 total_loss:0.004812
2025-05-27 16:18:11,767 - train.progressive_trainer - INFO - [61/62] Epoch 209/400 Stage 2 lr:0.000016 total_loss:0.006196
2025-05-27 16:18:12,551 - train.progressive_trainer - INFO - [0/62] Epoch 210/400 Stage 2 lr:0.000016 total_loss:0.005065
2025-05-27 16:18:20,314 - train.progressive_trainer - INFO - [50/62] Epoch 210/400 Stage 2 lr:0.000016 total_loss:0.005066
2025-05-27 16:18:22,306 - train.progressive_trainer - INFO - [61/62] Epoch 210/400 Stage 2 lr:0.000016 total_loss:0.006125
2025-05-27 16:18:23,064 - train.progressive_trainer - INFO - [0/62] Epoch 211/400 Stage 2 lr:0.000016 total_loss:0.004726
2025-05-27 16:18:30,726 - train.progressive_trainer - INFO - [50/62] Epoch 211/400 Stage 2 lr:0.000016 total_loss:0.004608
2025-05-27 16:18:32,222 - train.progressive_trainer - INFO - [61/62] Epoch 211/400 Stage 2 lr:0.000016 total_loss:0.004596
2025-05-27 16:18:32,803 - train.progressive_trainer - INFO - [0/62] Epoch 212/400 Stage 2 lr:0.000016 total_loss:0.004824
2025-05-27 16:18:42,728 - train.progressive_trainer - INFO - [50/62] Epoch 212/400 Stage 2 lr:0.000016 total_loss:0.004782
2025-05-27 16:18:44,417 - train.progressive_trainer - INFO - [61/62] Epoch 212/400 Stage 2 lr:0.000016 total_loss:0.004860
2025-05-27 16:18:45,957 - train.progressive_trainer - INFO - [0/62] Epoch 213/400 Stage 2 lr:0.000016 total_loss:0.004937
2025-05-27 16:18:54,624 - train.progressive_trainer - INFO - [50/62] Epoch 213/400 Stage 2 lr:0.000016 total_loss:0.005281
2025-05-27 16:18:56,086 - train.progressive_trainer - INFO - [61/62] Epoch 213/400 Stage 2 lr:0.000016 total_loss:0.004971
2025-05-27 16:18:56,810 - train.progressive_trainer - INFO - [0/62] Epoch 214/400 Stage 2 lr:0.000016 total_loss:0.005611
2025-05-27 16:19:05,828 - train.progressive_trainer - INFO - [50/62] Epoch 214/400 Stage 2 lr:0.000016 total_loss:0.005693
2025-05-27 16:19:07,480 - train.progressive_trainer - INFO - [61/62] Epoch 214/400 Stage 2 lr:0.000016 total_loss:0.005854
2025-05-27 16:19:08,460 - train.progressive_trainer - INFO - [0/62] Epoch 215/400 Stage 2 lr:0.000016 total_loss:0.005205
2025-05-27 16:19:16,613 - train.progressive_trainer - INFO - [50/62] Epoch 215/400 Stage 2 lr:0.000016 total_loss:0.004744
2025-05-27 16:19:18,213 - train.progressive_trainer - INFO - [61/62] Epoch 215/400 Stage 2 lr:0.000016 total_loss:0.004742
2025-05-27 16:19:19,859 - train.progressive_trainer - INFO - [0/62] Epoch 216/400 Stage 2 lr:0.000016 total_loss:0.004837
2025-05-27 16:19:27,411 - train.progressive_trainer - INFO - [50/62] Epoch 216/400 Stage 2 lr:0.000016 total_loss:0.004571
2025-05-27 16:19:29,297 - train.progressive_trainer - INFO - [61/62] Epoch 216/400 Stage 2 lr:0.000016 total_loss:0.020236
2025-05-27 16:19:30,651 - train.progressive_trainer - INFO - [0/62] Epoch 217/400 Stage 2 lr:0.000016 total_loss:0.004735
2025-05-27 16:19:42,025 - train.progressive_trainer - INFO - [50/62] Epoch 217/400 Stage 2 lr:0.000016 total_loss:0.004703
2025-05-27 16:19:43,767 - train.progressive_trainer - INFO - [61/62] Epoch 217/400 Stage 2 lr:0.000016 total_loss:0.004362
2025-05-27 16:19:45,459 - train.progressive_trainer - INFO - [0/62] Epoch 218/400 Stage 2 lr:0.000016 total_loss:0.006001
2025-05-27 16:19:53,986 - train.progressive_trainer - INFO - [50/62] Epoch 218/400 Stage 2 lr:0.000016 total_loss:0.004449
2025-05-27 16:19:55,524 - train.progressive_trainer - INFO - [61/62] Epoch 218/400 Stage 2 lr:0.000016 total_loss:0.004694
2025-05-27 16:19:56,461 - train.progressive_trainer - INFO - [0/62] Epoch 219/400 Stage 2 lr:0.000016 total_loss:0.013048
2025-05-27 16:20:04,298 - train.progressive_trainer - INFO - [50/62] Epoch 219/400 Stage 2 lr:0.000016 total_loss:0.012411
2025-05-27 16:20:06,017 - train.progressive_trainer - INFO - [61/62] Epoch 219/400 Stage 2 lr:0.000016 total_loss:0.005476
2025-05-27 16:20:06,863 - train.progressive_trainer - INFO - [0/62] Epoch 220/400 Stage 2 lr:0.000016 total_loss:0.004938
2025-05-27 16:20:15,216 - train.progressive_trainer - INFO - [50/62] Epoch 220/400 Stage 2 lr:0.000016 total_loss:0.004367
2025-05-27 16:20:17,183 - train.progressive_trainer - INFO - [61/62] Epoch 220/400 Stage 2 lr:0.000016 total_loss:0.004494
2025-05-27 16:20:18,755 - train.progressive_trainer - INFO - [0/62] Epoch 221/400 Stage 2 lr:0.000016 total_loss:0.007058
2025-05-27 16:20:27,159 - train.progressive_trainer - INFO - [50/62] Epoch 221/400 Stage 2 lr:0.000016 total_loss:0.004321
2025-05-27 16:20:28,733 - train.progressive_trainer - INFO - [61/62] Epoch 221/400 Stage 2 lr:0.000016 total_loss:0.004253
2025-05-27 16:20:30,224 - train.progressive_trainer - INFO - [0/62] Epoch 222/400 Stage 2 lr:0.000016 total_loss:0.004432
2025-05-27 16:20:40,260 - train.progressive_trainer - INFO - [50/62] Epoch 222/400 Stage 2 lr:0.000016 total_loss:0.004559
2025-05-27 16:20:42,123 - train.progressive_trainer - INFO - [61/62] Epoch 222/400 Stage 2 lr:0.000016 total_loss:0.004647
2025-05-27 16:20:42,955 - train.progressive_trainer - INFO - [0/62] Epoch 223/400 Stage 2 lr:0.000016 total_loss:0.004360
2025-05-27 16:20:51,319 - train.progressive_trainer - INFO - [50/62] Epoch 223/400 Stage 2 lr:0.000016 total_loss:0.004447
2025-05-27 16:20:53,258 - train.progressive_trainer - INFO - [61/62] Epoch 223/400 Stage 2 lr:0.000016 total_loss:0.004404
2025-05-27 16:20:54,014 - train.progressive_trainer - INFO - [0/62] Epoch 224/400 Stage 2 lr:0.000016 total_loss:0.005014
2025-05-27 16:21:02,719 - train.progressive_trainer - INFO - [50/62] Epoch 224/400 Stage 2 lr:0.000016 total_loss:0.004235
2025-05-27 16:21:04,613 - train.progressive_trainer - INFO - [61/62] Epoch 224/400 Stage 2 lr:0.000016 total_loss:0.004215
2025-05-27 16:21:05,960 - train.progressive_trainer - INFO - [0/62] Epoch 225/400 Stage 2 lr:0.000016 total_loss:0.004460
2025-05-27 16:21:13,834 - train.progressive_trainer - INFO - [50/62] Epoch 225/400 Stage 2 lr:0.000016 total_loss:0.004599
2025-05-27 16:21:15,683 - train.progressive_trainer - INFO - [61/62] Epoch 225/400 Stage 2 lr:0.000016 total_loss:0.004787
2025-05-27 16:21:16,559 - train.progressive_trainer - INFO - [0/62] Epoch 226/400 Stage 2 lr:0.000016 total_loss:0.004159
2025-05-27 16:21:25,197 - train.progressive_trainer - INFO - [50/62] Epoch 226/400 Stage 2 lr:0.000016 total_loss:0.004583
2025-05-27 16:21:26,727 - train.progressive_trainer - INFO - [61/62] Epoch 226/400 Stage 2 lr:0.000016 total_loss:0.004366
2025-05-27 16:21:27,559 - train.progressive_trainer - INFO - [0/62] Epoch 227/400 Stage 2 lr:0.000016 total_loss:0.005943
2025-05-27 16:21:40,122 - train.progressive_trainer - INFO - [50/62] Epoch 227/400 Stage 2 lr:0.000016 total_loss:0.004384
2025-05-27 16:21:42,023 - train.progressive_trainer - INFO - [61/62] Epoch 227/400 Stage 2 lr:0.000016 total_loss:0.005129
2025-05-27 16:21:42,859 - train.progressive_trainer - INFO - [0/62] Epoch 228/400 Stage 2 lr:0.000016 total_loss:0.004304
2025-05-27 16:21:51,216 - train.progressive_trainer - INFO - [50/62] Epoch 228/400 Stage 2 lr:0.000016 total_loss:0.004219
2025-05-27 16:21:53,123 - train.progressive_trainer - INFO - [61/62] Epoch 228/400 Stage 2 lr:0.000016 total_loss:0.004438
2025-05-27 16:21:54,063 - train.progressive_trainer - INFO - [0/62] Epoch 229/400 Stage 2 lr:0.000016 total_loss:0.004006
2025-05-27 16:22:01,851 - train.progressive_trainer - INFO - [50/62] Epoch 229/400 Stage 2 lr:0.000016 total_loss:0.004743
2025-05-27 16:22:03,623 - train.progressive_trainer - INFO - [61/62] Epoch 229/400 Stage 2 lr:0.000016 total_loss:0.004554
2025-05-27 16:22:05,156 - train.progressive_trainer - INFO - [0/62] Epoch 230/400 Stage 2 lr:0.000016 total_loss:0.004367
2025-05-27 16:22:13,420 - train.progressive_trainer - INFO - [50/62] Epoch 230/400 Stage 2 lr:0.000016 total_loss:0.005035
2025-05-27 16:22:15,017 - train.progressive_trainer - INFO - [61/62] Epoch 230/400 Stage 2 lr:0.000016 total_loss:0.005286
2025-05-27 16:22:16,260 - train.progressive_trainer - INFO - [0/62] Epoch 231/400 Stage 2 lr:0.000016 total_loss:0.004090
2025-05-27 16:22:24,176 - train.progressive_trainer - INFO - [50/62] Epoch 231/400 Stage 2 lr:0.000016 total_loss:0.004369
2025-05-27 16:22:25,817 - train.progressive_trainer - INFO - [61/62] Epoch 231/400 Stage 2 lr:0.000016 total_loss:0.005639
2025-05-27 16:22:26,761 - train.progressive_trainer - INFO - [0/62] Epoch 232/400 Stage 2 lr:0.000016 total_loss:0.006569
2025-05-27 16:22:35,110 - train.progressive_trainer - INFO - [50/62] Epoch 232/400 Stage 2 lr:0.000016 total_loss:0.004337
2025-05-27 16:22:41,422 - train.progressive_trainer - INFO - [61/62] Epoch 232/400 Stage 2 lr:0.000016 total_loss:0.004073
2025-05-27 16:22:42,255 - train.progressive_trainer - INFO - [0/62] Epoch 233/400 Stage 2 lr:0.000016 total_loss:0.004817
2025-05-27 16:22:50,817 - train.progressive_trainer - INFO - [50/62] Epoch 233/400 Stage 2 lr:0.000016 total_loss:0.004394
2025-05-27 16:22:52,518 - train.progressive_trainer - INFO - [61/62] Epoch 233/400 Stage 2 lr:0.000016 total_loss:0.004444
2025-05-27 16:22:53,559 - train.progressive_trainer - INFO - [0/62] Epoch 234/400 Stage 2 lr:0.000016 total_loss:0.004278
2025-05-27 16:23:00,818 - train.progressive_trainer - INFO - [50/62] Epoch 234/400 Stage 2 lr:0.000016 total_loss:0.005245
2025-05-27 16:23:02,227 - train.progressive_trainer - INFO - [61/62] Epoch 234/400 Stage 2 lr:0.000016 total_loss:0.004575
2025-05-27 16:23:03,158 - train.progressive_trainer - INFO - [0/62] Epoch 235/400 Stage 2 lr:0.000016 total_loss:0.004338
2025-05-27 16:23:11,426 - train.progressive_trainer - INFO - [50/62] Epoch 235/400 Stage 2 lr:0.000016 total_loss:0.005786
2025-05-27 16:23:12,925 - train.progressive_trainer - INFO - [61/62] Epoch 235/400 Stage 2 lr:0.000016 total_loss:0.004161
2025-05-27 16:23:13,754 - train.progressive_trainer - INFO - [0/62] Epoch 236/400 Stage 2 lr:0.000016 total_loss:0.004592
2025-05-27 16:23:22,011 - train.progressive_trainer - INFO - [50/62] Epoch 236/400 Stage 2 lr:0.000016 total_loss:0.006573
2025-05-27 16:23:23,724 - train.progressive_trainer - INFO - [61/62] Epoch 236/400 Stage 2 lr:0.000016 total_loss:0.004252
2025-05-27 16:23:24,661 - train.progressive_trainer - INFO - [0/62] Epoch 237/400 Stage 2 lr:0.000016 total_loss:0.004346
2025-05-27 16:23:32,473 - train.progressive_trainer - INFO - [50/62] Epoch 237/400 Stage 2 lr:0.000016 total_loss:0.004608
2025-05-27 16:23:34,018 - train.progressive_trainer - INFO - [61/62] Epoch 237/400 Stage 2 lr:0.000016 total_loss:0.004521
2025-05-27 16:23:40,658 - train.progressive_trainer - INFO - [0/62] Epoch 238/400 Stage 2 lr:0.000016 total_loss:0.004460
2025-05-27 16:23:48,622 - train.progressive_trainer - INFO - [50/62] Epoch 238/400 Stage 2 lr:0.000016 total_loss:0.004175
2025-05-27 16:23:50,725 - train.progressive_trainer - INFO - [61/62] Epoch 238/400 Stage 2 lr:0.000016 total_loss:0.004384
2025-05-27 16:23:51,635 - train.progressive_trainer - INFO - [0/62] Epoch 239/400 Stage 2 lr:0.000016 total_loss:0.003922
2025-05-27 16:23:59,024 - train.progressive_trainer - INFO - [50/62] Epoch 239/400 Stage 2 lr:0.000016 total_loss:0.006690
2025-05-27 16:24:00,824 - train.progressive_trainer - INFO - [61/62] Epoch 239/400 Stage 2 lr:0.000016 total_loss:0.006871
2025-05-27 16:24:01,639 - train.progressive_trainer - INFO - [0/62] Epoch 240/400 Stage 2 lr:0.000016 total_loss:0.004118
2025-05-27 16:24:09,624 - train.progressive_trainer - INFO - [50/62] Epoch 240/400 Stage 2 lr:0.000016 total_loss:0.004760
2025-05-27 16:24:11,365 - train.progressive_trainer - INFO - [61/62] Epoch 240/400 Stage 2 lr:0.000016 total_loss:0.004397
2025-05-27 16:24:12,147 - train.progressive_trainer - INFO - [0/62] Epoch 241/400 Stage 2 lr:0.000016 total_loss:0.004174
2025-05-27 16:24:20,022 - train.progressive_trainer - INFO - [50/62] Epoch 241/400 Stage 2 lr:0.000016 total_loss:0.004033
2025-05-27 16:24:21,714 - train.progressive_trainer - INFO - [61/62] Epoch 241/400 Stage 2 lr:0.000016 total_loss:0.004141
2025-05-27 16:24:22,384 - train.progressive_trainer - INFO - [0/62] Epoch 242/400 Stage 2 lr:0.000016 total_loss:0.005092
2025-05-27 16:24:30,147 - train.progressive_trainer - INFO - [50/62] Epoch 242/400 Stage 2 lr:0.000016 total_loss:0.004468
2025-05-27 16:24:31,513 - train.progressive_trainer - INFO - [61/62] Epoch 242/400 Stage 2 lr:0.000016 total_loss:0.008157
2025-05-27 16:24:32,259 - train.progressive_trainer - INFO - [0/62] Epoch 243/400 Stage 2 lr:0.000016 total_loss:0.006127
2025-05-27 16:24:46,226 - train.progressive_trainer - INFO - [50/62] Epoch 243/400 Stage 2 lr:0.000016 total_loss:0.004577
2025-05-27 16:24:47,915 - train.progressive_trainer - INFO - [61/62] Epoch 243/400 Stage 2 lr:0.000016 total_loss:0.004768
2025-05-27 16:24:49,347 - train.progressive_trainer - INFO - [0/62] Epoch 244/400 Stage 2 lr:0.000016 total_loss:0.006722
2025-05-27 16:24:57,315 - train.progressive_trainer - INFO - [50/62] Epoch 244/400 Stage 2 lr:0.000016 total_loss:0.004184
2025-05-27 16:24:58,827 - train.progressive_trainer - INFO - [61/62] Epoch 244/400 Stage 2 lr:0.000016 total_loss:0.006374
2025-05-27 16:25:00,441 - train.progressive_trainer - INFO - [0/62] Epoch 245/400 Stage 2 lr:0.000016 total_loss:0.004260
2025-05-27 16:25:07,982 - train.progressive_trainer - INFO - [50/62] Epoch 245/400 Stage 2 lr:0.000016 total_loss:0.004067
2025-05-27 16:25:09,411 - train.progressive_trainer - INFO - [61/62] Epoch 245/400 Stage 2 lr:0.000016 total_loss:0.004747
2025-05-27 16:25:10,305 - train.progressive_trainer - INFO - [0/62] Epoch 246/400 Stage 2 lr:0.000016 total_loss:0.004114
2025-05-27 16:25:18,368 - train.progressive_trainer - INFO - [50/62] Epoch 246/400 Stage 2 lr:0.000016 total_loss:0.003865
2025-05-27 16:25:20,307 - train.progressive_trainer - INFO - [61/62] Epoch 246/400 Stage 2 lr:0.000016 total_loss:0.004022
2025-05-27 16:25:21,062 - train.progressive_trainer - INFO - [0/62] Epoch 247/400 Stage 2 lr:0.000016 total_loss:0.003909
2025-05-27 16:25:29,509 - train.progressive_trainer - INFO - [50/62] Epoch 247/400 Stage 2 lr:0.000016 total_loss:0.003963
2025-05-27 16:25:30,814 - train.progressive_trainer - INFO - [61/62] Epoch 247/400 Stage 2 lr:0.000016 total_loss:0.004007
2025-05-27 16:25:31,512 - train.progressive_trainer - INFO - [0/62] Epoch 248/400 Stage 2 lr:0.000016 total_loss:0.004233
2025-05-27 16:25:44,505 - train.progressive_trainer - INFO - [50/62] Epoch 248/400 Stage 2 lr:0.000016 total_loss:0.004333
2025-05-27 16:25:45,679 - train.progressive_trainer - INFO - [61/62] Epoch 248/400 Stage 2 lr:0.000016 total_loss:0.004374
2025-05-27 16:25:46,546 - train.progressive_trainer - INFO - [0/62] Epoch 249/400 Stage 2 lr:0.000016 total_loss:0.010189
2025-05-27 16:25:55,425 - train.progressive_trainer - INFO - [50/62] Epoch 249/400 Stage 2 lr:0.000016 total_loss:0.003760
2025-05-27 16:25:57,074 - train.progressive_trainer - INFO - [61/62] Epoch 249/400 Stage 2 lr:0.000016 total_loss:0.004016
2025-05-27 16:25:57,856 - train.progressive_trainer - INFO - [0/62] Epoch 250/400 Stage 2 lr:0.000016 total_loss:0.004100
2025-05-27 16:26:06,423 - train.progressive_trainer - INFO - [50/62] Epoch 250/400 Stage 2 lr:0.000016 total_loss:0.003929
2025-05-27 16:26:08,516 - train.progressive_trainer - INFO - [61/62] Epoch 250/400 Stage 2 lr:0.000016 total_loss:0.003811
2025-05-27 16:26:08,683 - train.trainer - INFO - Running scheduled evaluation at epoch 250 (eval_every=50)...
2025-05-27 16:26:08,683 - train.trainer - INFO - Evaluating model at epoch 250...
2025-05-27 16:26:08,688 - train.trainer - INFO - Evaluation logs will be saved to outputs_a100_fixed/logs/eval/eval_epoch_250.log
2025-05-27 16:26:08,690 - train.trainer - INFO - Generating 995 samples for evaluation...
2025-05-27 16:26:08,690 - train.trainer - INFO - Using PulsarAdaptiveSampler for evaluation
2025-05-27 16:26:08,692 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:08,692 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0016, std=0.9972
2025-05-27 16:26:08,795 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1771, std=0.2093, range=[-0.7634, 0.8165]
2025-05-27 16:26:10,357 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:10,357 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0072, std=0.9996
2025-05-27 16:26:10,461 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1957, std=0.1935, range=[-0.6726, 0.7848]
2025-05-27 16:26:10,463 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:10,463 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0013, std=0.9946
2025-05-27 16:26:10,656 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1653, std=0.1687, range=[-0.5265, 0.7763]
2025-05-27 16:26:10,657 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:10,658 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0017, std=0.9966
2025-05-27 16:26:10,859 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1985, std=0.1748, range=[-0.7783, 0.7338]
2025-05-27 16:26:10,860 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:10,860 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0033, std=0.9988
2025-05-27 16:26:11,060 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1838, std=0.1927, range=[-0.7609, 0.7335]
2025-05-27 16:26:11,061 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:11,062 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0044, std=0.9975
2025-05-27 16:26:11,257 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1742, std=0.1676, range=[-0.4973, 0.5713]
2025-05-27 16:26:11,258 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:11,258 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0027, std=0.9993
2025-05-27 16:26:11,467 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2114, std=0.1688, range=[-0.7534, 0.4724]
2025-05-27 16:26:11,468 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:11,468 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0009, std=0.9976
2025-05-27 16:26:11,663 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1714, std=0.1701, range=[-0.5005, 0.5916]
2025-05-27 16:26:11,664 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:11,664 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0005, std=0.9963
2025-05-27 16:26:11,867 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1946, std=0.1659, range=[-0.7349, 0.7077]
2025-05-27 16:26:11,868 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:11,869 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0010, std=0.9967
2025-05-27 16:26:12,062 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2092, std=0.1646, range=[-0.8423, 0.4838]
2025-05-27 16:26:12,063 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:12,064 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0039, std=0.9972
2025-05-27 16:26:12,266 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1661, std=0.1719, range=[-0.4983, 0.6351]
2025-05-27 16:26:12,267 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:12,268 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0047, std=0.9983
2025-05-27 16:26:12,507 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1730, std=0.1614, range=[-0.5706, 0.6028]
2025-05-27 16:26:12,508 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:12,508 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0003, std=0.9973
2025-05-27 16:26:12,749 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1967, std=0.1811, range=[-0.6921, 0.7710]
2025-05-27 16:26:12,750 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:12,750 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0008, std=0.9967
2025-05-27 16:26:12,964 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1932, std=0.1337, range=[-0.7847, 0.4461]
2025-05-27 16:26:12,979 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:12,979 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0035, std=0.9978
2025-05-27 16:26:13,211 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2125, std=0.1635, range=[-0.6639, 0.9069]
2025-05-27 16:26:13,212 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:13,213 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0018, std=0.9965
2025-05-27 16:26:13,443 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1779, std=0.1777, range=[-0.5181, 0.7947]
2025-05-27 16:26:13,444 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:13,444 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0016, std=0.9987
2025-05-27 16:26:13,663 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1753, std=0.2025, range=[-0.6072, 0.8616]
2025-05-27 16:26:13,664 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:13,664 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0024, std=0.9984
2025-05-27 16:26:13,865 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1691, std=0.1723, range=[-0.4787, 0.6561]
2025-05-27 16:26:13,866 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:26:13,866 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0021, std=0.9982
2025-05-27 16:26:14,055 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1606, std=0.2205, range=[-0.6415, 0.8553]
2025-05-27 16:26:14,056 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=45, steps=20
2025-05-27 16:26:14,056 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0036, std=0.9958
2025-05-27 16:26:14,259 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2055, std=0.2209, range=[-0.7901, 0.8730]
2025-05-27 16:26:14,583 - train.trainer - INFO - Generated 995 images with shape torch.Size([995, 3, 32, 32])
2025-05-27 16:26:14,784 - train.trainer - INFO - Saved 100 samples to outputs_a100_fixed/checkpoints/eval_epoch_250/generated_samples.pt
2025-05-27 16:26:14,784 - train.trainer - INFO - Loading real images for evaluation...
2025-05-27 16:26:16,288 - train.trainer - INFO - Loaded 995 real images with shape torch.Size([995, 3, 32, 32])
2025-05-27 16:26:16,289 - train.trainer - INFO - Calculating overall FID and IS metrics...
2025-05-27 16:27:44,757 - train.trainer - INFO - Overall evaluation completed: FID=277.9557, IS=1.1859±0.0305
2025-05-27 16:27:44,757 - train.trainer - INFO - Calculating channel-specific metrics...
2025-05-27 16:31:30,251 - train.trainer - INFO - Channel-specific evaluation completed
2025-05-27 16:31:30,251 - train.trainer - INFO - ==================================================
2025-05-27 16:31:30,251 - train.trainer - INFO - EVALUATION RESULTS (Epoch 250):
2025-05-27 16:31:30,251 - train.trainer - INFO - ==================================================
2025-05-27 16:31:30,251 - train.trainer - INFO - Overall FID: 277.9557
2025-05-27 16:31:30,251 - train.trainer - INFO - Overall IS: 1.1859 ± 0.0305
2025-05-27 16:31:30,251 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:31:30,251 - train.trainer - INFO - Period-DM surface:
2025-05-27 16:31:30,251 - train.trainer - INFO -   FID = 337.9731
2025-05-27 16:31:30,251 - train.trainer - INFO -   IS  = 1.2119 ± 0.0719
2025-05-27 16:31:30,251 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:31:30,251 - train.trainer - INFO - Phase-Subband surface:
2025-05-27 16:31:30,251 - train.trainer - INFO -   FID = 370.3937
2025-05-27 16:31:30,251 - train.trainer - INFO -   IS  = 1.1099 ± 0.0181
2025-05-27 16:31:30,251 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:31:30,251 - train.trainer - INFO - Phase-Subintegration surface:
2025-05-27 16:31:30,252 - train.trainer - INFO -   FID = 427.2202
2025-05-27 16:31:30,252 - train.trainer - INFO -   IS  = 1.0149 ± 0.0025
2025-05-27 16:31:30,252 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:31:30,252 - train.trainer - INFO - Evaluation completed in 321.57 seconds
2025-05-27 16:31:30,255 - train.trainer - INFO - Saved evaluation results to outputs_a100_fixed/logs/eval/results_epoch_250.json
2025-05-27 16:31:30,255 - train.trainer - INFO - Removed evaluation log handler
2025-05-27 16:31:30,256 - train.trainer - INFO - Evaluation metrics at epoch 250:
2025-05-27 16:31:30,256 - train.trainer - INFO -   FID: 277.9556501305392
2025-05-27 16:31:30,256 - train.trainer - INFO -   IS: 1.185916543006897 ± 0.030486583709716797
2025-05-27 16:31:30,256 - train.trainer - INFO - Early stopping: No improvement at epoch 250 (current FID: 277.9557, best: 267.5613, counter: 1/3)
2025-05-27 16:31:30,300 - train.progressive_trainer - INFO - 检查点已保存: outputs_a100_fixed/checkpoints/checkpoint_epoch_250.pt
2025-05-27 16:31:31,366 - train.progressive_trainer - INFO - [0/62] Epoch 251/400 Stage 2 lr:0.000016 total_loss:0.003771
2025-05-27 16:31:39,324 - train.progressive_trainer - INFO - [50/62] Epoch 251/400 Stage 2 lr:0.000016 total_loss:0.003899
2025-05-27 16:31:41,217 - train.progressive_trainer - INFO - [61/62] Epoch 251/400 Stage 2 lr:0.000016 total_loss:0.004204
2025-05-27 16:31:41,996 - train.progressive_trainer - INFO - [0/62] Epoch 252/400 Stage 2 lr:0.000016 total_loss:0.003984
2025-05-27 16:31:49,827 - train.progressive_trainer - INFO - [50/62] Epoch 252/400 Stage 2 lr:0.000016 total_loss:0.003806
2025-05-27 16:31:51,671 - train.progressive_trainer - INFO - [61/62] Epoch 252/400 Stage 2 lr:0.000016 total_loss:0.003584
2025-05-27 16:31:52,458 - train.progressive_trainer - INFO - [0/62] Epoch 253/400 Stage 2 lr:0.000016 total_loss:0.003797
2025-05-27 16:32:00,318 - train.progressive_trainer - INFO - [50/62] Epoch 253/400 Stage 2 lr:0.000016 total_loss:0.008035
2025-05-27 16:32:02,121 - train.progressive_trainer - INFO - [61/62] Epoch 253/400 Stage 2 lr:0.000016 total_loss:0.004340
2025-05-27 16:32:02,819 - train.progressive_trainer - INFO - [0/62] Epoch 254/400 Stage 2 lr:0.000016 total_loss:0.003973
2025-05-27 16:32:11,368 - train.progressive_trainer - INFO - [50/62] Epoch 254/400 Stage 2 lr:0.000016 total_loss:0.003924
2025-05-27 16:32:13,029 - train.progressive_trainer - INFO - [61/62] Epoch 254/400 Stage 2 lr:0.000016 total_loss:0.003513
2025-05-27 16:32:14,754 - train.progressive_trainer - INFO - [0/62] Epoch 255/400 Stage 2 lr:0.000016 total_loss:0.004416
2025-05-27 16:32:23,224 - train.progressive_trainer - INFO - [50/62] Epoch 255/400 Stage 2 lr:0.000016 total_loss:0.003881
2025-05-27 16:32:25,139 - train.progressive_trainer - INFO - [61/62] Epoch 255/400 Stage 2 lr:0.000016 total_loss:0.003831
2025-05-27 16:32:25,936 - train.progressive_trainer - INFO - [0/62] Epoch 256/400 Stage 2 lr:0.000016 total_loss:0.003874
2025-05-27 16:32:34,412 - train.progressive_trainer - INFO - [50/62] Epoch 256/400 Stage 2 lr:0.000016 total_loss:0.004023
2025-05-27 16:32:35,916 - train.progressive_trainer - INFO - [61/62] Epoch 256/400 Stage 2 lr:0.000016 total_loss:0.003575
2025-05-27 16:32:36,611 - train.progressive_trainer - INFO - [0/62] Epoch 257/400 Stage 2 lr:0.000016 total_loss:0.003903
2025-05-27 16:32:44,911 - train.progressive_trainer - INFO - [50/62] Epoch 257/400 Stage 2 lr:0.000016 total_loss:0.004489
2025-05-27 16:32:46,624 - train.progressive_trainer - INFO - [61/62] Epoch 257/400 Stage 2 lr:0.000016 total_loss:0.003738
2025-05-27 16:32:47,455 - train.progressive_trainer - INFO - [0/62] Epoch 258/400 Stage 2 lr:0.000016 total_loss:0.004412
2025-05-27 16:32:55,210 - train.progressive_trainer - INFO - [50/62] Epoch 258/400 Stage 2 lr:0.000016 total_loss:0.005199
2025-05-27 16:32:57,022 - train.progressive_trainer - INFO - [61/62] Epoch 258/400 Stage 2 lr:0.000016 total_loss:0.003784
2025-05-27 16:32:57,856 - train.progressive_trainer - INFO - [0/62] Epoch 259/400 Stage 2 lr:0.000016 total_loss:0.003682
2025-05-27 16:33:05,823 - train.progressive_trainer - INFO - [50/62] Epoch 259/400 Stage 2 lr:0.000016 total_loss:0.003745
2025-05-27 16:33:07,527 - train.progressive_trainer - INFO - [61/62] Epoch 259/400 Stage 2 lr:0.000016 total_loss:0.004133
2025-05-27 16:33:08,558 - train.progressive_trainer - INFO - [0/62] Epoch 260/400 Stage 2 lr:0.000016 total_loss:0.011835
2025-05-27 16:33:17,520 - train.progressive_trainer - INFO - [50/62] Epoch 260/400 Stage 2 lr:0.000016 total_loss:0.004048
2025-05-27 16:33:19,220 - train.progressive_trainer - INFO - [61/62] Epoch 260/400 Stage 2 lr:0.000016 total_loss:0.003678
2025-05-27 16:33:20,153 - train.progressive_trainer - INFO - [0/62] Epoch 261/400 Stage 2 lr:0.000016 total_loss:0.003844
2025-05-27 16:33:28,613 - train.progressive_trainer - INFO - [50/62] Epoch 261/400 Stage 2 lr:0.000016 total_loss:0.003942
2025-05-27 16:33:30,302 - train.progressive_trainer - INFO - [61/62] Epoch 261/400 Stage 2 lr:0.000016 total_loss:0.003719
2025-05-27 16:33:31,457 - train.progressive_trainer - INFO - [0/62] Epoch 262/400 Stage 2 lr:0.000016 total_loss:0.003829
2025-05-27 16:33:42,515 - train.progressive_trainer - INFO - [50/62] Epoch 262/400 Stage 2 lr:0.000016 total_loss:0.003771
2025-05-27 16:33:44,406 - train.progressive_trainer - INFO - [61/62] Epoch 262/400 Stage 2 lr:0.000016 total_loss:0.003433
2025-05-27 16:33:45,961 - train.progressive_trainer - INFO - [0/62] Epoch 263/400 Stage 2 lr:0.000016 total_loss:0.003762
2025-05-27 16:33:54,284 - train.progressive_trainer - INFO - [50/62] Epoch 263/400 Stage 2 lr:0.000016 total_loss:0.005189
2025-05-27 16:33:56,316 - train.progressive_trainer - INFO - [61/62] Epoch 263/400 Stage 2 lr:0.000016 total_loss:0.003944
2025-05-27 16:33:57,015 - train.progressive_trainer - INFO - [0/62] Epoch 264/400 Stage 2 lr:0.000016 total_loss:0.003748
2025-05-27 16:34:05,516 - train.progressive_trainer - INFO - [50/62] Epoch 264/400 Stage 2 lr:0.000016 total_loss:0.003718
2025-05-27 16:34:07,521 - train.progressive_trainer - INFO - [61/62] Epoch 264/400 Stage 2 lr:0.000016 total_loss:0.003776
2025-05-27 16:34:08,460 - train.progressive_trainer - INFO - [0/62] Epoch 265/400 Stage 2 lr:0.000016 total_loss:0.003588
2025-05-27 16:34:16,266 - train.progressive_trainer - INFO - [50/62] Epoch 265/400 Stage 2 lr:0.000016 total_loss:0.004518
2025-05-27 16:34:18,221 - train.progressive_trainer - INFO - [61/62] Epoch 265/400 Stage 2 lr:0.000016 total_loss:0.003647
2025-05-27 16:34:19,961 - train.progressive_trainer - INFO - [0/62] Epoch 266/400 Stage 2 lr:0.000016 total_loss:0.004851
2025-05-27 16:34:27,203 - train.progressive_trainer - INFO - [50/62] Epoch 266/400 Stage 2 lr:0.000016 total_loss:0.003587
2025-05-27 16:34:28,768 - train.progressive_trainer - INFO - [61/62] Epoch 266/400 Stage 2 lr:0.000016 total_loss:0.003631
2025-05-27 16:34:30,358 - train.progressive_trainer - INFO - [0/62] Epoch 267/400 Stage 2 lr:0.000016 total_loss:0.003603
2025-05-27 16:34:41,707 - train.progressive_trainer - INFO - [50/62] Epoch 267/400 Stage 2 lr:0.000016 total_loss:0.003991
2025-05-27 16:34:43,611 - train.progressive_trainer - INFO - [61/62] Epoch 267/400 Stage 2 lr:0.000016 total_loss:0.003847
2025-05-27 16:34:45,261 - train.progressive_trainer - INFO - [0/62] Epoch 268/400 Stage 2 lr:0.000016 total_loss:0.004304
2025-05-27 16:34:53,415 - train.progressive_trainer - INFO - [50/62] Epoch 268/400 Stage 2 lr:0.000016 total_loss:0.003447
2025-05-27 16:34:54,874 - train.progressive_trainer - INFO - [61/62] Epoch 268/400 Stage 2 lr:0.000016 total_loss:0.003794
2025-05-27 16:34:55,504 - train.progressive_trainer - INFO - [0/62] Epoch 269/400 Stage 2 lr:0.000016 total_loss:0.003470
2025-05-27 16:35:03,391 - train.progressive_trainer - INFO - [50/62] Epoch 269/400 Stage 2 lr:0.000016 total_loss:0.003677
2025-05-27 16:35:05,227 - train.progressive_trainer - INFO - [61/62] Epoch 269/400 Stage 2 lr:0.000016 total_loss:0.003595
2025-05-27 16:35:06,060 - train.progressive_trainer - INFO - [0/62] Epoch 270/400 Stage 2 lr:0.000016 total_loss:0.003888
2025-05-27 16:35:14,205 - train.progressive_trainer - INFO - [50/62] Epoch 270/400 Stage 2 lr:0.000016 total_loss:0.003668
2025-05-27 16:35:16,055 - train.progressive_trainer - INFO - [61/62] Epoch 270/400 Stage 2 lr:0.000016 total_loss:0.004631
2025-05-27 16:35:16,858 - train.progressive_trainer - INFO - [0/62] Epoch 271/400 Stage 2 lr:0.000016 total_loss:0.003664
2025-05-27 16:35:24,784 - train.progressive_trainer - INFO - [50/62] Epoch 271/400 Stage 2 lr:0.000016 total_loss:0.003341
2025-05-27 16:35:26,211 - train.progressive_trainer - INFO - [61/62] Epoch 271/400 Stage 2 lr:0.000016 total_loss:0.003423
2025-05-27 16:35:27,153 - train.progressive_trainer - INFO - [0/62] Epoch 272/400 Stage 2 lr:0.000016 total_loss:0.003322
2025-05-27 16:35:34,423 - train.progressive_trainer - INFO - [50/62] Epoch 272/400 Stage 2 lr:0.000016 total_loss:0.003893
2025-05-27 16:35:41,520 - train.progressive_trainer - INFO - [61/62] Epoch 272/400 Stage 2 lr:0.000016 total_loss:0.003552
2025-05-27 16:35:42,346 - train.progressive_trainer - INFO - [0/62] Epoch 273/400 Stage 2 lr:0.000016 total_loss:0.004545
2025-05-27 16:35:50,315 - train.progressive_trainer - INFO - [50/62] Epoch 273/400 Stage 2 lr:0.000016 total_loss:0.003882
2025-05-27 16:35:52,324 - train.progressive_trainer - INFO - [61/62] Epoch 273/400 Stage 2 lr:0.000016 total_loss:0.003352
2025-05-27 16:35:53,262 - train.progressive_trainer - INFO - [0/62] Epoch 274/400 Stage 2 lr:0.000016 total_loss:0.003384
2025-05-27 16:36:00,623 - train.progressive_trainer - INFO - [50/62] Epoch 274/400 Stage 2 lr:0.000016 total_loss:0.003954
2025-05-27 16:36:02,413 - train.progressive_trainer - INFO - [61/62] Epoch 274/400 Stage 2 lr:0.000016 total_loss:0.003892
2025-05-27 16:36:03,044 - train.progressive_trainer - INFO - [0/62] Epoch 275/400 Stage 2 lr:0.000016 total_loss:0.004279
2025-05-27 16:36:10,711 - train.progressive_trainer - INFO - [50/62] Epoch 275/400 Stage 2 lr:0.000016 total_loss:0.003700
2025-05-27 16:36:12,425 - train.progressive_trainer - INFO - [61/62] Epoch 275/400 Stage 2 lr:0.000016 total_loss:0.004017
2025-05-27 16:36:13,260 - train.progressive_trainer - INFO - [0/62] Epoch 276/400 Stage 2 lr:0.000016 total_loss:0.003498
2025-05-27 16:36:20,923 - train.progressive_trainer - INFO - [50/62] Epoch 276/400 Stage 2 lr:0.000016 total_loss:0.003400
2025-05-27 16:36:22,008 - train.progressive_trainer - INFO - [61/62] Epoch 276/400 Stage 2 lr:0.000016 total_loss:0.003382
2025-05-27 16:36:22,859 - train.progressive_trainer - INFO - [0/62] Epoch 277/400 Stage 2 lr:0.000016 total_loss:0.003354
2025-05-27 16:36:31,825 - train.progressive_trainer - INFO - [50/62] Epoch 277/400 Stage 2 lr:0.000016 total_loss:0.003657
2025-05-27 16:36:40,213 - train.progressive_trainer - INFO - [61/62] Epoch 277/400 Stage 2 lr:0.000016 total_loss:0.003408
2025-05-27 16:36:40,961 - train.progressive_trainer - INFO - [0/62] Epoch 278/400 Stage 2 lr:0.000016 total_loss:0.003633
2025-05-27 16:36:49,015 - train.progressive_trainer - INFO - [50/62] Epoch 278/400 Stage 2 lr:0.000016 total_loss:0.003304
2025-05-27 16:36:50,824 - train.progressive_trainer - INFO - [61/62] Epoch 278/400 Stage 2 lr:0.000016 total_loss:0.003414
2025-05-27 16:36:51,731 - train.progressive_trainer - INFO - [0/62] Epoch 279/400 Stage 2 lr:0.000016 total_loss:0.003918
2025-05-27 16:37:00,122 - train.progressive_trainer - INFO - [50/62] Epoch 279/400 Stage 2 lr:0.000016 total_loss:0.003333
2025-05-27 16:37:01,711 - train.progressive_trainer - INFO - [61/62] Epoch 279/400 Stage 2 lr:0.000016 total_loss:0.003935
2025-05-27 16:37:02,533 - train.progressive_trainer - INFO - [0/62] Epoch 280/400 Stage 2 lr:0.000016 total_loss:0.006119
2025-05-27 16:37:11,224 - train.progressive_trainer - INFO - [50/62] Epoch 280/400 Stage 2 lr:0.000016 total_loss:0.004279
2025-05-27 16:37:12,975 - train.progressive_trainer - INFO - [61/62] Epoch 280/400 Stage 2 lr:0.000016 total_loss:0.003758
2025-05-27 16:37:14,224 - train.progressive_trainer - INFO - [0/62] Epoch 281/400 Stage 2 lr:0.000016 total_loss:0.003550
2025-05-27 16:37:22,217 - train.progressive_trainer - INFO - [50/62] Epoch 281/400 Stage 2 lr:0.000016 total_loss:0.003416
2025-05-27 16:37:23,311 - train.progressive_trainer - INFO - [61/62] Epoch 281/400 Stage 2 lr:0.000016 total_loss:0.003529
2025-05-27 16:37:24,356 - train.progressive_trainer - INFO - [0/62] Epoch 282/400 Stage 2 lr:0.000016 total_loss:0.003560
2025-05-27 16:37:32,525 - train.progressive_trainer - INFO - [50/62] Epoch 282/400 Stage 2 lr:0.000016 total_loss:0.003182
2025-05-27 16:37:34,410 - train.progressive_trainer - INFO - [61/62] Epoch 282/400 Stage 2 lr:0.000016 total_loss:0.003428
2025-05-27 16:37:35,743 - train.progressive_trainer - INFO - [0/62] Epoch 283/400 Stage 2 lr:0.000016 total_loss:0.003310
2025-05-27 16:37:48,311 - train.progressive_trainer - INFO - [50/62] Epoch 283/400 Stage 2 lr:0.000016 total_loss:0.003725
2025-05-27 16:37:50,016 - train.progressive_trainer - INFO - [61/62] Epoch 283/400 Stage 2 lr:0.000016 total_loss:0.003067
2025-05-27 16:37:50,574 - train.progressive_trainer - INFO - [0/62] Epoch 284/400 Stage 2 lr:0.000016 total_loss:0.004059
2025-05-27 16:37:59,019 - train.progressive_trainer - INFO - [50/62] Epoch 284/400 Stage 2 lr:0.000016 total_loss:0.003581
2025-05-27 16:38:00,528 - train.progressive_trainer - INFO - [61/62] Epoch 284/400 Stage 2 lr:0.000016 total_loss:0.004057
2025-05-27 16:38:01,422 - train.progressive_trainer - INFO - [0/62] Epoch 285/400 Stage 2 lr:0.000016 total_loss:0.003424
2025-05-27 16:38:09,515 - train.progressive_trainer - INFO - [50/62] Epoch 285/400 Stage 2 lr:0.000016 total_loss:0.003765
2025-05-27 16:38:11,425 - train.progressive_trainer - INFO - [61/62] Epoch 285/400 Stage 2 lr:0.000016 total_loss:0.003441
2025-05-27 16:38:12,262 - train.progressive_trainer - INFO - [0/62] Epoch 286/400 Stage 2 lr:0.000016 total_loss:0.003414
2025-05-27 16:38:20,326 - train.progressive_trainer - INFO - [50/62] Epoch 286/400 Stage 2 lr:0.000016 total_loss:0.003588
2025-05-27 16:38:21,716 - train.progressive_trainer - INFO - [61/62] Epoch 286/400 Stage 2 lr:0.000016 total_loss:0.003243
2025-05-27 16:38:22,562 - train.progressive_trainer - INFO - [0/62] Epoch 287/400 Stage 2 lr:0.000016 total_loss:0.003405
2025-05-27 16:38:30,813 - train.progressive_trainer - INFO - [50/62] Epoch 287/400 Stage 2 lr:0.000016 total_loss:0.003900
2025-05-27 16:38:32,710 - train.progressive_trainer - INFO - [61/62] Epoch 287/400 Stage 2 lr:0.000016 total_loss:0.003921
2025-05-27 16:38:34,259 - train.progressive_trainer - INFO - [0/62] Epoch 288/400 Stage 2 lr:0.000016 total_loss:0.003754
2025-05-27 16:38:47,179 - train.progressive_trainer - INFO - [50/62] Epoch 288/400 Stage 2 lr:0.000016 total_loss:0.003159
2025-05-27 16:38:49,165 - train.progressive_trainer - INFO - [61/62] Epoch 288/400 Stage 2 lr:0.000016 total_loss:0.003129
2025-05-27 16:38:50,550 - train.progressive_trainer - INFO - [0/62] Epoch 289/400 Stage 2 lr:0.000016 total_loss:0.005580
2025-05-27 16:38:58,919 - train.progressive_trainer - INFO - [50/62] Epoch 289/400 Stage 2 lr:0.000016 total_loss:0.003828
2025-05-27 16:39:00,329 - train.progressive_trainer - INFO - [61/62] Epoch 289/400 Stage 2 lr:0.000016 total_loss:0.003165
2025-05-27 16:39:01,449 - train.progressive_trainer - INFO - [0/62] Epoch 290/400 Stage 2 lr:0.000016 total_loss:0.007026
2025-05-27 16:39:09,916 - train.progressive_trainer - INFO - [50/62] Epoch 290/400 Stage 2 lr:0.000016 total_loss:0.003105
2025-05-27 16:39:11,466 - train.progressive_trainer - INFO - [61/62] Epoch 290/400 Stage 2 lr:0.000016 total_loss:0.003281
2025-05-27 16:39:12,260 - train.progressive_trainer - INFO - [0/62] Epoch 291/400 Stage 2 lr:0.000016 total_loss:0.003285
2025-05-27 16:39:19,678 - train.progressive_trainer - INFO - [50/62] Epoch 291/400 Stage 2 lr:0.000016 total_loss:0.003116
2025-05-27 16:39:21,074 - train.progressive_trainer - INFO - [61/62] Epoch 291/400 Stage 2 lr:0.000016 total_loss:0.003174
2025-05-27 16:39:21,859 - train.progressive_trainer - INFO - [0/62] Epoch 292/400 Stage 2 lr:0.000016 total_loss:0.003377
2025-05-27 16:39:29,711 - train.progressive_trainer - INFO - [50/62] Epoch 292/400 Stage 2 lr:0.000016 total_loss:0.004852
2025-05-27 16:39:31,522 - train.progressive_trainer - INFO - [61/62] Epoch 292/400 Stage 2 lr:0.000016 total_loss:0.003095
2025-05-27 16:39:32,261 - train.progressive_trainer - INFO - [0/62] Epoch 293/400 Stage 2 lr:0.000016 total_loss:0.004063
2025-05-27 16:39:45,719 - train.progressive_trainer - INFO - [50/62] Epoch 293/400 Stage 2 lr:0.000016 total_loss:0.002960
2025-05-27 16:39:47,618 - train.progressive_trainer - INFO - [61/62] Epoch 293/400 Stage 2 lr:0.000016 total_loss:0.003510
2025-05-27 16:39:49,044 - train.progressive_trainer - INFO - [0/62] Epoch 294/400 Stage 2 lr:0.000016 total_loss:0.002945
2025-05-27 16:39:57,406 - train.progressive_trainer - INFO - [50/62] Epoch 294/400 Stage 2 lr:0.000016 total_loss:0.003524
2025-05-27 16:39:59,418 - train.progressive_trainer - INFO - [61/62] Epoch 294/400 Stage 2 lr:0.000016 total_loss:0.003603
2025-05-27 16:40:01,360 - train.progressive_trainer - INFO - [0/62] Epoch 295/400 Stage 2 lr:0.000016 total_loss:0.003585
2025-05-27 16:40:10,215 - train.progressive_trainer - INFO - [50/62] Epoch 295/400 Stage 2 lr:0.000016 total_loss:0.003042
2025-05-27 16:40:11,923 - train.progressive_trainer - INFO - [61/62] Epoch 295/400 Stage 2 lr:0.000016 total_loss:0.003281
2025-05-27 16:40:12,567 - train.progressive_trainer - INFO - [0/62] Epoch 296/400 Stage 2 lr:0.000016 total_loss:0.003258
2025-05-27 16:40:20,521 - train.progressive_trainer - INFO - [50/62] Epoch 296/400 Stage 2 lr:0.000016 total_loss:0.004311
2025-05-27 16:40:22,307 - train.progressive_trainer - INFO - [61/62] Epoch 296/400 Stage 2 lr:0.000016 total_loss:0.003139
2025-05-27 16:40:23,161 - train.progressive_trainer - INFO - [0/62] Epoch 297/400 Stage 2 lr:0.000016 total_loss:0.003114
2025-05-27 16:40:31,211 - train.progressive_trainer - INFO - [50/62] Epoch 297/400 Stage 2 lr:0.000016 total_loss:0.003093
2025-05-27 16:40:33,125 - train.progressive_trainer - INFO - [61/62] Epoch 297/400 Stage 2 lr:0.000016 total_loss:0.002993
2025-05-27 16:40:34,458 - train.progressive_trainer - INFO - [0/62] Epoch 298/400 Stage 2 lr:0.000016 total_loss:0.003321
2025-05-27 16:40:45,340 - train.progressive_trainer - INFO - [50/62] Epoch 298/400 Stage 2 lr:0.000016 total_loss:0.003254
2025-05-27 16:40:46,726 - train.progressive_trainer - INFO - [61/62] Epoch 298/400 Stage 2 lr:0.000016 total_loss:0.003758
2025-05-27 16:40:47,459 - train.progressive_trainer - INFO - [0/62] Epoch 299/400 Stage 2 lr:0.000016 total_loss:0.002873
2025-05-27 16:40:55,562 - train.progressive_trainer - INFO - [50/62] Epoch 299/400 Stage 2 lr:0.000016 total_loss:0.003673
2025-05-27 16:40:57,124 - train.progressive_trainer - INFO - [61/62] Epoch 299/400 Stage 2 lr:0.000016 total_loss:0.003045
2025-05-27 16:40:57,223 - train.progressive_trainer - INFO - 使用优化损失函数，切换到阶段: fine_tune
2025-05-27 16:40:57,223 - train.progressive_trainer - INFO - ============================================================
2025-05-27 16:40:57,223 - train.progressive_trainer - INFO - 训练阶段切换: 阶段2 → 阶段3
2025-05-27 16:40:57,223 - train.progressive_trainer - INFO - 新阶段: fine_tune
2025-05-27 16:40:57,223 - train.progressive_trainer - INFO - 学习率调整: 0.000072 → 0.000064
2025-05-27 16:40:57,224 - train.progressive_trainer - INFO - 物理损失权重: 优化损失函数激活
2025-05-27 16:40:57,224 - train.progressive_trainer - INFO - ============================================================
2025-05-27 16:40:57,701 - train.progressive_trainer - INFO - [0/62] Epoch 300/400 Stage 3 lr:0.000016 total_loss:0.003056
2025-05-27 16:41:00,288 - train.progressive_trainer - INFO - [50/62] Epoch 300/400 Stage 3 lr:0.000016 total_loss:0.004265
2025-05-27 16:41:00,870 - train.progressive_trainer - INFO - [61/62] Epoch 300/400 Stage 3 lr:0.000016 total_loss:0.003834
2025-05-27 16:41:00,985 - train.trainer - INFO - Running scheduled evaluation at epoch 300 (eval_every=50)...
2025-05-27 16:41:00,985 - train.trainer - INFO - Evaluating model at epoch 300...
2025-05-27 16:41:01,219 - train.trainer - INFO - Evaluation logs will be saved to outputs_a100_fixed/logs/eval/eval_epoch_300.log
2025-05-27 16:41:01,221 - train.trainer - INFO - Generating 995 samples for evaluation...
2025-05-27 16:41:01,221 - train.trainer - INFO - Using PulsarAdaptiveSampler for evaluation
2025-05-27 16:41:01,223 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:01,223 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0020, std=0.9966
2025-05-27 16:41:01,325 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1382, std=0.1824, range=[-0.5481, 0.6100]
2025-05-27 16:41:02,096 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:02,096 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0021, std=0.9976
2025-05-27 16:41:02,197 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1544, std=0.1908, range=[-0.6044, 0.7761]
2025-05-27 16:41:02,202 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:02,202 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0012, std=1.0013
2025-05-27 16:41:02,471 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1507, std=0.1842, range=[-0.7136, 0.7572]
2025-05-27 16:41:02,473 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:02,473 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0025, std=0.9973
2025-05-27 16:41:02,660 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1536, std=0.2193, range=[-0.7512, 0.8632]
2025-05-27 16:41:02,661 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:02,661 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0030, std=0.9994
2025-05-27 16:41:02,859 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1390, std=0.2401, range=[-0.7011, 0.8623]
2025-05-27 16:41:02,860 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:02,860 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0004, std=0.9994
2025-05-27 16:41:03,057 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1535, std=0.1849, range=[-0.6354, 0.8058]
2025-05-27 16:41:03,058 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:03,058 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0006, std=0.9986
2025-05-27 16:41:03,257 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1393, std=0.1841, range=[-0.5807, 0.6382]
2025-05-27 16:41:03,258 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:03,258 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0001, std=0.9972
2025-05-27 16:41:03,470 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1371, std=0.1991, range=[-0.5605, 0.8212]
2025-05-27 16:41:03,471 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:03,471 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0029, std=0.9963
2025-05-27 16:41:03,665 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1298, std=0.2171, range=[-0.5994, 0.8283]
2025-05-27 16:41:03,666 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:03,666 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0023, std=0.9958
2025-05-27 16:41:03,855 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1305, std=0.2210, range=[-0.6839, 0.8821]
2025-05-27 16:41:03,856 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:03,856 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0021, std=0.9980
2025-05-27 16:41:04,060 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1504, std=0.1875, range=[-0.5598, 0.7344]
2025-05-27 16:41:04,079 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:04,079 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0031, std=0.9984
2025-05-27 16:41:04,356 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1419, std=0.2070, range=[-0.6559, 0.8378]
2025-05-27 16:41:04,357 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:04,357 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0015, std=0.9943
2025-05-27 16:41:04,565 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1463, std=0.2148, range=[-0.6882, 0.7190]
2025-05-27 16:41:04,579 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:04,579 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0028, std=0.9998
2025-05-27 16:41:04,839 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1304, std=0.2245, range=[-0.5001, 0.9400]
2025-05-27 16:41:04,840 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:04,840 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0046, std=0.9990
2025-05-27 16:41:05,053 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1358, std=0.1988, range=[-0.6130, 0.6697]
2025-05-27 16:41:05,054 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:05,054 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0019, std=0.9960
2025-05-27 16:41:05,250 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1435, std=0.1645, range=[-0.5215, 0.6727]
2025-05-27 16:41:05,251 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:05,251 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0017, std=0.9964
2025-05-27 16:41:05,456 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1402, std=0.2167, range=[-0.7510, 0.7352]
2025-05-27 16:41:05,457 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:05,457 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0006, std=1.0004
2025-05-27 16:41:05,662 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1566, std=0.1746, range=[-0.6522, 0.6989]
2025-05-27 16:41:05,663 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:41:05,664 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0025, std=0.9968
2025-05-27 16:41:05,848 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1245, std=0.2195, range=[-0.5501, 0.8994]
2025-05-27 16:41:05,849 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=45, steps=20
2025-05-27 16:41:05,849 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0012, std=0.9966
2025-05-27 16:41:06,059 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1511, std=0.2265, range=[-0.7947, 0.8814]
2025-05-27 16:41:06,479 - train.trainer - INFO - Generated 995 images with shape torch.Size([995, 3, 32, 32])
2025-05-27 16:41:06,594 - train.trainer - INFO - Saved 100 samples to outputs_a100_fixed/checkpoints/eval_epoch_300/generated_samples.pt
2025-05-27 16:41:06,594 - train.trainer - INFO - Loading real images for evaluation...
2025-05-27 16:41:08,208 - train.trainer - INFO - Loaded 995 real images with shape torch.Size([995, 3, 32, 32])
2025-05-27 16:41:08,208 - train.trainer - INFO - Calculating overall FID and IS metrics...
2025-05-27 16:42:36,140 - train.trainer - INFO - Overall evaluation completed: FID=354.6842, IS=1.0613±0.0082
2025-05-27 16:42:36,140 - train.trainer - INFO - Calculating channel-specific metrics...
2025-05-27 16:46:25,578 - train.trainer - INFO - Channel-specific evaluation completed
2025-05-27 16:46:25,579 - train.trainer - INFO - ==================================================
2025-05-27 16:46:25,579 - train.trainer - INFO - EVALUATION RESULTS (Epoch 300):
2025-05-27 16:46:25,579 - train.trainer - INFO - ==================================================
2025-05-27 16:46:25,579 - train.trainer - INFO - Overall FID: 354.6842
2025-05-27 16:46:25,579 - train.trainer - INFO - Overall IS: 1.0613 ± 0.0082
2025-05-27 16:46:25,579 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:46:25,579 - train.trainer - INFO - Period-DM surface:
2025-05-27 16:46:25,579 - train.trainer - INFO -   FID = 387.4654
2025-05-27 16:46:25,579 - train.trainer - INFO -   IS  = 1.0245 ± 0.0068
2025-05-27 16:46:25,579 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:46:25,579 - train.trainer - INFO - Phase-Subband surface:
2025-05-27 16:46:25,579 - train.trainer - INFO -   FID = 380.1160
2025-05-27 16:46:25,579 - train.trainer - INFO -   IS  = 1.0940 ± 0.0108
2025-05-27 16:46:25,579 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:46:25,579 - train.trainer - INFO - Phase-Subintegration surface:
2025-05-27 16:46:25,581 - train.trainer - INFO -   FID = 441.6567
2025-05-27 16:46:25,581 - train.trainer - INFO -   IS  = 1.0075 ± 0.0008
2025-05-27 16:46:25,581 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:46:25,581 - train.trainer - INFO - Evaluation completed in 324.60 seconds
2025-05-27 16:46:25,584 - train.trainer - INFO - Saved evaluation results to outputs_a100_fixed/logs/eval/results_epoch_300.json
2025-05-27 16:46:25,585 - train.trainer - INFO - Removed evaluation log handler
2025-05-27 16:46:25,586 - train.trainer - INFO - Evaluation metrics at epoch 300:
2025-05-27 16:46:25,586 - train.trainer - INFO -   FID: 354.6842011712119
2025-05-27 16:46:25,586 - train.trainer - INFO -   IS: 1.0612726211547852 ± 0.008237688802182674
2025-05-27 16:46:25,586 - train.trainer - INFO - Early stopping: No improvement at epoch 300 (current FID: 354.6842, best: 267.5613, counter: 2/3)
2025-05-27 16:46:25,629 - train.progressive_trainer - INFO - 检查点已保存: outputs_a100_fixed/checkpoints/checkpoint_epoch_300.pt
2025-05-27 16:46:26,190 - train.progressive_trainer - INFO - [0/62] Epoch 301/400 Stage 3 lr:0.000016 total_loss:0.003123
2025-05-27 16:46:28,770 - train.progressive_trainer - INFO - [50/62] Epoch 301/400 Stage 3 lr:0.000016 total_loss:0.003134
2025-05-27 16:46:29,348 - train.progressive_trainer - INFO - [61/62] Epoch 301/400 Stage 3 lr:0.000016 total_loss:0.003132
2025-05-27 16:46:30,769 - train.progressive_trainer - INFO - [0/62] Epoch 302/400 Stage 3 lr:0.000016 total_loss:0.003469
2025-05-27 16:46:33,385 - train.progressive_trainer - INFO - [50/62] Epoch 302/400 Stage 3 lr:0.000016 total_loss:0.003043
2025-05-27 16:46:33,959 - train.progressive_trainer - INFO - [61/62] Epoch 302/400 Stage 3 lr:0.000016 total_loss:0.003180
2025-05-27 16:46:35,447 - train.progressive_trainer - INFO - [0/62] Epoch 303/400 Stage 3 lr:0.000016 total_loss:0.003049
2025-05-27 16:46:38,054 - train.progressive_trainer - INFO - [50/62] Epoch 303/400 Stage 3 lr:0.000016 total_loss:0.005377
2025-05-27 16:46:38,630 - train.progressive_trainer - INFO - [61/62] Epoch 303/400 Stage 3 lr:0.000016 total_loss:0.003121
2025-05-27 16:46:39,375 - train.progressive_trainer - INFO - [0/62] Epoch 304/400 Stage 3 lr:0.000016 total_loss:0.003397
2025-05-27 16:46:41,983 - train.progressive_trainer - INFO - [50/62] Epoch 304/400 Stage 3 lr:0.000016 total_loss:0.003525
2025-05-27 16:46:42,572 - train.progressive_trainer - INFO - [61/62] Epoch 304/400 Stage 3 lr:0.000016 total_loss:0.003107
2025-05-27 16:46:43,179 - train.progressive_trainer - INFO - [0/62] Epoch 305/400 Stage 3 lr:0.000016 total_loss:0.003074
2025-05-27 16:46:45,790 - train.progressive_trainer - INFO - [50/62] Epoch 305/400 Stage 3 lr:0.000016 total_loss:0.003102
2025-05-27 16:46:46,363 - train.progressive_trainer - INFO - [61/62] Epoch 305/400 Stage 3 lr:0.000016 total_loss:0.003120
2025-05-27 16:46:46,959 - train.progressive_trainer - INFO - [0/62] Epoch 306/400 Stage 3 lr:0.000016 total_loss:0.002962
2025-05-27 16:46:49,569 - train.progressive_trainer - INFO - [50/62] Epoch 306/400 Stage 3 lr:0.000016 total_loss:0.009777
2025-05-27 16:46:50,157 - train.progressive_trainer - INFO - [61/62] Epoch 306/400 Stage 3 lr:0.000016 total_loss:0.003298
2025-05-27 16:46:50,741 - train.progressive_trainer - INFO - [0/62] Epoch 307/400 Stage 3 lr:0.000016 total_loss:0.002740
2025-05-27 16:46:53,349 - train.progressive_trainer - INFO - [50/62] Epoch 307/400 Stage 3 lr:0.000016 total_loss:0.003025
2025-05-27 16:46:53,920 - train.progressive_trainer - INFO - [61/62] Epoch 307/400 Stage 3 lr:0.000016 total_loss:0.004910
2025-05-27 16:46:54,692 - train.progressive_trainer - INFO - [0/62] Epoch 308/400 Stage 3 lr:0.000016 total_loss:0.008777
2025-05-27 16:46:57,257 - train.progressive_trainer - INFO - [50/62] Epoch 308/400 Stage 3 lr:0.000016 total_loss:0.003121
2025-05-27 16:46:57,830 - train.progressive_trainer - INFO - [61/62] Epoch 308/400 Stage 3 lr:0.000016 total_loss:0.003260
2025-05-27 16:46:58,398 - train.progressive_trainer - INFO - [0/62] Epoch 309/400 Stage 3 lr:0.000016 total_loss:0.002875
2025-05-27 16:47:00,998 - train.progressive_trainer - INFO - [50/62] Epoch 309/400 Stage 3 lr:0.000016 total_loss:0.002997
2025-05-27 16:47:01,581 - train.progressive_trainer - INFO - [61/62] Epoch 309/400 Stage 3 lr:0.000016 total_loss:0.003492
2025-05-27 16:47:02,188 - train.progressive_trainer - INFO - [0/62] Epoch 310/400 Stage 3 lr:0.000016 total_loss:0.003103
2025-05-27 16:47:04,768 - train.progressive_trainer - INFO - [50/62] Epoch 310/400 Stage 3 lr:0.000016 total_loss:0.002899
2025-05-27 16:47:05,343 - train.progressive_trainer - INFO - [61/62] Epoch 310/400 Stage 3 lr:0.000016 total_loss:0.004321
2025-05-27 16:47:05,928 - train.progressive_trainer - INFO - [0/62] Epoch 311/400 Stage 3 lr:0.000016 total_loss:0.002999
2025-05-27 16:47:42,364 - train.progressive_trainer - INFO - [50/62] Epoch 311/400 Stage 3 lr:0.000016 total_loss:0.002928
2025-05-27 16:47:42,937 - train.progressive_trainer - INFO - [61/62] Epoch 311/400 Stage 3 lr:0.000016 total_loss:0.003055
2025-05-27 16:47:43,533 - train.progressive_trainer - INFO - [0/62] Epoch 312/400 Stage 3 lr:0.000016 total_loss:0.003374
2025-05-27 16:47:46,137 - train.progressive_trainer - INFO - [50/62] Epoch 312/400 Stage 3 lr:0.000016 total_loss:0.003461
2025-05-27 16:47:46,709 - train.progressive_trainer - INFO - [61/62] Epoch 312/400 Stage 3 lr:0.000016 total_loss:0.002932
2025-05-27 16:47:47,317 - train.progressive_trainer - INFO - [0/62] Epoch 313/400 Stage 3 lr:0.000016 total_loss:0.003116
2025-05-27 16:47:49,923 - train.progressive_trainer - INFO - [50/62] Epoch 313/400 Stage 3 lr:0.000016 total_loss:0.003003
2025-05-27 16:47:50,503 - train.progressive_trainer - INFO - [61/62] Epoch 313/400 Stage 3 lr:0.000016 total_loss:0.002919
2025-05-27 16:47:51,079 - train.progressive_trainer - INFO - [0/62] Epoch 314/400 Stage 3 lr:0.000016 total_loss:0.003198
2025-05-27 16:47:53,690 - train.progressive_trainer - INFO - [50/62] Epoch 314/400 Stage 3 lr:0.000016 total_loss:0.003185
2025-05-27 16:47:54,265 - train.progressive_trainer - INFO - [61/62] Epoch 314/400 Stage 3 lr:0.000016 total_loss:0.003423
2025-05-27 16:47:55,091 - train.progressive_trainer - INFO - [0/62] Epoch 315/400 Stage 3 lr:0.000016 total_loss:0.002687
2025-05-27 16:47:57,704 - train.progressive_trainer - INFO - [50/62] Epoch 315/400 Stage 3 lr:0.000016 total_loss:0.003467
2025-05-27 16:47:58,278 - train.progressive_trainer - INFO - [61/62] Epoch 315/400 Stage 3 lr:0.000016 total_loss:0.003430
2025-05-27 16:47:59,731 - train.progressive_trainer - INFO - [0/62] Epoch 316/400 Stage 3 lr:0.000016 total_loss:0.003013
2025-05-27 16:48:02,349 - train.progressive_trainer - INFO - [50/62] Epoch 316/400 Stage 3 lr:0.000016 total_loss:0.003104
2025-05-27 16:48:02,926 - train.progressive_trainer - INFO - [61/62] Epoch 316/400 Stage 3 lr:0.000016 total_loss:0.003304
2025-05-27 16:48:04,205 - train.progressive_trainer - INFO - [0/62] Epoch 317/400 Stage 3 lr:0.000016 total_loss:0.003151
2025-05-27 16:48:41,642 - train.progressive_trainer - INFO - [50/62] Epoch 317/400 Stage 3 lr:0.000016 total_loss:0.002920
2025-05-27 16:48:42,216 - train.progressive_trainer - INFO - [61/62] Epoch 317/400 Stage 3 lr:0.000016 total_loss:0.006465
2025-05-27 16:48:42,781 - train.progressive_trainer - INFO - [0/62] Epoch 318/400 Stage 3 lr:0.000016 total_loss:0.003408
2025-05-27 16:48:45,396 - train.progressive_trainer - INFO - [50/62] Epoch 318/400 Stage 3 lr:0.000016 total_loss:0.002763
2025-05-27 16:48:45,968 - train.progressive_trainer - INFO - [61/62] Epoch 318/400 Stage 3 lr:0.000016 total_loss:0.002889
2025-05-27 16:48:46,561 - train.progressive_trainer - INFO - [0/62] Epoch 319/400 Stage 3 lr:0.000016 total_loss:0.002677
2025-05-27 16:48:49,178 - train.progressive_trainer - INFO - [50/62] Epoch 319/400 Stage 3 lr:0.000016 total_loss:0.003067
2025-05-27 16:48:49,756 - train.progressive_trainer - INFO - [61/62] Epoch 319/400 Stage 3 lr:0.000016 total_loss:0.002914
2025-05-27 16:48:50,391 - train.progressive_trainer - INFO - [0/62] Epoch 320/400 Stage 3 lr:0.000016 total_loss:0.002674
2025-05-27 16:48:53,019 - train.progressive_trainer - INFO - [50/62] Epoch 320/400 Stage 3 lr:0.000016 total_loss:0.002754
2025-05-27 16:48:53,602 - train.progressive_trainer - INFO - [61/62] Epoch 320/400 Stage 3 lr:0.000016 total_loss:0.002888
2025-05-27 16:48:54,329 - train.progressive_trainer - INFO - [0/62] Epoch 321/400 Stage 3 lr:0.000016 total_loss:0.004295
2025-05-27 16:48:56,942 - train.progressive_trainer - INFO - [50/62] Epoch 321/400 Stage 3 lr:0.000016 total_loss:0.003132
2025-05-27 16:48:57,515 - train.progressive_trainer - INFO - [61/62] Epoch 321/400 Stage 3 lr:0.000016 total_loss:0.002987
2025-05-27 16:48:58,089 - train.progressive_trainer - INFO - [0/62] Epoch 322/400 Stage 3 lr:0.000016 total_loss:0.002839
2025-05-27 16:49:00,705 - train.progressive_trainer - INFO - [50/62] Epoch 322/400 Stage 3 lr:0.000016 total_loss:0.003092
2025-05-27 16:49:01,284 - train.progressive_trainer - INFO - [61/62] Epoch 322/400 Stage 3 lr:0.000016 total_loss:0.002989
2025-05-27 16:49:01,864 - train.progressive_trainer - INFO - [0/62] Epoch 323/400 Stage 3 lr:0.000016 total_loss:0.003069
2025-05-27 16:49:40,791 - train.progressive_trainer - INFO - [50/62] Epoch 323/400 Stage 3 lr:0.000016 total_loss:0.003143
2025-05-27 16:49:41,366 - train.progressive_trainer - INFO - [61/62] Epoch 323/400 Stage 3 lr:0.000016 total_loss:0.002931
2025-05-27 16:49:41,975 - train.progressive_trainer - INFO - [0/62] Epoch 324/400 Stage 3 lr:0.000016 total_loss:0.002551
2025-05-27 16:49:44,575 - train.progressive_trainer - INFO - [50/62] Epoch 324/400 Stage 3 lr:0.000016 total_loss:0.003050
2025-05-27 16:49:45,145 - train.progressive_trainer - INFO - [61/62] Epoch 324/400 Stage 3 lr:0.000016 total_loss:0.002886
2025-05-27 16:49:46,364 - train.progressive_trainer - INFO - [0/62] Epoch 325/400 Stage 3 lr:0.000016 total_loss:0.002828
2025-05-27 16:49:48,956 - train.progressive_trainer - INFO - [50/62] Epoch 325/400 Stage 3 lr:0.000016 total_loss:0.003063
2025-05-27 16:49:49,527 - train.progressive_trainer - INFO - [61/62] Epoch 325/400 Stage 3 lr:0.000016 total_loss:0.003159
2025-05-27 16:49:50,410 - train.progressive_trainer - INFO - [0/62] Epoch 326/400 Stage 3 lr:0.000016 total_loss:0.002997
2025-05-27 16:49:53,015 - train.progressive_trainer - INFO - [50/62] Epoch 326/400 Stage 3 lr:0.000016 total_loss:0.002734
2025-05-27 16:49:53,586 - train.progressive_trainer - INFO - [61/62] Epoch 326/400 Stage 3 lr:0.000016 total_loss:0.002707
2025-05-27 16:49:54,428 - train.progressive_trainer - INFO - [0/62] Epoch 327/400 Stage 3 lr:0.000016 total_loss:0.003002
2025-05-27 16:49:57,028 - train.progressive_trainer - INFO - [50/62] Epoch 327/400 Stage 3 lr:0.000016 total_loss:0.002849
2025-05-27 16:49:57,601 - train.progressive_trainer - INFO - [61/62] Epoch 327/400 Stage 3 lr:0.000016 total_loss:0.002977
2025-05-27 16:49:58,194 - train.progressive_trainer - INFO - [0/62] Epoch 328/400 Stage 3 lr:0.000016 total_loss:0.002834
2025-05-27 16:50:00,795 - train.progressive_trainer - INFO - [50/62] Epoch 328/400 Stage 3 lr:0.000016 total_loss:0.003779
2025-05-27 16:50:01,380 - train.progressive_trainer - INFO - [61/62] Epoch 328/400 Stage 3 lr:0.000016 total_loss:0.002628
2025-05-27 16:50:02,063 - train.progressive_trainer - INFO - [0/62] Epoch 329/400 Stage 3 lr:0.000016 total_loss:0.002614
2025-05-27 16:50:04,679 - train.progressive_trainer - INFO - [50/62] Epoch 329/400 Stage 3 lr:0.000016 total_loss:0.002864
2025-05-27 16:50:40,595 - train.progressive_trainer - INFO - [61/62] Epoch 329/400 Stage 3 lr:0.000016 total_loss:0.002820
2025-05-27 16:50:41,178 - train.progressive_trainer - INFO - [0/62] Epoch 330/400 Stage 3 lr:0.000016 total_loss:0.003166
2025-05-27 16:50:43,819 - train.progressive_trainer - INFO - [50/62] Epoch 330/400 Stage 3 lr:0.000016 total_loss:0.002623
2025-05-27 16:50:44,390 - train.progressive_trainer - INFO - [61/62] Epoch 330/400 Stage 3 lr:0.000016 total_loss:0.003408
2025-05-27 16:50:45,952 - train.progressive_trainer - INFO - [0/62] Epoch 331/400 Stage 3 lr:0.000016 total_loss:0.003519
2025-05-27 16:50:48,567 - train.progressive_trainer - INFO - [50/62] Epoch 331/400 Stage 3 lr:0.000016 total_loss:0.003210
2025-05-27 16:50:49,148 - train.progressive_trainer - INFO - [61/62] Epoch 331/400 Stage 3 lr:0.000016 total_loss:0.002888
2025-05-27 16:50:50,355 - train.progressive_trainer - INFO - [0/62] Epoch 332/400 Stage 3 lr:0.000016 total_loss:0.005563
2025-05-27 16:50:52,970 - train.progressive_trainer - INFO - [50/62] Epoch 332/400 Stage 3 lr:0.000016 total_loss:0.002914
2025-05-27 16:50:53,541 - train.progressive_trainer - INFO - [61/62] Epoch 332/400 Stage 3 lr:0.000016 total_loss:0.003816
2025-05-27 16:50:54,191 - train.progressive_trainer - INFO - [0/62] Epoch 333/400 Stage 3 lr:0.000016 total_loss:0.002866
2025-05-27 16:50:56,792 - train.progressive_trainer - INFO - [50/62] Epoch 333/400 Stage 3 lr:0.000016 total_loss:0.002821
2025-05-27 16:50:57,364 - train.progressive_trainer - INFO - [61/62] Epoch 333/400 Stage 3 lr:0.000016 total_loss:0.005500
2025-05-27 16:50:57,971 - train.progressive_trainer - INFO - [0/62] Epoch 334/400 Stage 3 lr:0.000016 total_loss:0.002663
2025-05-27 16:51:00,551 - train.progressive_trainer - INFO - [50/62] Epoch 334/400 Stage 3 lr:0.000016 total_loss:0.003718
2025-05-27 16:51:01,123 - train.progressive_trainer - INFO - [61/62] Epoch 334/400 Stage 3 lr:0.000016 total_loss:0.002901
2025-05-27 16:51:01,706 - train.progressive_trainer - INFO - [0/62] Epoch 335/400 Stage 3 lr:0.000016 total_loss:0.003349
2025-05-27 16:51:04,321 - train.progressive_trainer - INFO - [50/62] Epoch 335/400 Stage 3 lr:0.000016 total_loss:0.005181
2025-05-27 16:51:04,894 - train.progressive_trainer - INFO - [61/62] Epoch 335/400 Stage 3 lr:0.000016 total_loss:0.002671
2025-05-27 16:51:05,656 - train.progressive_trainer - INFO - [0/62] Epoch 336/400 Stage 3 lr:0.000016 total_loss:0.002741
2025-05-27 16:51:42,511 - train.progressive_trainer - INFO - [50/62] Epoch 336/400 Stage 3 lr:0.000016 total_loss:0.003220
2025-05-27 16:51:43,088 - train.progressive_trainer - INFO - [61/62] Epoch 336/400 Stage 3 lr:0.000016 total_loss:0.002740
2025-05-27 16:51:44,399 - train.progressive_trainer - INFO - [0/62] Epoch 337/400 Stage 3 lr:0.000016 total_loss:0.002489
2025-05-27 16:51:47,008 - train.progressive_trainer - INFO - [50/62] Epoch 337/400 Stage 3 lr:0.000016 total_loss:0.004501
2025-05-27 16:51:47,584 - train.progressive_trainer - INFO - [61/62] Epoch 337/400 Stage 3 lr:0.000016 total_loss:0.002700
2025-05-27 16:51:49,152 - train.progressive_trainer - INFO - [0/62] Epoch 338/400 Stage 3 lr:0.000016 total_loss:0.002653
2025-05-27 16:51:51,766 - train.progressive_trainer - INFO - [50/62] Epoch 338/400 Stage 3 lr:0.000016 total_loss:0.002948
2025-05-27 16:51:52,348 - train.progressive_trainer - INFO - [61/62] Epoch 338/400 Stage 3 lr:0.000016 total_loss:0.002622
2025-05-27 16:51:53,172 - train.progressive_trainer - INFO - [0/62] Epoch 339/400 Stage 3 lr:0.000016 total_loss:0.002463
2025-05-27 16:51:55,426 - train.progressive_trainer - INFO - [50/62] Epoch 339/400 Stage 3 lr:0.000016 total_loss:0.002693
2025-05-27 16:51:55,999 - train.progressive_trainer - INFO - [61/62] Epoch 339/400 Stage 3 lr:0.000016 total_loss:0.005414
2025-05-27 16:51:56,763 - train.progressive_trainer - INFO - [0/62] Epoch 340/400 Stage 3 lr:0.000016 total_loss:0.002596
2025-05-27 16:51:59,369 - train.progressive_trainer - INFO - [50/62] Epoch 340/400 Stage 3 lr:0.000016 total_loss:0.003138
2025-05-27 16:51:59,944 - train.progressive_trainer - INFO - [61/62] Epoch 340/400 Stage 3 lr:0.000016 total_loss:0.002554
2025-05-27 16:52:01,398 - train.progressive_trainer - INFO - [0/62] Epoch 341/400 Stage 3 lr:0.000016 total_loss:0.002988
2025-05-27 16:52:04,012 - train.progressive_trainer - INFO - [50/62] Epoch 341/400 Stage 3 lr:0.000016 total_loss:0.002502
2025-05-27 16:52:04,585 - train.progressive_trainer - INFO - [61/62] Epoch 341/400 Stage 3 lr:0.000016 total_loss:0.002557
2025-05-27 16:52:05,896 - train.progressive_trainer - INFO - [0/62] Epoch 342/400 Stage 3 lr:0.000016 total_loss:0.003009
2025-05-27 16:52:41,491 - train.progressive_trainer - INFO - [50/62] Epoch 342/400 Stage 3 lr:0.000016 total_loss:0.002763
2025-05-27 16:52:42,078 - train.progressive_trainer - INFO - [61/62] Epoch 342/400 Stage 3 lr:0.000016 total_loss:0.002717
2025-05-27 16:52:42,716 - train.progressive_trainer - INFO - [0/62] Epoch 343/400 Stage 3 lr:0.000016 total_loss:0.002728
2025-05-27 16:52:45,315 - train.progressive_trainer - INFO - [50/62] Epoch 343/400 Stage 3 lr:0.000016 total_loss:0.002656
2025-05-27 16:52:45,889 - train.progressive_trainer - INFO - [61/62] Epoch 343/400 Stage 3 lr:0.000016 total_loss:0.002625
2025-05-27 16:52:46,482 - train.progressive_trainer - INFO - [0/62] Epoch 344/400 Stage 3 lr:0.000016 total_loss:0.002571
2025-05-27 16:52:49,084 - train.progressive_trainer - INFO - [50/62] Epoch 344/400 Stage 3 lr:0.000016 total_loss:0.002606
2025-05-27 16:52:49,655 - train.progressive_trainer - INFO - [61/62] Epoch 344/400 Stage 3 lr:0.000016 total_loss:0.003088
2025-05-27 16:52:50,543 - train.progressive_trainer - INFO - [0/62] Epoch 345/400 Stage 3 lr:0.000016 total_loss:0.002797
2025-05-27 16:52:53,142 - train.progressive_trainer - INFO - [50/62] Epoch 345/400 Stage 3 lr:0.000016 total_loss:0.004629
2025-05-27 16:52:53,713 - train.progressive_trainer - INFO - [61/62] Epoch 345/400 Stage 3 lr:0.000016 total_loss:0.002901
2025-05-27 16:52:54,547 - train.progressive_trainer - INFO - [0/62] Epoch 346/400 Stage 3 lr:0.000016 total_loss:0.002579
2025-05-27 16:52:57,153 - train.progressive_trainer - INFO - [50/62] Epoch 346/400 Stage 3 lr:0.000016 total_loss:0.002940
2025-05-27 16:52:57,728 - train.progressive_trainer - INFO - [61/62] Epoch 346/400 Stage 3 lr:0.000016 total_loss:0.002553
2025-05-27 16:52:58,313 - train.progressive_trainer - INFO - [0/62] Epoch 347/400 Stage 3 lr:0.000016 total_loss:0.002572
2025-05-27 16:53:00,939 - train.progressive_trainer - INFO - [50/62] Epoch 347/400 Stage 3 lr:0.000016 total_loss:0.003082
2025-05-27 16:53:01,513 - train.progressive_trainer - INFO - [61/62] Epoch 347/400 Stage 3 lr:0.000016 total_loss:0.002663
2025-05-27 16:53:02,090 - train.progressive_trainer - INFO - [0/62] Epoch 348/400 Stage 3 lr:0.000016 total_loss:0.002526
2025-05-27 16:53:40,574 - train.progressive_trainer - INFO - [50/62] Epoch 348/400 Stage 3 lr:0.000016 total_loss:0.002673
2025-05-27 16:53:41,145 - train.progressive_trainer - INFO - [61/62] Epoch 348/400 Stage 3 lr:0.000016 total_loss:0.002701
2025-05-27 16:53:41,766 - train.progressive_trainer - INFO - [0/62] Epoch 349/400 Stage 3 lr:0.000016 total_loss:0.002689
2025-05-27 16:53:44,412 - train.progressive_trainer - INFO - [50/62] Epoch 349/400 Stage 3 lr:0.000016 total_loss:0.002513
2025-05-27 16:53:44,993 - train.progressive_trainer - INFO - [61/62] Epoch 349/400 Stage 3 lr:0.000016 total_loss:0.002731
2025-05-27 16:53:46,284 - train.progressive_trainer - INFO - [0/62] Epoch 350/400 Stage 3 lr:0.000016 total_loss:0.002691
2025-05-27 16:53:48,648 - train.progressive_trainer - INFO - [50/62] Epoch 350/400 Stage 3 lr:0.000016 total_loss:0.002596
2025-05-27 16:53:49,220 - train.progressive_trainer - INFO - [61/62] Epoch 350/400 Stage 3 lr:0.000016 total_loss:0.002775
2025-05-27 16:53:49,312 - train.trainer - INFO - Running scheduled evaluation at epoch 350 (eval_every=50)...
2025-05-27 16:53:49,312 - train.trainer - INFO - Evaluating model at epoch 350...
2025-05-27 16:53:49,317 - train.trainer - INFO - Evaluation logs will be saved to outputs_a100_fixed/logs/eval/eval_epoch_350.log
2025-05-27 16:53:49,318 - train.trainer - INFO - Generating 995 samples for evaluation...
2025-05-27 16:53:49,319 - train.trainer - INFO - Using PulsarAdaptiveSampler for evaluation
2025-05-27 16:53:49,321 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:49,321 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0035, std=1.0000
2025-05-27 16:53:49,420 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1983, std=0.2085, range=[-0.7977, 0.8432]
2025-05-27 16:53:51,180 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:51,180 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0002, std=0.9981
2025-05-27 16:53:51,283 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2104, std=0.1447, range=[-0.8291, 0.3648]
2025-05-27 16:53:51,284 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:51,285 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0006, std=0.9981
2025-05-27 16:53:51,467 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1918, std=0.1664, range=[-0.7974, 0.5786]
2025-05-27 16:53:51,479 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:51,479 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0006, std=0.9999
2025-05-27 16:53:51,760 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1977, std=0.1618, range=[-0.7672, 0.4386]
2025-05-27 16:53:51,761 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:51,761 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0008, std=0.9992
2025-05-27 16:53:51,966 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2238, std=0.2275, range=[-0.9288, 0.6776]
2025-05-27 16:53:51,967 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:51,968 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0009, std=0.9968
2025-05-27 16:53:52,153 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2144, std=0.2211, range=[-0.8349, 0.7282]
2025-05-27 16:53:52,154 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:52,154 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0039, std=0.9966
2025-05-27 16:53:52,353 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1953, std=0.1669, range=[-0.8230, 0.4624]
2025-05-27 16:53:52,354 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:52,354 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0010, std=1.0009
2025-05-27 16:53:52,565 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2056, std=0.1836, range=[-0.6889, 0.7605]
2025-05-27 16:53:52,567 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:52,567 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0057, std=0.9991
2025-05-27 16:53:52,771 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2423, std=0.1870, range=[-0.8039, 0.5668]
2025-05-27 16:53:52,772 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:52,772 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0013, std=0.9974
2025-05-27 16:53:52,961 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2053, std=0.1984, range=[-0.7997, 0.8047]
2025-05-27 16:53:52,962 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:52,962 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0013, std=0.9998
2025-05-27 16:53:53,165 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2011, std=0.1701, range=[-0.7520, 0.5984]
2025-05-27 16:53:53,166 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:53,166 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0018, std=0.9974
2025-05-27 16:53:53,361 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2322, std=0.1948, range=[-0.8599, 0.7302]
2025-05-27 16:53:53,363 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:53,364 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0002, std=0.9997
2025-05-27 16:53:53,581 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2140, std=0.1945, range=[-0.9305, 0.6848]
2025-05-27 16:53:53,582 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:53,582 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0027, std=0.9951
2025-05-27 16:53:53,784 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2305, std=0.1529, range=[-0.8547, 0.5773]
2025-05-27 16:53:53,785 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:53,785 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0051, std=0.9987
2025-05-27 16:53:53,995 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1918, std=0.1368, range=[-0.4937, 0.6976]
2025-05-27 16:53:53,996 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:53,996 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0054, std=0.9973
2025-05-27 16:53:54,226 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2131, std=0.1524, range=[-0.7316, 0.4753]
2025-05-27 16:53:54,227 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:54,227 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=-0.0011, std=0.9967
2025-05-27 16:53:54,457 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2072, std=0.1352, range=[-0.7022, 0.6566]
2025-05-27 16:53:54,458 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:54,458 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0046, std=0.9984
2025-05-27 16:53:54,657 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.1990, std=0.2009, range=[-0.8292, 0.8241]
2025-05-27 16:53:54,658 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=50, steps=20
2025-05-27 16:53:54,658 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0026, std=0.9985
2025-05-27 16:53:54,857 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2067, std=0.1759, range=[-0.7140, 0.6052]
2025-05-27 16:53:54,858 - models.pulsar_adaptive_sampler - INFO - 开始采样: batch_size=45, steps=20
2025-05-27 16:53:54,858 - models.pulsar_adaptive_sampler - INFO - 初始样本: mean=0.0013, std=0.9957
2025-05-27 16:53:55,062 - models.pulsar_adaptive_sampler - INFO - 采样完成: mean=-0.2047, std=0.1921, range=[-0.7231, 0.8444]
2025-05-27 16:53:55,381 - train.trainer - INFO - Generated 995 images with shape torch.Size([995, 3, 32, 32])
2025-05-27 16:53:55,585 - train.trainer - INFO - Saved 100 samples to outputs_a100_fixed/checkpoints/eval_epoch_350/generated_samples.pt
2025-05-27 16:53:55,585 - train.trainer - INFO - Loading real images for evaluation...
2025-05-27 16:53:57,198 - train.trainer - INFO - Loaded 995 real images with shape torch.Size([995, 3, 32, 32])
2025-05-27 16:53:57,198 - train.trainer - INFO - Calculating overall FID and IS metrics...
2025-05-27 16:55:23,395 - train.trainer - INFO - Overall evaluation completed: FID=364.9170, IS=1.0287±0.0067
2025-05-27 16:55:23,396 - train.trainer - INFO - Calculating channel-specific metrics...
2025-05-27 16:58:59,180 - train.trainer - INFO - Channel-specific evaluation completed
2025-05-27 16:58:59,181 - train.trainer - INFO - ==================================================
2025-05-27 16:58:59,181 - train.trainer - INFO - EVALUATION RESULTS (Epoch 350):
2025-05-27 16:58:59,181 - train.trainer - INFO - ==================================================
2025-05-27 16:58:59,181 - train.trainer - INFO - Overall FID: 364.9170
2025-05-27 16:58:59,181 - train.trainer - INFO - Overall IS: 1.0287 ± 0.0067
2025-05-27 16:58:59,181 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:58:59,181 - train.trainer - INFO - Period-DM surface:
2025-05-27 16:58:59,181 - train.trainer - INFO -   FID = 399.3133
2025-05-27 16:58:59,181 - train.trainer - INFO -   IS  = 1.0342 ± 0.0142
2025-05-27 16:58:59,181 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:58:59,181 - train.trainer - INFO - Phase-Subband surface:
2025-05-27 16:58:59,181 - train.trainer - INFO -   FID = 393.3803
2025-05-27 16:58:59,181 - train.trainer - INFO -   IS  = 1.0607 ± 0.0158
2025-05-27 16:58:59,181 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:58:59,181 - train.trainer - INFO - Phase-Subintegration surface:
2025-05-27 16:58:59,181 - train.trainer - INFO -   FID = 446.2858
2025-05-27 16:58:59,181 - train.trainer - INFO -   IS  = 1.0109 ± 0.0068
2025-05-27 16:58:59,181 - train.trainer - INFO - --------------------------------------------------
2025-05-27 16:58:59,181 - train.trainer - INFO - Evaluation completed in 309.87 seconds
2025-05-27 16:58:59,332 - train.trainer - INFO - Saved evaluation results to outputs_a100_fixed/logs/eval/results_epoch_350.json
2025-05-27 16:58:59,332 - train.trainer - INFO - Removed evaluation log handler
2025-05-27 16:58:59,338 - train.trainer - INFO - Evaluation metrics at epoch 350:
2025-05-27 16:58:59,338 - train.trainer - INFO -   FID: 364.91699656563867
2025-05-27 16:58:59,338 - train.trainer - INFO -   IS: 1.0287307500839233 ± 0.006721499375998974
2025-05-27 16:58:59,338 - train.trainer - INFO - Early stopping: No improvement at epoch 350 (current FID: 364.9170, best: 267.5613, counter: 3/3)
2025-05-27 16:58:59,338 - train.trainer - INFO - ============================================================
2025-05-27 16:58:59,339 - train.trainer - INFO - EARLY STOPPING TRIGGERED
2025-05-27 16:58:59,339 - train.trainer - INFO - ============================================================
2025-05-27 16:58:59,339 - train.trainer - INFO - No improvement in FID score for 3 consecutive evaluations
2025-05-27 16:58:59,339 - train.trainer - INFO - Best FID score: 267.5613
2025-05-27 16:58:59,339 - train.trainer - INFO - Current FID score: 364.9170
2025-05-27 16:58:59,339 - train.trainer - INFO - Training stopped at epoch 350
2025-05-27 16:58:59,339 - train.trainer - INFO - ============================================================
2025-05-27 16:58:59,339 - train.trainer - INFO - Early stopping triggered, breaking training loop
2025-05-27 16:58:59,339 - train.trainer - INFO - ================================================================================
2025-05-27 16:58:59,340 - train.trainer - INFO - SKIPPING FINAL EVALUATION - LAST EPOCH ALREADY EVALUATED
2025-05-27 16:58:59,340 - train.trainer - INFO - Final epoch: 350, Max epochs: 400, Eval every: 50
2025-05-27 16:58:59,340 - train.trainer - INFO - ================================================================================
2025-05-27 16:58:59,340 - train.trainer - INFO - ==================================================
2025-05-27 16:58:59,340 - train.trainer - INFO - FINAL RESULTS (FROM LAST EPOCH EVALUATION):
2025-05-27 16:58:59,340 - train.trainer - INFO - ==================================================
2025-05-27 16:58:59,340 - train.trainer - INFO -   FID: nan
2025-05-27 16:58:59,341 - train.trainer - INFO -   IS: nan ± nan
2025-05-27 16:58:59,341 - train.trainer - INFO -   Channel 0:
2025-05-27 16:58:59,341 - train.trainer - INFO -     FID = nan
2025-05-27 16:58:59,341 - train.trainer - INFO -     IS  = nan ± nan
2025-05-27 16:58:59,341 - train.trainer - INFO -   Channel 1:
2025-05-27 16:58:59,341 - train.trainer - INFO -     FID = nan
2025-05-27 16:58:59,341 - train.trainer - INFO -     IS  = nan ± nan
2025-05-27 16:58:59,341 - train.trainer - INFO -   Channel 2:
2025-05-27 16:58:59,341 - train.trainer - INFO -     FID = nan
2025-05-27 16:58:59,342 - train.trainer - INFO -     IS  = nan ± nan
2025-05-27 16:58:59,342 - train.trainer - INFO - ==================================================
2025-05-27 16:58:59,342 - train.trainer - INFO - Training completed in 6303.76 seconds
2025-05-27 16:58:59,343 - __main__ - INFO - ================================================================================
2025-05-27 16:58:59,343 - __main__ - INFO - 🎉 训练完成！
2025-05-27 16:58:59,343 - __main__ - INFO - ================================================================================
