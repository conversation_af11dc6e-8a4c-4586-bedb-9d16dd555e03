#!/usr/bin/env python3
"""
Physics-Constrained Loss Function Analysis and Optimization
基于Phase 2测试结果的损失函数分析和优化建议
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Tuple

class OptimizedPhysicsConstrainedLoss(nn.Module):
    """
    优化的物理约束损失函数

    基于Phase 2分析结果的改进版本：
    - 调整权重比例
    - 修复padding问题
    - 添加损失缩放机制
    """

    def __init__(self,
                 mse_weight: float = 0.8,
                 physics_weight: float = 0.05,  # 大幅降低
                 consistency_weight: float = 0.15,  # 增加
                 loss_scaling: bool = True,
                 adaptive_weights: bool = True):
        """
        初始化优化的物理约束损失

        Args:
            mse_weight: MSE损失权重 (增加到0.8)
            physics_weight: 物理约束权重 (降低到0.05)
            consistency_weight: 一致性约束权重 (增加到0.15)
            loss_scaling: 是否启用损失缩放
            adaptive_weights: 是否使用自适应权重
        """
        super().__init__()

        self.mse_weight = mse_weight
        self.physics_weight = physics_weight
        self.consistency_weight = consistency_weight
        self.loss_scaling = loss_scaling
        self.adaptive_weights = adaptive_weights

        self.mse_loss = nn.MSELoss()

        # 自适应权重参数
        if adaptive_weights:
            self.weight_ema_decay = 0.99
            self.register_buffer('mse_ema', torch.tensor(1.0))
            self.register_buffer('physics_ema', torch.tensor(1.0))
            self.register_buffer('consistency_ema', torch.tensor(1.0))

    def forward(self,
                pred: torch.Tensor,
                target: torch.Tensor,
                training_step: int = 0) -> Dict[str, torch.Tensor]:
        """
        计算优化的物理约束损失

        Args:
            pred: 预测结果 (B, C, H, W)
            target: 目标结果 (B, C, H, W)
            training_step: 训练步数（用于自适应权重）

        Returns:
            损失字典
        """
        # 基础MSE损失
        mse_loss = self.mse_loss(pred, target)

        # 优化的物理约束损失
        physics_loss = self._compute_optimized_physics_constraints(pred, target)

        # 增强的通道一致性损失
        consistency_loss = self._compute_enhanced_channel_consistency(pred, target)

        # 损失缩放
        if self.loss_scaling:
            physics_loss = self._scale_physics_loss(physics_loss, mse_loss)
            consistency_loss = self._scale_consistency_loss(consistency_loss, mse_loss)

        # 自适应权重调整
        if self.adaptive_weights and training_step > 100:
            weights = self._compute_adaptive_weights(mse_loss, physics_loss, consistency_loss)
        else:
            weights = {
                'mse': self.mse_weight,
                'physics': self.physics_weight,
                'consistency': self.consistency_weight
            }

        # 总损失
        total_loss = (weights['mse'] * mse_loss +
                     weights['physics'] * physics_loss +
                     weights['consistency'] * consistency_loss)

        return {
            'total_loss': total_loss,
            'mse_loss': mse_loss,
            'physics_loss': physics_loss,
            'consistency_loss': consistency_loss,
            'weights': weights
        }

    def _compute_optimized_physics_constraints(self,
                                             pred: torch.Tensor,
                                             target: torch.Tensor) -> torch.Tensor:
        """计算优化的物理约束损失"""
        # 1. 修复的周期性约束 (Period-DM通道)
        period_loss = self._fixed_periodicity_constraint(pred[:, 0:1], target[:, 0:1])

        # 2. 优化的垂直条纹约束 (Phase-Subband通道)
        stripe_loss = self._optimized_vertical_stripe_constraint(pred[:, 1:2], target[:, 1:2])

        # 3. 改进的相位一致性约束 (Phase-Subintegration通道)
        phase_loss = self._improved_phase_consistency_constraint(pred[:, 2:3], target[:, 2:3])

        # 加权平均，考虑通道复杂度
        weighted_loss = (0.4 * period_loss +    # Period-DM: 简单
                        0.35 * stripe_loss +     # Phase-Subband: 中等
                        0.25 * phase_loss)       # Phase-Subintegration: 复杂

        return weighted_loss

    def _fixed_periodicity_constraint(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """修复的周期性约束损失"""
        # 使用正确的padding策略
        kernel_size = 3
        padding = kernel_size // 2

        # 计算水平方向的局部相关性（避免padding='same'问题）
        pred_shifted = F.pad(pred, (1, 1, 0, 0), mode='circular')[:, :, :, 1:-1]
        target_shifted = F.pad(target, (1, 1, 0, 0), mode='circular')[:, :, :, 1:-1]

        pred_corr = F.mse_loss(pred, pred_shifted)
        target_corr = F.mse_loss(target, target_shifted)

        return F.mse_loss(pred_corr, target_corr)

    def _optimized_vertical_stripe_constraint(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """优化的垂直条纹约束损失"""
        # 使用Sobel算子检测垂直边缘
        sobel_v = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]],
                              dtype=pred.dtype, device=pred.device).view(1, 1, 3, 3)

        pred_edges = F.conv2d(pred, sobel_v, padding=1)
        target_edges = F.conv2d(target, sobel_v, padding=1)

        return F.mse_loss(pred_edges, target_edges)

    def _improved_phase_consistency_constraint(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """改进的相位一致性约束损失"""
        # 计算局部方差而非全局方差
        kernel_size = 5
        padding = kernel_size // 2

        # 使用平均池化计算局部统计
        pred_local_mean = F.avg_pool2d(pred, kernel_size, stride=1, padding=padding)
        target_local_mean = F.avg_pool2d(target, kernel_size, stride=1, padding=padding)

        pred_local_var = F.avg_pool2d((pred - pred_local_mean) ** 2, kernel_size, stride=1, padding=padding)
        target_local_var = F.avg_pool2d((target - target_local_mean) ** 2, kernel_size, stride=1, padding=padding)

        return F.mse_loss(pred_local_var, target_local_var)

    def _compute_enhanced_channel_consistency(self,
                                            pred: torch.Tensor,
                                            target: torch.Tensor) -> torch.Tensor:
        """计算增强的通道间一致性损失"""
        # 基于Phase 1发现的通道相关性>0.99
        pred_corr = self._compute_channel_correlation_matrix(pred)
        target_corr = self._compute_channel_correlation_matrix(target)

        # 重点关注高相关性的保持
        correlation_loss = F.mse_loss(pred_corr, target_corr)

        # 添加通道间能量比例一致性
        pred_energy = torch.mean(pred ** 2, dim=(2, 3))  # (B, C)
        target_energy = torch.mean(target ** 2, dim=(2, 3))  # (B, C)

        pred_energy_ratio = pred_energy / (pred_energy.sum(dim=1, keepdim=True) + 1e-8)
        target_energy_ratio = target_energy / (target_energy.sum(dim=1, keepdim=True) + 1e-8)

        energy_loss = F.mse_loss(pred_energy_ratio, target_energy_ratio)

        return correlation_loss + 0.5 * energy_loss

    def _compute_channel_correlation_matrix(self, x: torch.Tensor) -> torch.Tensor:
        """计算通道间相关矩阵"""
        B, C, H, W = x.shape
        x_flat = x.view(B, C, -1)  # (B, C, H*W)

        # 中心化
        x_centered = x_flat - x_flat.mean(dim=2, keepdim=True)

        # 计算相关矩阵
        correlation = torch.bmm(x_centered, x_centered.transpose(1, 2))

        # 归一化
        norm = torch.sqrt(torch.diagonal(correlation, dim1=1, dim2=2))
        correlation = correlation / (norm.unsqueeze(2) * norm.unsqueeze(1) + 1e-8)

        return correlation

    def _scale_physics_loss(self, physics_loss: torch.Tensor, mse_loss: torch.Tensor) -> torch.Tensor:
        """缩放物理损失到合理范围"""
        # 将物理损失缩放到MSE损失的相同数量级
        scale_factor = mse_loss.detach() / (physics_loss.detach() + 1e-8)
        scale_factor = torch.clamp(scale_factor, 0.01, 10.0)  # 限制缩放范围
        return physics_loss * scale_factor

    def _scale_consistency_loss(self, consistency_loss: torch.Tensor, mse_loss: torch.Tensor) -> torch.Tensor:
        """缩放一致性损失到合理范围"""
        # 确保一致性损失不会过小
        if consistency_loss < 0.01 * mse_loss:
            scale_factor = 0.01 * mse_loss / (consistency_loss + 1e-8)
            return consistency_loss * scale_factor
        return consistency_loss

    def _compute_adaptive_weights(self,
                                mse_loss: torch.Tensor,
                                physics_loss: torch.Tensor,
                                consistency_loss: torch.Tensor) -> Dict[str, float]:
        """计算自适应权重"""
        # 更新EMA
        self.mse_ema = self.weight_ema_decay * self.mse_ema + (1 - self.weight_ema_decay) * mse_loss.detach()
        self.physics_ema = self.weight_ema_decay * self.physics_ema + (1 - self.weight_ema_decay) * physics_loss.detach()
        self.consistency_ema = self.weight_ema_decay * self.consistency_ema + (1 - self.weight_ema_decay) * consistency_loss.detach()

        # 计算相对权重
        total_ema = self.mse_ema + self.physics_ema + self.consistency_ema

        mse_weight = 0.6 + 0.2 * (self.mse_ema / total_ema)
        physics_weight = 0.02 + 0.08 * (self.physics_ema / total_ema)
        consistency_weight = 1.0 - mse_weight - physics_weight

        return {
            'mse': float(mse_weight),
            'physics': float(physics_weight),
            'consistency': float(consistency_weight)
        }

def analyze_loss_function_performance():
    """分析损失函数性能"""
    print("📊 Physics-Constrained Loss Function Analysis")
    print("="*60)

    # 创建测试数据
    batch_size, channels, height, width = 4, 3, 32, 32
    pred = torch.randn(batch_size, channels, height, width, requires_grad=True)
    target = torch.randn(batch_size, channels, height, width)

    # 原始损失函数
    print("\n🔍 原始损失函数分析:")
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from models.channel_specific_processing import PhysicsConstrainedLoss
    original_loss_fn = PhysicsConstrainedLoss(
        mse_weight=0.7,
        physics_weight=0.2,
        consistency_weight=0.1
    )

    original_losses = original_loss_fn(pred, target)
    print(f"  总损失: {original_losses['total_loss'].item():.4f}")
    print(f"  MSE损失: {original_losses['mse_loss'].item():.4f}")
    print(f"  物理损失: {original_losses['physics_loss'].item():.4f}")
    print(f"  一致性损失: {original_losses['consistency_loss'].item():.4f}")

    # 优化损失函数
    print("\n✨ 优化损失函数分析:")
    optimized_loss_fn = OptimizedPhysicsConstrainedLoss(
        mse_weight=0.8,
        physics_weight=0.05,
        consistency_weight=0.15,
        loss_scaling=True,
        adaptive_weights=True
    )

    optimized_losses = optimized_loss_fn(pred, target, training_step=200)
    print(f"  总损失: {optimized_losses['total_loss'].item():.4f}")
    print(f"  MSE损失: {optimized_losses['mse_loss'].item():.4f}")
    print(f"  物理损失: {optimized_losses['physics_loss'].item():.4f}")
    print(f"  一致性损失: {optimized_losses['consistency_loss'].item():.4f}")
    print(f"  自适应权重: {optimized_losses['weights']}")

    # 损失比例分析
    print("\n📈 损失比例对比:")
    original_ratio = original_losses['physics_loss'] / original_losses['mse_loss']
    optimized_ratio = optimized_losses['physics_loss'] / optimized_losses['mse_loss']

    print(f"  原始物理/MSE比例: {original_ratio.item():.2f}")
    print(f"  优化物理/MSE比例: {optimized_ratio.item():.2f}")
    print(f"  改善倍数: {original_ratio / optimized_ratio:.2f}x")

    # 建议
    print("\n💡 优化建议:")
    print("  1. 使用优化的权重配置: MSE(0.8), Physics(0.05), Consistency(0.15)")
    print("  2. 启用损失缩放机制，防止物理损失过大")
    print("  3. 修复padding='same'问题，使用circular padding")
    print("  4. 增强通道一致性约束，利用>0.99相关性")
    print("  5. 启用自适应权重，动态平衡损失组件")

    return optimized_loss_fn

if __name__ == "__main__":
    optimized_loss_fn = analyze_loss_function_performance()
