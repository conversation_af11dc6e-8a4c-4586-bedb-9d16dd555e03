{"phase2_start_time": "2025-05-27T12:52:21.935454", "phase1_evidence_summary": {"data_accuracy": "100% verified - 995 pulsar samples confirmed", "channel_similarity": "High correlation >0.99 but distinct differences confirmed", "cnn_vs_transformer": "CNN 9.5/10 vs Transformer 2.0/10 based on literature", "sample_size_challenge": "995 samples << 14M threshold for ViT advantage", "computational_efficiency": "CNN 7.3x more parameter efficient", "astronomical_precedent": "ResNet achieved 98%+ accuracy on HTRU2", "normalization_status": "Current range [23,229] is correct original data"}, "implementation_strategy": {"A": {"name": "数据处理优化", "priority": 1, "evidence_support": 9.5, "tasks": ["implement_signal_preserving_normalization", "implement_astronomical_data_augmentation", "implement_channel_specific_processing"]}, "B": {"name": "CNN架构替换", "priority": 2, "evidence_support": 9.5, "tasks": ["remove_transformer_components", "implement_cnn_based_unet", "maintain_sampler_compatibility"]}, "C": {"name": "训练策略增强", "priority": 3, "evidence_support": 8.7, "tasks": ["implement_small_sample_optimization", "implement_physics_constrained_loss", "implement_ema_evaluation"]}}, "detailed_tasks": {"A1_signal_preserving_normalization": {"description": "实施基于正样本统计的信号保留型归一化", "evidence": "Phase 1发现当前归一化压缩信号至[-1,0.23]", "implementation": ["分析995个正样本的统计分布", "设计基于正样本[26,226]范围的归一化", "实现通道独立归一化策略", "验证信号保真度"], "files_to_modify": ["data/dataset.py", "data/transforms.py"], "tests_required": ["test_data_loading.py", "test_normalization.py"]}, "A2_astronomical_data_augmentation": {"description": "实施天文领域特定数据增强技术", "evidence": "替代多尺度采样，32x32图像不适合下采样", "implementation": ["时间轴循环移位增强", "频率轴小幅移位增强", "射电噪声注入策略", "小角度旋转和强度缩放"], "files_to_modify": ["data/augmentation.py", "data/dataset.py"], "tests_required": ["test_augmentation.py"]}, "A3_channel_specific_processing": {"description": "基于物理意义的通道特定处理权重", "evidence": "Period-DM、Phase-Subband、Phase-Subintegration复杂度不同", "implementation": ["Period-DM: 简单处理，权重0.4", "Phase-Subband: 中等处理，权重0.35", "Phase-Subintegration: 复杂处理，权重0.25", "动态权重调整机制"], "files_to_modify": ["models/channel_processing.py", "train/loss.py"], "tests_required": ["test_channel_processing.py"]}, "B1_remove_transformer_components": {"description": "完全移除Transformer组件", "evidence": "CNN在995样本规模下优势明显", "implementation": ["识别所有Transformer相关代码", "安全移除CrossChannelTransformer", "移除SpatialTransformerBlock", "清理相关导入和配置"], "files_to_modify": ["models/unet.py", "models/transformer.py"], "tests_required": ["test_model_creation.py", "test_forward_pass.py"]}, "B2_implement_cnn_based_unet": {"description": "实现轻量级CNN-based U-Net架构", "evidence": "15-25M参数，CNN架构适合小样本学习", "implementation": ["设计ResNet块替代Transformer", "实现空间注意力机制", "保持U-Net跳跃连接", "优化参数效率"], "files_to_modify": ["models/cnn_unet.py", "models/attention.py"], "tests_required": ["test_cnn_unet.py", "test_attention.py"]}, "B3_maintain_sampler_compatibility": {"description": "保持与PulsarAdaptiveSampler的兼容性", "evidence": "PulsarAdaptiveSampler已验证有效", "implementation": ["确保新架构输出格式兼容", "保持扩散过程接口", "验证采样器集成", "测试端到端流程"], "files_to_modify": ["models/diffusion.py", "sampling/sampler.py"], "tests_required": ["test_sampler_integration.py"]}, "C1_small_sample_optimization": {"description": "实施小样本学习优化", "evidence": "995样本需要专门的学习策略", "implementation": ["实现对比学习损失", "添加知识蒸馏机制", "优化正则化策略", "设计自适应学习率"], "files_to_modify": ["train/small_sample.py", "train/optimizer.py"], "tests_required": ["test_small_sample.py"]}, "C2_physics_constrained_loss": {"description": "基于脉冲星信号特性的物理约束损失", "evidence": "脉冲星信号具有特定物理约束", "implementation": ["周期性约束损失", "频域相关性损失", "能量守恒约束", "信号稀疏性正则化"], "files_to_modify": ["train/physics_loss.py", "train/trainer.py"], "tests_required": ["test_physics_loss.py"]}, "C3_ema_evaluation": {"description": "集成指数移动平均评估方法", "evidence": "EMA在小样本学习中提供更稳定的评估", "implementation": ["实现EMA模型更新", "集成到评估流程", "优化EMA衰减率", "对比标准评估结果"], "files_to_modify": ["evaluation/ema.py", "train/trainer.py"], "tests_required": ["test_ema.py"]}}, "success_criteria": {"performance_targets": {"fid_score": "<40 (current: 220-371)", "is_score": ">5 (current: 1.21-1.41)", "training_stability": "No NaN/Inf warnings", "convergence_speed": "50% faster than current"}, "code_quality_targets": {"test_coverage": ">95%", "no_redundant_code": "Complete removal of unused components", "modular_design": "Clear separation of concerns", "documentation": "Complete API documentation"}, "technical_targets": {"parameter_efficiency": "15-25M parameters (vs current 23M)", "memory_usage": "<30GB on A100", "training_time": "<8 hours for 400 epochs", "inference_speed": "<1s per sample"}}, "risk_assessment": {"high_risk": {"transformer_removal": {"risk": "Breaking existing functionality", "mitigation": "Modular replacement with extensive testing"}}, "medium_risk": {"performance_regression": {"risk": "Temporary performance drop during transition", "mitigation": "Staged implementation with rollback capability"}}, "low_risk": {"data_augmentation": {"risk": "Minimal impact on existing pipeline", "mitigation": "Additive implementation"}}}, "timeline": {"Phase_A_data_processing": "2-3 days", "Phase_B_architecture": "4-5 days", "Phase_C_training_strategy": "2-3 days", "testing_and_validation": "2 days", "total_estimated_time": "10-13 days", "parallel_execution_possible": "A and C can be done in parallel"}}