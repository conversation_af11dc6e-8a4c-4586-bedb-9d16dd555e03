#!/bin/bash
# Optuna超参数自动优化启动脚本
# 目标：FID<40, IS>5

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查环境
check_environment() {
    log_step "检查运行环境..."
    
    # 检查conda环境
    if [[ "$CONDA_DEFAULT_ENV" != "wgan_env" ]]; then
        log_error "请先激活conda环境: conda activate wgan_env"
        exit 1
    fi
    
    # 检查GPU
    if ! command -v nvidia-smi &> /dev/null; then
        log_error "未检测到NVIDIA GPU"
        exit 1
    fi
    
    # 检查Python包
    python -c "import optuna, torch" 2>/dev/null || {
        log_error "缺少必要的Python包，请安装: pip install optuna"
        exit 1
    }
    
    log_info "环境检查通过"
}

# 显示GPU信息
show_gpu_info() {
    log_step "GPU信息:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits | while read line; do
        echo "  $line"
    done
}

# 主函数
main() {
    log_info "🚀 启动PulsarDDPM Optuna超参数优化"
    log_info "目标: FID<40, IS>5"
    
    # 检查环境
    check_environment
    show_gpu_info
    
    # 进入项目目录
    cd "$(dirname "$0")"
    
    # 设置优化参数
    STUDY_NAME="pulsar_ddpm_optimization_$(date +%Y%m%d_%H%M%S)"
    N_TRIALS=50
    TIMEOUT=115200  # 32小时
    N_JOBS=1
    DEVICE="cuda"
    
    log_info "优化配置:"
    log_info "  研究名称: $STUDY_NAME"
    log_info "  试验次数: $N_TRIALS"
    log_info "  超时时间: $TIMEOUT 秒 (32小时)"
    log_info "  并行作业: $N_JOBS"
    log_info "  设备: $DEVICE"
    
    # 创建结果目录
    mkdir -p optuna_results
    
    # 启动优化
    log_step "开始Optuna超参数优化..."
    
    python optuna_optimization.py \
        --study_name "$STUDY_NAME" \
        --n_trials $N_TRIALS \
        --timeout $TIMEOUT \
        --n_jobs $N_JOBS \
        --device "$DEVICE" \
        2>&1 | tee "optuna_results/optimization_$(date +%Y%m%d_%H%M%S).log"
    
    # 检查结果
    if [ -f "optuna_results/best_config.json" ]; then
        log_info "✅ 优化完成！最佳配置已保存"
        log_info "最佳配置文件: optuna_results/best_config.json"
        log_info "训练脚本: optuna_results/train_best_config.sh"
        
        # 显示最佳配置摘要
        if command -v jq &> /dev/null; then
            log_info "最佳配置摘要:"
            echo "  学习率: $(jq -r '.lr' optuna_results/best_config.json)"
            echo "  物理损失权重: $(jq -r '.physics_weight' optuna_results/best_config.json)"
            echo "  基础通道数: $(jq -r '.base_channels' optuna_results/best_config.json)"
            echo "  Transformer维度: $(jq -r '.transformer_dim' optuna_results/best_config.json)"
        fi
        
        # 询问是否立即开始最佳配置训练
        read -p "是否立即使用最佳配置开始训练? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_step "启动最佳配置训练..."
            bash optuna_results/train_best_config.sh
        else
            log_info "可以稍后运行: bash optuna_results/train_best_config.sh"
        fi
    else
        log_warn "优化可能未完成或出现错误"
        log_info "请检查日志文件获取详细信息"
    fi
}

# 信号处理
trap 'log_warn "优化被中断"; exit 130' INT TERM

# 执行主函数
main "$@"
