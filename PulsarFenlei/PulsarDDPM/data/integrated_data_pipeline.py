#!/usr/bin/env python3
"""
集成数据处理管道
将SignalPreservingNormalization和AstronomicalDataAugmentation完全集成到训练流程
"""

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Tuple, Optional, Dict, Any
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from signal_preserving_transforms import SignalPreservingNormalization, AstronomicalDataAugmentation

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegratedDataProcessor:
    """
    集成数据处理器

    整合所有数据处理组件：
    1. SignalPreservingNormalization (信号保留型归一化)
    2. AstronomicalDataAugmentation (天文数据增强)
    3. 通道特定处理权重
    """

    def __init__(self,
                 normalization_method: str = 'positive_sample_based',
                 enable_augmentation: bool = True,
                 augmentation_prob: float = 0.7,
                 preserve_physics: bool = True,
                 device: str = "cuda"):
        """
        初始化集成数据处理器

        Args:
            normalization_method: 归一化方法
            enable_augmentation: 是否启用数据增强
            augmentation_prob: 数据增强概率
            preserve_physics: 是否保持物理特性
            device: 设备
        """
        self.device = device
        self.enable_augmentation = enable_augmentation
        self.preserve_physics = preserve_physics

        # 初始化信号保留型归一化
        self.normalizer = SignalPreservingNormalization(
            normalization_method=normalization_method,
            channel_independent=True,
            preserve_signal_range=True
        )

        # 初始化天文数据增强
        if enable_augmentation:
            self.augmenter = AstronomicalDataAugmentation(
                time_shift_range=0.1,
                freq_shift_range=0.05,
                noise_injection_prob=0.3,
                rotation_range=5.0
            )
        else:
            self.augmenter = None

        # 通道特定权重 (基于Phase 1分析)
        self.channel_weights = {
            0: 0.4,   # Period-DM: 简单，权重最高
            1: 0.35,  # Phase-Subband: 中等复杂
            2: 0.25   # Phase-Subintegration: 最复杂，权重最低
        }

        logger.info(f"集成数据处理器初始化完成")
        logger.info(f"  归一化方法: {normalization_method}")
        logger.info(f"  数据增强: {'启用' if enable_augmentation else '禁用'}")
        logger.info(f"  物理特性保持: {'启用' if preserve_physics else '禁用'}")

    def process_batch(self,
                     raw_data: np.ndarray,
                     training: bool = True) -> torch.Tensor:
        """
        处理一个批次的数据

        Args:
            raw_data: 原始数据 (B, H, W, C) 或 (H, W, C)
            training: 是否为训练模式

        Returns:
            处理后的数据张量 (B, C, H, W)
        """
        # 确保数据格式正确
        if raw_data.ndim == 3:
            raw_data = raw_data[np.newaxis, ...]  # 添加批次维度

        batch_size = raw_data.shape[0]
        processed_batch = []

        for i in range(batch_size):
            sample = raw_data[i]  # (H, W, C)

            # 1. 信号保留型归一化
            normalized = self.normalizer.normalize(sample)

            # 2. 数据增强 (仅在训练时)
            if training and self.augmenter is not None:
                augmented = self.augmenter.augment(
                    normalized,
                    preserve_physics=self.preserve_physics
                )
            else:
                augmented = normalized

            # 3. 通道特定权重应用
            weighted = self._apply_channel_weights(augmented)

            # 4. 转换为PyTorch张量格式 (C, H, W)
            tensor = torch.from_numpy(weighted).float()
            if tensor.shape[-1] == 3:  # (H, W, C) -> (C, H, W)
                tensor = tensor.permute(2, 0, 1)

            processed_batch.append(tensor)

        # 堆叠为批次张量
        batch_tensor = torch.stack(processed_batch, dim=0)  # (B, C, H, W)

        # 确保数据范围在[-1, 1]
        batch_tensor = torch.clamp(batch_tensor, -1.0, 1.0)

        # 只在需要时移动到GPU（避免pin memory问题）
        return batch_tensor

    def _apply_channel_weights(self, data: np.ndarray) -> np.ndarray:
        """应用通道特定权重"""
        weighted_data = data.copy()

        for ch in range(min(3, data.shape[-1])):
            weight = self.channel_weights[ch]
            weighted_data[..., ch] *= weight

        return weighted_data

    def get_statistics(self, data_batch: torch.Tensor) -> Dict[str, float]:
        """获取处理后数据的统计信息"""
        return {
            'mean': data_batch.mean().item(),
            'std': data_batch.std().item(),
            'min': data_batch.min().item(),
            'max': data_batch.max().item(),
            'range': data_batch.max().item() - data_batch.min().item()
        }

class EnhancedHTRU1Dataset:
    """
    增强版HTRU1数据集

    集成所有数据处理优化组件
    """

    def __init__(self,
                 original_dataset,
                 normalization_method: str = 'positive_sample_based',
                 enable_augmentation: bool = True,
                 augmentation_prob: float = 0.7,
                 preserve_physics: bool = True,
                 device: str = "cuda"):
        """
        初始化增强版数据集

        Args:
            original_dataset: 原始HTRU1Dataset实例
            其他参数同IntegratedDataProcessor
        """
        self.original_dataset = original_dataset
        self.processor = IntegratedDataProcessor(
            normalization_method=normalization_method,
            enable_augmentation=enable_augmentation,
            augmentation_prob=augmentation_prob,
            preserve_physics=preserve_physics,
            device=device
        )

        logger.info(f"增强版HTRU1数据集初始化完成")
        logger.info(f"  原始数据集大小: {len(original_dataset)}")

    def __len__(self):
        return len(self.original_dataset)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        """
        获取处理后的数据样本

        Args:
            idx: 样本索引

        Returns:
            (processed_image, label)
        """
        # 获取原始数据 (这里会应用原有的归一化)
        img, label = self.original_dataset[idx]

        # 转换为numpy格式进行进一步处理
        if isinstance(img, torch.Tensor):
            img_np = img.permute(1, 2, 0).cpu().numpy()  # (C, H, W) -> (H, W, C)
        else:
            img_np = np.array(img)

        # 应用集成数据处理
        processed_img = self.processor.process_batch(
            img_np,
            training=self.original_dataset.train if hasattr(self.original_dataset, 'train') else True
        )

        # 移除批次维度
        processed_img = processed_img.squeeze(0)  # (1, C, H, W) -> (C, H, W)

        return processed_img, label

def create_enhanced_dataloader(
    original_dataloader,
    normalization_method: str = 'positive_sample_based',
    enable_augmentation: bool = True,
    augmentation_prob: float = 0.7,
    preserve_physics: bool = True,
    device: str = "cuda"
):
    """
    创建增强版数据加载器

    Args:
        original_dataloader: 原始数据加载器
        其他参数同IntegratedDataProcessor

    Returns:
        增强版数据加载器
    """
    # 获取原始数据集
    original_dataset = original_dataloader.dataset

    # 创建增强版数据集
    enhanced_dataset = EnhancedHTRU1Dataset(
        original_dataset=original_dataset,
        normalization_method=normalization_method,
        enable_augmentation=enable_augmentation,
        augmentation_prob=augmentation_prob,
        preserve_physics=preserve_physics,
        device=device
    )

    # 创建新的数据加载器
    from torch.utils.data import DataLoader

    enhanced_dataloader = DataLoader(
        enhanced_dataset,
        batch_size=original_dataloader.batch_size,
        shuffle=hasattr(original_dataloader, 'shuffle') and original_dataloader.shuffle,
        num_workers=original_dataloader.num_workers,
        drop_last=original_dataloader.drop_last,
        pin_memory=False  # 禁用pin_memory避免GPU张量问题
    )

    logger.info(f"增强版数据加载器创建成功")
    logger.info(f"  批次大小: {enhanced_dataloader.batch_size}")
    logger.info(f"  工作进程数: {enhanced_dataloader.num_workers}")

    return enhanced_dataloader

def test_integrated_data_pipeline():
    """测试集成数据处理管道"""
    logger.info("🧪 测试集成数据处理管道")

    device = "cuda" if torch.cuda.is_available() else "cpu"

    # 创建模拟HTRU1数据
    batch_size = 4
    raw_data = np.random.randint(26, 227, (batch_size, 32, 32, 3)).astype(np.uint8)

    logger.info(f"原始数据: 形状{raw_data.shape}, 范围[{raw_data.min()}, {raw_data.max()}]")

    # 测试不同配置
    configs = [
        {
            'name': '基础配置',
            'normalization_method': 'positive_sample_based',
            'enable_augmentation': False
        },
        {
            'name': '完整配置',
            'normalization_method': 'positive_sample_based',
            'enable_augmentation': True,
            'augmentation_prob': 0.7
        },
        {
            'name': '通道独立配置',
            'normalization_method': 'channel_independent',
            'enable_augmentation': True,
            'augmentation_prob': 0.5
        }
    ]

    for config in configs:
        logger.info(f"\n测试配置: {config['name']}")

        processor = IntegratedDataProcessor(
            normalization_method=config['normalization_method'],
            enable_augmentation=config.get('enable_augmentation', True),
            augmentation_prob=config.get('augmentation_prob', 0.7),
            preserve_physics=True,
            device=device
        )

        # 处理数据
        processed = processor.process_batch(raw_data, training=True)

        # 获取统计信息
        stats = processor.get_statistics(processed)

        logger.info(f"  处理后数据: 形状{processed.shape}")
        logger.info(f"  统计信息: mean={stats['mean']:.4f}, std={stats['std']:.4f}")
        logger.info(f"  数据范围: [{stats['min']:.4f}, {stats['max']:.4f}]")

        # 验证数据范围
        assert -1.1 <= stats['min'] <= 1.1, f"数据范围异常: min={stats['min']}"
        assert -1.1 <= stats['max'] <= 1.1, f"数据范围异常: max={stats['max']}"

        logger.info(f"  ✅ {config['name']} 测试通过")

    logger.info("\n✅ 集成数据处理管道测试完成")
    logger.info("✅ 所有配置测试通过")
    logger.info("✅ 数据范围验证通过")
    logger.info("✅ 准备集成到训练流程")

if __name__ == "__main__":
    test_integrated_data_pipeline()
