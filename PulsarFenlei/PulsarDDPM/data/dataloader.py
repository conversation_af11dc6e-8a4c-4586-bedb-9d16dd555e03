import os
import pickle
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader, Subset
import torchvision.transforms as transforms
from typing import Optional, Tuple, List, Dict, Any, Union

# OPTIMIZATION 3.1: Import ChannelSpecificAugmenter for pulsar-specific data augmentation
import sys
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from alternative_channel_strategies import ChannelSpecificAugmenter
    CHANNEL_AUGMENTER_AVAILABLE = True
except ImportError:
    print("Warning: Could not import ChannelSpecificAugmenter from alternative_channel_strategies")
    CHANNEL_AUGMENTER_AVAILABLE = False


class HTRU1Dataset(Dataset):
    """
    HTRU1 Dataset for pulsar generation.
    Only loads pulsar samples (label 0).

    Args:
        root (str): Root directory of the HTRU1 dataset
        train (bool): If True, creates dataset from training set, otherwise from test set
        transform (callable, optional): Optional transform to be applied on a sample
        download (bool): Not implemented, kept for compatibility
    """
    def __init__(
        self,
        root: str,
        train: bool = True,
        transform: Optional[callable] = None,
        download: bool = False,
        # OPTIMIZATION 3.1: Add pulsar-specific augmentation parameters
        use_channel_augmentation: bool = True,
        augmentation_prob: float = 0.7,
        progressive_augmentation: bool = False,
        current_epoch: int = 0,
        total_epochs: int = 400,
        # OPTIMIZATION 3.2: Progressive intensity parameters
        progressive_intensity: bool = False,
        min_intensity: float = 0.3,
        max_intensity: float = 1.0,
        # OPTIMIZATION 3.3: Physical constraint validation parameters
        enable_physical_validation: bool = True,
        peak_preservation_threshold: float = 0.8,
        stripe_coherence_threshold: float = 0.7,
        detail_variance_threshold: float = 0.5
    ):
        self.root = root
        self.transform = transform
        self.train = train

        # OPTIMIZATION 3.1: Initialize pulsar-specific augmentation
        self.use_channel_augmentation = use_channel_augmentation and train  # Only augment training data
        self.augmentation_prob = augmentation_prob
        self.progressive_augmentation = progressive_augmentation
        self.current_epoch = current_epoch
        self.total_epochs = total_epochs
        # OPTIMIZATION 3.2: Initialize progressive intensity parameters
        self.progressive_intensity = progressive_intensity
        self.min_intensity = min_intensity
        self.max_intensity = max_intensity
        # OPTIMIZATION 3.3: Initialize physical constraint validation parameters
        self.enable_physical_validation = enable_physical_validation
        self.peak_preservation_threshold = peak_preservation_threshold
        self.stripe_coherence_threshold = stripe_coherence_threshold
        self.detail_variance_threshold = detail_variance_threshold
        self.channel_augmenter = None

        if self.use_channel_augmentation and CHANNEL_AUGMENTER_AVAILABLE:
            # Calculate dynamic augmentation probability if progressive
            if self.progressive_augmentation:
                # Start with 0.3, gradually increase to augmentation_prob
                progress = min(1.0, self.current_epoch / (self.total_epochs * 0.5))
                dynamic_prob = 0.3 + (self.augmentation_prob - 0.3) * progress
            else:
                dynamic_prob = self.augmentation_prob

            # OPTIMIZATION 3.2: Calculate initial intensity
            if self.progressive_intensity:
                progress = min(1.0, self.current_epoch / (self.total_epochs * 0.6))
                initial_intensity = self.min_intensity + (self.max_intensity - self.min_intensity) * progress
            else:
                initial_intensity = self.max_intensity

            self.channel_augmenter = ChannelSpecificAugmenter(
                prob=dynamic_prob,
                intensity=initial_intensity,
                progressive_intensity=self.progressive_intensity,
                min_intensity=self.min_intensity,
                max_intensity=self.max_intensity,
                # OPTIMIZATION 3.3: Pass physical constraint validation parameters
                enable_physical_validation=self.enable_physical_validation,
                peak_preservation_threshold=self.peak_preservation_threshold,
                stripe_coherence_threshold=self.stripe_coherence_threshold,
                detail_variance_threshold=self.detail_variance_threshold
            )
            print(f"Initialized ChannelSpecificAugmenter with prob={dynamic_prob:.3f}, intensity={initial_intensity:.3f}")
        elif self.use_channel_augmentation:
            print("Warning: ChannelSpecificAugmenter requested but not available")
            self.use_channel_augmentation = False

        if download:
            print("Download not implemented. Please provide the dataset manually.")

        # Load data
        if self.train:
            self.data, self.targets = self._load_training_data()
        else:
            self.data, self.targets = self._load_test_data()

        # Filter to keep only pulsar samples (label 0)
        pulsar_indices = [i for i, label in enumerate(self.targets) if label == 0]
        self.data = [self.data[i] for i in pulsar_indices]
        self.targets = [self.targets[i] for i in pulsar_indices]

        print(f"Loaded {len(self.data)} pulsar samples from {'training' if self.train else 'test'} set")

    def _load_training_data(self) -> Tuple[List[np.ndarray], List[int]]:
        """Load training data from data_batch files"""
        data = []
        targets = []

        # Load data from data_batch_1 to data_batch_5
        for i in range(1, 6):
            file_path = os.path.join(self.root, f'data_batch_{i}')
            try:
                with open(file_path, 'rb') as f:
                    batch = pickle.load(f, encoding='bytes')
                    # 尝试字节键
                    if b'data' in batch:
                        data.extend(batch[b'data'])
                        targets.extend(batch[b'labels'])
                    # 尝试字符串键
                    elif 'data' in batch:
                        data.extend(batch['data'])
                        targets.extend(batch['labels'])
                    else:
                        raise KeyError(f"无法找到数据键，可用键: {list(batch.keys())}")
            except Exception as e:
                print(f"加载 {file_path} 时出错: {e}")
                raise

        return data, targets

    def _load_test_data(self) -> Tuple[List[np.ndarray], List[int]]:
        """Load test data from test_batch file"""
        file_path = os.path.join(self.root, 'test_batch')
        try:
            with open(file_path, 'rb') as f:
                batch = pickle.load(f, encoding='bytes')
                # 尝试字节键
                if b'data' in batch:
                    return batch[b'data'], batch[b'labels']
                # 尝试字符串键
                elif 'data' in batch:
                    return batch['data'], batch['labels']
                else:
                    raise KeyError(f"无法找到数据键，可用键: {list(batch.keys())}")
        except Exception as e:
            print(f"加载 {file_path} 时出错: {e}")
            raise

    def __len__(self) -> int:
        return len(self.data)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        img = self.data[idx]
        target = self.targets[idx]

        # 确保img是numpy数组格式
        if isinstance(img, list):
            img = np.array(img)
        elif not isinstance(img, np.ndarray):
            img = np.array(img)

        # 确保数据形状正确：应该是3072个元素 (3*32*32)
        if img.size == 3072:
            # Reshape to (3, 32, 32)
            img = img.reshape(3, 32, 32)
        else:
            raise ValueError(f"期望数据大小为3072，但得到{img.size}")

        # Convert to float32 first
        img = img.astype(np.float32)

        # 通道独立归一化：针对脉冲星数据的三个通道分别优化
        # Channel 0: Period-DM surface (低频变化)
        # Channel 1: Phase-Subband surface (中频条纹)
        # Channel 2: Phase-Subintegration surface (高频细节)
        normalized_channels = []

        for c in range(3):
            channel_data = img[c]

            # 获取通道的最小值和最大值
            c_min = channel_data.min()
            c_max = channel_data.max()

            # 数值稳定性检查：避免除零错误
            if c_max - c_min < 1e-8:
                # 如果通道数据几乎为常数，设置为零
                normalized_channel = np.zeros_like(channel_data)
            else:
                # 通道独立归一化到[-1, 1]范围
                # 保持每个通道的完整动态范围
                normalized_channel = 2.0 * (channel_data - c_min) / (c_max - c_min) - 1.0

            normalized_channels.append(normalized_channel)

        # 重新组合归一化后的通道
        img = np.stack(normalized_channels, axis=0)

        # 最终数值范围检查
        img = np.clip(img, -1.0, 1.0)

        # Convert to tensor
        img = torch.from_numpy(img)

        # OPTIMIZATION 3.1: Apply channel-specific augmentation before standard transforms
        if self.use_channel_augmentation and self.channel_augmenter is not None:
            img = self.channel_augmenter(img)

        # Apply standard transforms
        if self.transform:
            img = self.transform(img)

        # 确保数据在[-1, 1]范围内
        img = torch.clamp(img, -1.0, 1.0)

        return img, target

    # OPTIMIZATION 3.1: Method to update augmentation parameters during training
    def update_augmentation_params(self, current_epoch: int):
        """Update augmentation parameters based on current training epoch"""
        self.current_epoch = current_epoch

        if self.use_channel_augmentation and self.channel_augmenter is not None:
            # Update probability if progressive
            if self.progressive_augmentation:
                # Recalculate dynamic augmentation probability
                progress = min(1.0, self.current_epoch / (self.total_epochs * 0.5))
                dynamic_prob = 0.3 + (self.augmentation_prob - 0.3) * progress
                self.channel_augmenter.prob = dynamic_prob
            else:
                dynamic_prob = self.augmentation_prob

            # OPTIMIZATION 3.2: Update intensity if progressive
            if self.progressive_intensity:
                intensity = self.channel_augmenter.update_intensity(self.current_epoch, self.total_epochs)
            else:
                intensity = self.channel_augmenter.intensity

            return dynamic_prob, intensity

        return (self.augmentation_prob if self.use_channel_augmentation else 0.0, 1.0)


def get_transforms(augment: bool = True) -> transforms.Compose:
    """
    Get transforms for HTRU1 dataset

    Args:
        augment (bool): Whether to apply data augmentation

    Returns:
        transforms.Compose: Composition of transforms
    """
    if augment:
        return transforms.Compose([
            transforms.RandomHorizontalFlip(),
            transforms.RandomVerticalFlip(),
            # No need for ToTensor as data is already in tensor format
        ])
    else:
        return transforms.Compose([
            # No need for ToTensor as data is already in tensor format
        ])


def get_dataloader(
    root: str,
    batch_size: int,
    train: bool = True,
    augment: bool = True,
    num_workers: int = 4,
    shuffle: bool = True,
    drop_last: bool = True,
    resolution: int = 32,
    # OPTIMIZATION 3.1: Add pulsar-specific augmentation parameters
    use_channel_augmentation: bool = True,
    augmentation_prob: float = 0.7,
    progressive_augmentation: bool = False,
    current_epoch: int = 0,
    total_epochs: int = 400,
    # OPTIMIZATION 3.2: Progressive intensity parameters
    progressive_intensity: bool = False,
    min_intensity: float = 0.3,
    max_intensity: float = 1.0,
    # OPTIMIZATION 3.3: Physical constraint validation parameters
    enable_physical_validation: bool = True,
    peak_preservation_threshold: float = 0.8,
    stripe_coherence_threshold: float = 0.7,
    detail_variance_threshold: float = 0.5
) -> DataLoader:
    """
    Get dataloader for HTRU1 dataset

    Args:
        root (str): Root directory of the HTRU1 dataset
        batch_size (int): Batch size
        train (bool): If True, creates dataloader from training set, otherwise from test set
        augment (bool): Whether to apply data augmentation
        num_workers (int): Number of workers for dataloader
        shuffle (bool): Whether to shuffle the dataset
        drop_last (bool): Whether to drop the last incomplete batch
        resolution (int): Image resolution (16 or 32)

    Returns:
        DataLoader: DataLoader for HTRU1 dataset
    """
    transform = get_transforms(augment)
    dataset = HTRU1Dataset(
        root=root,
        train=train,
        transform=transform,
        # OPTIMIZATION 3.1: Pass pulsar-specific augmentation parameters
        use_channel_augmentation=use_channel_augmentation,
        augmentation_prob=augmentation_prob,
        progressive_augmentation=progressive_augmentation,
        current_epoch=current_epoch,
        total_epochs=total_epochs,
        # OPTIMIZATION 3.2: Pass progressive intensity parameters
        progressive_intensity=progressive_intensity,
        min_intensity=min_intensity,
        max_intensity=max_intensity,
        # OPTIMIZATION 3.3: Pass physical constraint validation parameters
        enable_physical_validation=enable_physical_validation,
        peak_preservation_threshold=peak_preservation_threshold,
        stripe_coherence_threshold=stripe_coherence_threshold,
        detail_variance_threshold=detail_variance_threshold
    )

    # If resolution is 16, add downsampling transform
    if resolution == 16:
        resize_transform = transforms.Resize(16, antialias=True)
        dataset = TransformedDataset(dataset, resize_transform)

    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        drop_last=drop_last,
        pin_memory=True
    )


class TransformedDataset(Dataset):
    """
    Dataset wrapper that applies a transform to the outputs of another dataset.

    Args:
        dataset (Dataset): The dataset to wrap
        transform (callable): The transform to apply
    """
    def __init__(self, dataset: Dataset, transform: callable):
        self.dataset = dataset
        self.transform = transform

    def __len__(self) -> int:
        return len(self.dataset)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        img, target = self.dataset[idx]
        img = self.transform(img)
        return img, target
