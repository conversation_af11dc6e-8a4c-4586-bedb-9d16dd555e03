#!/usr/bin/env python3
"""
PulsarDDPM专用数据增强模块
针对HTRU1数据集的三个通道进行通道特定的数据增强
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional, Dict, Any
import random


class PulsarChannelAugmentation(nn.Module):
    """
    脉冲星通道特定数据增强
    
    针对HTRU1数据集的三个通道设计：
    - Channel 0: Period-DM surface (周期-色散测量表面)
    - Channel 1: Phase-Subband surface (相位-子带表面)  
    - Channel 2: Phase-Subintegration surface (相位-子积分表面)
    """
    
    def __init__(
        self,
        rotation_prob: float = 0.5,
        flip_prob: float = 0.3,
        noise_prob: float = 0.4,
        freq_aug_prob: float = 0.3,
        mixup_prob: float = 0.2,
        preserve_pulsar_features: bool = True
    ):
        super().__init__()
        self.rotation_prob = rotation_prob
        self.flip_prob = flip_prob
        self.noise_prob = noise_prob
        self.freq_aug_prob = freq_aug_prob
        self.mixup_prob = mixup_prob
        self.preserve_pulsar_features = preserve_pulsar_features
        
        # 通道特定的增强参数
        self.channel_configs = {
            0: {  # Period-DM surface
                'name': 'Period-DM',
                'rotation_angles': [90, 180, 270],  # 允许的旋转角度
                'flip_axes': [0, 1],  # 允许的翻转轴
                'noise_std': 0.02,  # 噪声标准差
                'freq_bands': [(0.1, 0.3), (0.3, 0.7)],  # 频域增强带
                'preserve_center': True  # 保护中心区域
            },
            1: {  # Phase-Subband surface
                'name': 'Phase-Subband',
                'rotation_angles': [90, 180, 270],
                'flip_axes': [1],  # 只允许水平翻转，保持相位连续性
                'noise_std': 0.015,
                'freq_bands': [(0.05, 0.25), (0.25, 0.5)],
                'preserve_center': False
            },
            2: {  # Phase-Subintegration surface
                'name': 'Phase-Subintegration',
                'rotation_angles': [180],  # 只允许180度旋转
                'flip_axes': [0],  # 只允许垂直翻转
                'noise_std': 0.01,
                'freq_bands': [(0.1, 0.4), (0.4, 0.8)],
                'preserve_center': True
            }
        }
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播：应用通道特定的数据增强
        
        Args:
            x: 输入张量 (B, 3, H, W)
        Returns:
            增强后的张量 (B, 3, H, W)
        """
        if not self.training:
            return x
        
        batch_size = x.size(0)
        augmented = x.clone()
        
        for i in range(batch_size):
            for channel in range(3):
                # 获取单个通道数据
                channel_data = augmented[i, channel:channel+1]  # (1, H, W)
                
                # 应用通道特定增强
                channel_data = self._apply_channel_augmentation(
                    channel_data, channel
                )
                
                # 更新增强后的数据
                augmented[i, channel:channel+1] = channel_data
        
        return augmented
    
    def _apply_channel_augmentation(
        self, 
        channel_data: torch.Tensor, 
        channel_idx: int
    ) -> torch.Tensor:
        """应用单个通道的增强"""
        config = self.channel_configs[channel_idx]
        
        # 1. 通道特定旋转
        if random.random() < self.rotation_prob:
            channel_data = self._channel_specific_rotation(channel_data, config)
        
        # 2. 通道特定翻转
        if random.random() < self.flip_prob:
            channel_data = self._channel_specific_flip(channel_data, config)
        
        # 3. 自适应噪声注入
        if random.random() < self.noise_prob:
            channel_data = self._adaptive_noise_injection(channel_data, config)
        
        # 4. 频域增强
        if random.random() < self.freq_aug_prob:
            channel_data = self._frequency_domain_augmentation(channel_data, config)
        
        return channel_data
    
    def _channel_specific_rotation(
        self, 
        data: torch.Tensor, 
        config: Dict[str, Any]
    ) -> torch.Tensor:
        """通道特定旋转"""
        if not config['rotation_angles']:
            return data
        
        angle = random.choice(config['rotation_angles'])
        k = angle // 90  # 90度的倍数
        
        # 使用torch.rot90进行旋转
        rotated = torch.rot90(data, k, dims=[-2, -1])
        
        # 如果需要保护中心区域（对于Period-DM和Phase-Subintegration）
        if config.get('preserve_center', False):
            h, w = data.shape[-2:]
            center_h, center_w = h // 4, w // 4
            h_start, h_end = h//2 - center_h//2, h//2 + center_h//2
            w_start, w_end = w//2 - center_w//2, w//2 + center_w//2
            
            # 保持中心区域不变
            rotated[..., h_start:h_end, w_start:w_end] = \
                data[..., h_start:h_end, w_start:w_end]
        
        return rotated
    
    def _channel_specific_flip(
        self, 
        data: torch.Tensor, 
        config: Dict[str, Any]
    ) -> torch.Tensor:
        """通道特定翻转"""
        if not config['flip_axes']:
            return data
        
        axis = random.choice(config['flip_axes'])
        # axis=0对应垂直翻转(dim=-2), axis=1对应水平翻转(dim=-1)
        flip_dim = -2 if axis == 0 else -1
        
        return torch.flip(data, dims=[flip_dim])
    
    def _adaptive_noise_injection(
        self, 
        data: torch.Tensor, 
        config: Dict[str, Any]
    ) -> torch.Tensor:
        """自适应噪声注入"""
        # 计算数据的局部方差来自适应噪声强度
        local_std = torch.std(data)
        adaptive_noise_std = config['noise_std'] * (0.5 + local_std.item())
        
        # 生成噪声
        noise = torch.randn_like(data) * adaptive_noise_std
        
        # 应用噪声，但保持数据范围
        noisy_data = data + noise
        
        # 确保数据仍在合理范围内
        return torch.clamp(noisy_data, -1.0, 1.0)
    
    def _frequency_domain_augmentation(
        self, 
        data: torch.Tensor, 
        config: Dict[str, Any]
    ) -> torch.Tensor:
        """频域增强"""
        # 转换到频域
        fft_data = torch.fft.fft2(data)
        fft_shifted = torch.fft.fftshift(fft_data)
        
        # 获取频率掩码
        h, w = data.shape[-2:]
        freq_mask = self._create_frequency_mask(h, w, config['freq_bands'])
        freq_mask = freq_mask.to(data.device)
        
        # 应用频域滤波
        filtered_fft = fft_shifted * freq_mask
        
        # 转换回空域
        filtered_fft_shifted = torch.fft.ifftshift(filtered_fft)
        filtered_data = torch.fft.ifft2(filtered_fft_shifted).real
        
        # 混合原始数据和滤波数据
        alpha = 0.7  # 混合比例
        mixed_data = alpha * data + (1 - alpha) * filtered_data
        
        return torch.clamp(mixed_data, -1.0, 1.0)
    
    def _create_frequency_mask(
        self, 
        h: int, 
        w: int, 
        freq_bands: list
    ) -> torch.Tensor:
        """创建频域掩码"""
        mask = torch.ones(1, h, w)
        
        # 创建频率坐标
        freq_h = torch.fft.fftfreq(h).abs()
        freq_w = torch.fft.fftfreq(w).abs()
        freq_grid = torch.sqrt(freq_h[:, None]**2 + freq_w[None, :]**2)
        
        # 应用频带滤波
        for low_freq, high_freq in freq_bands:
            band_mask = (freq_grid >= low_freq) & (freq_grid <= high_freq)
            mask[0] *= (1.0 + 0.3 * band_mask.float())  # 增强特定频带
        
        return mask


class PulsarMixupAugmentation(nn.Module):
    """脉冲星混合样本生成"""
    
    def __init__(self, alpha: float = 0.2, prob: float = 0.2):
        super().__init__()
        self.alpha = alpha
        self.prob = prob
    
    def forward(
        self, 
        x: torch.Tensor, 
        y: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Mixup增强
        
        Args:
            x: 输入图像 (B, C, H, W)
            y: 标签 (B,) 可选
        Returns:
            混合后的图像和标签
        """
        if not self.training or random.random() > self.prob:
            return x, y
        
        batch_size = x.size(0)
        
        # 生成混合权重
        lam = np.random.beta(self.alpha, self.alpha)
        
        # 随机排列索引
        indices = torch.randperm(batch_size).to(x.device)
        
        # 混合图像
        mixed_x = lam * x + (1 - lam) * x[indices]
        
        # 混合标签（如果提供）
        if y is not None:
            mixed_y = (y, y[indices], lam)
            return mixed_x, mixed_y
        
        return mixed_x, None


def create_pulsar_augmentation_pipeline(
    rotation_prob: float = 0.5,
    flip_prob: float = 0.3,
    noise_prob: float = 0.4,
    freq_aug_prob: float = 0.3,
    mixup_prob: float = 0.2,
    mixup_alpha: float = 0.2
) -> nn.Module:
    """创建完整的脉冲星数据增强流水线"""
    
    class PulsarAugmentationPipeline(nn.Module):
        def __init__(self):
            super().__init__()
            self.channel_aug = PulsarChannelAugmentation(
                rotation_prob=rotation_prob,
                flip_prob=flip_prob,
                noise_prob=noise_prob,
                freq_aug_prob=freq_aug_prob,
                mixup_prob=0.0  # Mixup单独处理
            )
            self.mixup_aug = PulsarMixupAugmentation(
                alpha=mixup_alpha,
                prob=mixup_prob
            )
        
        def forward(self, x, y=None):
            # 先应用通道特定增强
            x = self.channel_aug(x)
            
            # 再应用Mixup
            x, y = self.mixup_aug(x, y)
            
            return x, y
    
    return PulsarAugmentationPipeline()


# 测试函数
def test_pulsar_augmentation():
    """测试脉冲星数据增强"""
    print("🧪 测试脉冲星数据增强模块")
    
    # 创建测试数据
    batch_size = 4
    test_data = torch.randn(batch_size, 3, 32, 32)
    test_labels = torch.zeros(batch_size)
    
    # 创建增强流水线
    aug_pipeline = create_pulsar_augmentation_pipeline()
    aug_pipeline.train()
    
    # 测试增强
    augmented_data, augmented_labels = aug_pipeline(test_data, test_labels)
    
    print(f"✅ 原始数据形状: {test_data.shape}")
    print(f"✅ 增强数据形状: {augmented_data.shape}")
    print(f"✅ 数据范围: [{augmented_data.min():.4f}, {augmented_data.max():.4f}]")
    
    # 验证数值范围
    assert augmented_data.min() >= -1.1 and augmented_data.max() <= 1.1
    print("✅ 数值范围验证通过")
    
    return True


if __name__ == "__main__":
    test_pulsar_augmentation()
