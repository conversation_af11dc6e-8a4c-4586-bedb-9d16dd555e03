#!/usr/bin/env python3
"""
信号保留型数据变换
基于Phase 1分析，实施保留脉冲星信号特征的归一化和变换方法
"""

import numpy as np
import torch
from typing import Tuple, Optional, Dict, Any
from pathlib import Path
import pickle

class SignalPreservingNormalization:
    """
    基于HTRU1正样本统计的信号保留型归一化
    
    Phase 1证据:
    - 正样本范围: [26, 226] (实际数据范围)
    - 三通道统计: 均值87.2±32.8
    - 当前[-1,1]归一化压缩信号至[-1,0.23]
    """
    
    def __init__(self, 
                 normalization_method: str = 'positive_sample_based',
                 channel_independent: bool = True,
                 preserve_signal_range: bool = True):
        """
        初始化信号保留型归一化
        
        Args:
            normalization_method: 归一化方法 ('positive_sample_based', 'channel_independent', 'global')
            channel_independent: 是否对每个通道独立归一化
            preserve_signal_range: 是否保留信号的完整动态范围
        """
        self.method = normalization_method
        self.channel_independent = channel_independent
        self.preserve_signal_range = preserve_signal_range
        
        # 基于Phase 1分析的HTRU1正样本统计
        self.positive_sample_stats = self._load_positive_sample_stats()
        
    def _load_positive_sample_stats(self) -> Dict[str, Any]:
        """加载正样本统计数据"""
        # 基于Phase 1验证的真实统计数据
        return {
            'global_stats': {
                'min': 26, 'max': 226,
                'mean': 87.2, 'std': 32.8
            },
            'channel_stats': {
                0: {'min': 26, 'max': 225, 'mean': 87.207, 'std': 32.778},
                1: {'min': 26, 'max': 226, 'mean': 87.192, 'std': 32.772},
                2: {'min': 26, 'max': 226, 'mean': 87.203, 'std': 32.793}
            }
        }
    
    def normalize(self, data: np.ndarray) -> np.ndarray:
        """
        执行信号保留型归一化
        
        Args:
            data: 输入数据 (H, W, C) 或 (N, H, W, C)
            
        Returns:
            归一化后的数据，保留脉冲星信号特征
        """
        if self.method == 'positive_sample_based':
            return self._positive_sample_normalization(data)
        elif self.method == 'channel_independent':
            return self._channel_independent_normalization(data)
        elif self.method == 'global':
            return self._global_normalization(data)
        else:
            raise ValueError(f"Unknown normalization method: {self.method}")
    
    def _positive_sample_normalization(self, data: np.ndarray) -> np.ndarray:
        """
        基于正样本统计的归一化
        使用995个正样本的真实统计范围进行归一化
        """
        if self.channel_independent and data.ndim >= 3:
            # 通道独立归一化
            normalized = np.zeros_like(data, dtype=np.float32)
            
            for ch in range(data.shape[-1]):
                ch_stats = self.positive_sample_stats['channel_stats'][ch]
                ch_data = data[..., ch]
                
                if self.preserve_signal_range:
                    # 保留完整信号范围: [min, max] -> [-1, 1]
                    ch_min, ch_max = ch_stats['min'], ch_stats['max']
                    normalized[..., ch] = 2.0 * (ch_data - ch_min) / (ch_max - ch_min) - 1.0
                else:
                    # 基于均值和标准差的标准化
                    ch_mean, ch_std = ch_stats['mean'], ch_stats['std']
                    normalized[..., ch] = (ch_data - ch_mean) / ch_std
                    
            return normalized
        else:
            # 全局归一化
            stats = self.positive_sample_stats['global_stats']
            if self.preserve_signal_range:
                data_min, data_max = stats['min'], stats['max']
                return 2.0 * (data - data_min) / (data_max - data_min) - 1.0
            else:
                data_mean, data_std = stats['mean'], stats['std']
                return (data - data_mean) / data_std
    
    def _channel_independent_normalization(self, data: np.ndarray) -> np.ndarray:
        """通道独立归一化，基于每个通道的实际范围"""
        normalized = np.zeros_like(data, dtype=np.float32)
        
        for ch in range(data.shape[-1]):
            ch_data = data[..., ch]
            ch_min, ch_max = ch_data.min(), ch_data.max()
            
            if ch_max > ch_min:
                normalized[..., ch] = 2.0 * (ch_data - ch_min) / (ch_max - ch_min) - 1.0
            else:
                normalized[..., ch] = 0.0
                
        return normalized
    
    def _global_normalization(self, data: np.ndarray) -> np.ndarray:
        """全局归一化"""
        data_min, data_max = data.min(), data.max()
        if data_max > data_min:
            return 2.0 * (data - data_min) / (data_max - data_min) - 1.0
        else:
            return np.zeros_like(data, dtype=np.float32)
    
    def denormalize(self, normalized_data: np.ndarray) -> np.ndarray:
        """反归一化，恢复原始数据范围"""
        if self.method == 'positive_sample_based':
            return self._positive_sample_denormalization(normalized_data)
        else:
            # 简化的反归一化
            stats = self.positive_sample_stats['global_stats']
            data_min, data_max = stats['min'], stats['max']
            return (normalized_data + 1.0) * (data_max - data_min) / 2.0 + data_min
    
    def _positive_sample_denormalization(self, normalized_data: np.ndarray) -> np.ndarray:
        """基于正样本统计的反归一化"""
        if self.channel_independent and normalized_data.ndim >= 3:
            denormalized = np.zeros_like(normalized_data)
            
            for ch in range(normalized_data.shape[-1]):
                ch_stats = self.positive_sample_stats['channel_stats'][ch]
                ch_data = normalized_data[..., ch]
                
                if self.preserve_signal_range:
                    ch_min, ch_max = ch_stats['min'], ch_stats['max']
                    denormalized[..., ch] = (ch_data + 1.0) * (ch_max - ch_min) / 2.0 + ch_min
                else:
                    ch_mean, ch_std = ch_stats['mean'], ch_stats['std']
                    denormalized[..., ch] = ch_data * ch_std + ch_mean
                    
            return denormalized
        else:
            stats = self.positive_sample_stats['global_stats']
            if self.preserve_signal_range:
                data_min, data_max = stats['min'], stats['max']
                return (normalized_data + 1.0) * (data_max - data_min) / 2.0 + data_min
            else:
                data_mean, data_std = stats['mean'], stats['std']
                return normalized_data * data_std + data_mean

class AstronomicalDataAugmentation:
    """
    天文领域特定的数据增强技术
    
    基于Phase 1发现：32x32图像不适合多尺度采样
    替代方案：时间轴移位、频率轴移位、射电噪声注入
    """
    
    def __init__(self, 
                 time_shift_range: float = 0.1,
                 freq_shift_range: float = 0.05,
                 noise_injection_prob: float = 0.3,
                 rotation_range: float = 5.0,
                 intensity_scale_range: Tuple[float, float] = (0.9, 1.1)):
        """
        初始化天文数据增强
        
        Args:
            time_shift_range: 时间轴循环移位范围 (相对于图像宽度)
            freq_shift_range: 频率轴移位范围 (相对于图像高度)
            noise_injection_prob: 射电噪声注入概率
            rotation_range: 小角度旋转范围 (度)
            intensity_scale_range: 强度缩放范围
        """
        self.time_shift_range = time_shift_range
        self.freq_shift_range = freq_shift_range
        self.noise_injection_prob = noise_injection_prob
        self.rotation_range = rotation_range
        self.intensity_scale_range = intensity_scale_range
    
    def augment(self, data: np.ndarray, preserve_physics: bool = True) -> np.ndarray:
        """
        执行天文数据增强
        
        Args:
            data: 输入数据 (H, W, C)
            preserve_physics: 是否保留物理特性
            
        Returns:
            增强后的数据
        """
        augmented = data.copy()
        
        # 1. 时间轴循环移位 (保持周期性)
        if np.random.random() < 0.5:
            augmented = self._time_axis_shift(augmented)
        
        # 2. 频率轴小幅移位
        if np.random.random() < 0.3:
            augmented = self._frequency_axis_shift(augmented)
        
        # 3. 射电噪声注入
        if np.random.random() < self.noise_injection_prob:
            augmented = self._radio_noise_injection(augmented)
        
        # 4. 小角度旋转
        if np.random.random() < 0.2:
            augmented = self._small_angle_rotation(augmented)
        
        # 5. 强度缩放
        if np.random.random() < 0.4:
            augmented = self._intensity_scaling(augmented)
        
        # 物理约束验证
        if preserve_physics:
            augmented = self._enforce_physics_constraints(augmented, data)
        
        return augmented
    
    def _time_axis_shift(self, data: np.ndarray) -> np.ndarray:
        """时间轴循环移位，保持脉冲星周期性"""
        shift_pixels = int(np.random.uniform(-self.time_shift_range, self.time_shift_range) * data.shape[1])
        return np.roll(data, shift_pixels, axis=1)
    
    def _frequency_axis_shift(self, data: np.ndarray) -> np.ndarray:
        """频率轴小幅移位"""
        shift_pixels = int(np.random.uniform(-self.freq_shift_range, self.freq_shift_range) * data.shape[0])
        return np.roll(data, shift_pixels, axis=0)
    
    def _radio_noise_injection(self, data: np.ndarray) -> np.ndarray:
        """射电噪声注入"""
        noise_level = np.random.uniform(0.01, 0.05) * data.std()
        noise = np.random.normal(0, noise_level, data.shape)
        return data + noise
    
    def _small_angle_rotation(self, data: np.ndarray) -> np.ndarray:
        """小角度旋转 (简化实现)"""
        # 这里使用简单的转置来模拟小角度旋转
        if np.random.random() < 0.5:
            return np.transpose(data, (1, 0, 2))
        return data
    
    def _intensity_scaling(self, data: np.ndarray) -> np.ndarray:
        """强度缩放"""
        scale = np.random.uniform(*self.intensity_scale_range)
        return data * scale
    
    def _enforce_physics_constraints(self, augmented: np.ndarray, original: np.ndarray) -> np.ndarray:
        """强制执行物理约束"""
        # 确保增强后的数据保持合理的动态范围
        augmented = np.clip(augmented, original.min(), original.max())
        
        # 保持通道间的相对关系
        for ch in range(augmented.shape[-1]):
            ch_ratio = original[..., ch].mean() / (original.mean() + 1e-8)
            augmented[..., ch] = augmented[..., ch] * ch_ratio
        
        return augmented

def test_signal_preserving_transforms():
    """测试信号保留型变换"""
    print("🧪 测试信号保留型变换")
    
    # 创建模拟的HTRU1数据
    test_data = np.random.randint(26, 227, (32, 32, 3)).astype(np.uint8)
    
    # 测试归一化
    normalizer = SignalPreservingNormalization(
        normalization_method='positive_sample_based',
        channel_independent=True
    )
    
    normalized = normalizer.normalize(test_data)
    denormalized = normalizer.denormalize(normalized)
    
    print(f"原始数据范围: [{test_data.min()}, {test_data.max()}]")
    print(f"归一化后范围: [{normalized.min():.3f}, {normalized.max():.3f}]")
    print(f"反归一化后范围: [{denormalized.min():.1f}, {denormalized.max():.1f}]")
    
    # 测试数据增强
    augmenter = AstronomicalDataAugmentation()
    augmented = augmenter.augment(test_data.astype(np.float32))
    
    print(f"增强后数据范围: [{augmented.min():.1f}, {augmented.max():.1f}]")
    print("✅ 信号保留型变换测试通过")

if __name__ == "__main__":
    test_signal_preserving_transforms()
