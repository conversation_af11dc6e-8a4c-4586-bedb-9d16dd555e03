#!/usr/bin/env python3
"""
完整训练流程实现
三阶段渐进式训练：VAE预训练→WGAN-GP集成→联合优化

基于WGAN-GP+VAE实施计划的训练规格：
- 阶段1: VAE预训练 (50轮)
- 阶段2: WGAN-GP集成 (40轮) 
- 阶段3: 联合优化 (60轮)
- 总计: 150轮训练
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
import time
import os
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

from models.wgan_vae import PulsarWGANVAE, PulsarWGANVAETrainer, create_model_and_trainer
from utils.data_loader import create_htru1_dataloader, validate_dataloader
from utils.random_seed import set_random_seed

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TrainingConfig:
    """训练配置类"""
    # 基础配置
    latent_dim: int = 64
    batch_size: int = 16
    device: str = 'auto'
    random_seed: int = 42
    
    # 学习率配置
    vae_lr: float = 2e-4
    discriminator_lr: float = 4e-4
    
    # 训练轮数配置
    stage1_epochs: int = 50  # VAE预训练
    stage2_epochs: int = 40  # WGAN-GP集成
    stage3_epochs: int = 60  # 联合优化
    
    # 损失函数配置
    beta_vae: float = 1.0
    lambda_gp: float = 10.0
    n_critic: int = 5
    
    # 数据配置
    data_augment: bool = True
    augment_factor: int = 2
    num_workers: int = 4
    
    # 保存配置
    save_dir: str = './checkpoints'
    save_interval: int = 10
    log_interval: int = 5
    
    # 内存优化
    gradient_accumulation_steps: int = 1
    max_memory_gb: float = 12.0

class FullTrainingPipeline:
    """
    完整训练流程管道
    
    实现三阶段渐进式训练的完整流程：
    1. 数据加载和验证
    2. 模型和训练器创建
    3. 三阶段训练执行
    4. 检查点保存和恢复
    5. 训练监控和日志
    """
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        
        # 设备配置
        if config.device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(config.device)
        
        # 设置随机种子
        set_random_seed(config.random_seed)
        
        # 创建保存目录
        os.makedirs(config.save_dir, exist_ok=True)
        
        # 初始化组件
        self.model = None
        self.trainer = None
        self.dataloader = None
        self.training_history = {
            'stage1': [], 'stage2': [], 'stage3': []
        }
        
        logger.info(f"FullTrainingPipeline初始化:")
        logger.info(f"  设备: {self.device}")
        logger.info(f"  随机种子: {config.random_seed}")
        logger.info(f"  总训练轮数: {config.stage1_epochs + config.stage2_epochs + config.stage3_epochs}")
        logger.info(f"  保存目录: {config.save_dir}")
    
    def setup_data(self) -> DataLoader:
        """设置数据加载器"""
        logger.info("设置数据加载器...")
        
        # 创建数据加载器
        self.dataloader = create_htru1_dataloader(
            batch_size=self.config.batch_size,
            augment=self.config.data_augment,
            augment_factor=self.config.augment_factor,
            num_workers=self.config.num_workers
        )
        
        # 验证数据加载器
        stats = validate_dataloader(self.dataloader)
        
        logger.info(f"数据加载器设置完成:")
        logger.info(f"  批次大小: {self.config.batch_size}")
        logger.info(f"  批次数量: {len(self.dataloader)}")
        logger.info(f"  数据增强: {self.config.data_augment}")
        
        return self.dataloader
    
    def setup_model(self) -> Tuple[PulsarWGANVAE, PulsarWGANVAETrainer]:
        """设置模型和训练器"""
        logger.info("设置模型和训练器...")
        
        # 创建模型和训练器
        self.model, self.trainer = create_model_and_trainer(
            latent_dim=self.config.latent_dim,
            device=self.device,
            vae_lr=self.config.vae_lr,
            discriminator_lr=self.config.discriminator_lr
        )
        
        # 配置训练器参数
        self.trainer.loss_function.beta_vae = self.config.beta_vae
        self.trainer.loss_function.lambda_gp = self.config.lambda_gp
        self.trainer.n_critic = self.config.n_critic
        
        logger.info(f"模型和训练器设置完成:")
        logger.info(f"  总参数量: {self.model.total_params/1e6:.2f}M")
        logger.info(f"  VAE学习率: {self.config.vae_lr}")
        logger.info(f"  判别器学习率: {self.config.discriminator_lr}")
        
        return self.model, self.trainer
    
    def train_stage(self, stage: int, epochs: int) -> List[Dict[str, float]]:
        """训练单个阶段"""
        stage_name = {1: "VAE预训练", 2: "WGAN-GP集成", 3: "联合优化"}[stage]
        logger.info(f"开始{stage_name} (阶段{stage})，共{epochs}轮...")
        
        # 设置训练阶段
        self.trainer.set_stage(stage)
        
        # 训练历史
        stage_history = []
        
        # 内存监控
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            initial_memory = torch.cuda.memory_allocated() / 1024**3
        
        for epoch in range(epochs):
            epoch_start_time = time.time()
            epoch_metrics = []
            
            # 训练一个epoch
            for batch_idx, batch in enumerate(self.dataloader):
                batch_start_time = time.time()

                # 内存检查
                if torch.cuda.is_available():
                    current_memory = torch.cuda.memory_allocated() / 1024**3
                    if current_memory > self.config.max_memory_gb:
                        logger.warning(f"内存使用过高: {current_memory:.2f}GB")
                        torch.cuda.empty_cache()

                # 训练步骤 - 添加进度监控
                try:
                    if batch_idx % 50 == 0:  # 每50个批次打印进度
                        logger.info(f"  批次 {batch_idx+1}/{len(self.dataloader)}")

                    metrics = self.trainer.train_step(batch)

                    # 检查训练步骤时间
                    batch_time = time.time() - batch_start_time
                    if batch_time > 30:  # 如果单个批次超过30秒
                        logger.warning(f"批次{batch_idx+1}训练时间过长: {batch_time:.2f}s")

                    epoch_metrics.append(metrics)

                    # 检查NaN/Inf
                    for key, value in metrics.items():
                        if isinstance(value, (int, float)) and (np.isnan(value) or np.isinf(value)):
                            logger.error(f"训练指标异常: {key}={value}")
                            raise ValueError(f"训练出现数值问题: {key}={value}")

                except Exception as e:
                    logger.error(f"批次{batch_idx+1}训练失败: {e}")
                    # 不立即终止，尝试继续训练
                    continue
            
            # 计算epoch平均指标
            avg_metrics = {}
            for key in epoch_metrics[0].keys():
                avg_metrics[key] = np.mean([m[key] for m in epoch_metrics])
            
            # 添加时间信息
            epoch_time = time.time() - epoch_start_time
            avg_metrics['epoch_time'] = epoch_time
            avg_metrics['epoch'] = epoch
            avg_metrics['stage'] = stage
            
            stage_history.append(avg_metrics)
            
            # 日志输出
            if (epoch + 1) % self.config.log_interval == 0:
                logger.info(f"阶段{stage} Epoch {epoch+1}/{epochs}:")
                for key, value in avg_metrics.items():
                    if key not in ['epoch', 'stage', 'epoch_time']:
                        logger.info(f"  {key}: {value:.6f}")
                logger.info(f"  训练时间: {epoch_time:.2f}s")
                
                # 内存使用
                if torch.cuda.is_available():
                    current_memory = torch.cuda.memory_allocated() / 1024**3
                    logger.info(f"  GPU内存: {current_memory:.2f}GB")
            
            # 保存检查点
            if (epoch + 1) % self.config.save_interval == 0:
                self.save_checkpoint(stage, epoch + 1)
        
        logger.info(f"{stage_name}完成！")
        return stage_history
    
    def run_full_training(self) -> Dict[str, List[Dict[str, float]]]:
        """运行完整训练流程"""
        logger.info("🚀 开始完整训练流程...")
        
        # 设置数据和模型
        self.setup_data()
        self.setup_model()
        
        # 三阶段训练
        try:
            # 阶段1: VAE预训练
            self.training_history['stage1'] = self.train_stage(1, self.config.stage1_epochs)
            
            # 阶段2: WGAN-GP集成
            self.training_history['stage2'] = self.train_stage(2, self.config.stage2_epochs)
            
            # 阶段3: 联合优化
            self.training_history['stage3'] = self.train_stage(3, self.config.stage3_epochs)
            
            # 保存最终模型
            self.save_final_model()
            
            logger.info("🎉 完整训练流程成功完成！")
            
        except Exception as e:
            logger.error(f"训练过程中出现错误: {e}")
            raise
        
        return self.training_history
    
    def save_checkpoint(self, stage: int, epoch: int):
        """保存检查点"""
        checkpoint = {
            'stage': stage,
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'vae_optimizer_state_dict': self.trainer.vae_optimizer.state_dict(),
            'discriminator_optimizer_state_dict': self.trainer.discriminator_optimizer.state_dict(),
            'training_history': self.training_history,
            'config': self.config
        }
        
        checkpoint_path = os.path.join(
            self.config.save_dir, f'checkpoint_stage{stage}_epoch{epoch}.pth'
        )
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"检查点已保存: {checkpoint_path}")
    
    def save_final_model(self):
        """保存最终模型"""
        final_model_path = os.path.join(self.config.save_dir, 'final_model.pth')
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config,
            'training_history': self.training_history
        }, final_model_path)
        logger.info(f"最终模型已保存: {final_model_path}")
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.trainer.vae_optimizer.load_state_dict(checkpoint['vae_optimizer_state_dict'])
        self.trainer.discriminator_optimizer.load_state_dict(checkpoint['discriminator_optimizer_state_dict'])
        self.training_history = checkpoint['training_history']
        
        logger.info(f"检查点已加载: {checkpoint_path}")
        return checkpoint['stage'], checkpoint['epoch']

def create_training_pipeline(config: Optional[TrainingConfig] = None) -> FullTrainingPipeline:
    """创建训练流程管道"""
    if config is None:
        config = TrainingConfig()
    
    return FullTrainingPipeline(config)

def run_full_training(config: Optional[TrainingConfig] = None) -> Dict[str, List[Dict[str, float]]]:
    """运行完整训练流程的便捷函数"""
    pipeline = create_training_pipeline(config)
    return pipeline.run_full_training()
