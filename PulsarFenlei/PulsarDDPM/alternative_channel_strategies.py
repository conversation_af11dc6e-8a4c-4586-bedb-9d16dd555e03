"""
替代通道处理策略实现
在禁用channel_specific=True后，通过其他方法改善各通道的生成质量
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms.functional as TF
import random
import math
import numpy as np
from typing import Dict, List, Tuple, Optional

# OPTIMIZATION 2.3: Import for perceptual loss
try:
    import torchvision.models as models
    TORCHVISION_AVAILABLE = True
except ImportError:
    TORCHVISION_AVAILABLE = False


class ChannelSpecificAugmenter:
    """
    通道特定数据增强器
    为三个脉冲星通道设计专门的数据增强方法
    """

    def __init__(self,
                 prob: float = 0.7,
                 # OPTIMIZATION 3.2: Progressive augmentation intensity parameters
                 intensity: float = 1.0,
                 progressive_intensity: bool = False,
                 min_intensity: float = 0.3,
                 max_intensity: float = 1.0,
                 # OPTIMIZATION 3.3: Physical constraint validation parameters
                 enable_physical_validation: bool = True,
                 peak_preservation_threshold: float = 0.8,
                 stripe_coherence_threshold: float = 0.7,
                 detail_variance_threshold: float = 0.5,
                 # OPTIMIZATION 3.4: Adaptive augmentation probability parameters
                 adaptive_probability: bool = True,
                 loss_history_window: int = 10,
                 prob_adjustment_factor: float = 0.1,
                 min_prob: float = 0.3,
                 max_prob: float = 0.9):
        """
        Args:
            prob: 应用增强的概率
            intensity: 增强强度 (0.0-1.0)
            progressive_intensity: 是否使用渐进式强度调整
            min_intensity: 最小强度
            max_intensity: 最大强度
        """
        self.prob = prob
        self.intensity = intensity
        self.progressive_intensity = progressive_intensity
        self.min_intensity = min_intensity
        self.max_intensity = max_intensity

        # OPTIMIZATION 3.3: Physical constraint validation settings
        self.enable_physical_validation = enable_physical_validation
        self.peak_preservation_threshold = peak_preservation_threshold
        self.stripe_coherence_threshold = stripe_coherence_threshold
        self.detail_variance_threshold = detail_variance_threshold

        # OPTIMIZATION 3.4: Adaptive augmentation probability settings
        self.adaptive_probability = adaptive_probability
        self.loss_history_window = loss_history_window
        self.prob_adjustment_factor = prob_adjustment_factor
        self.min_prob = min_prob
        self.max_prob = max_prob
        self.base_prob = prob  # Store original probability

        # Training state tracking for adaptive probability
        self.loss_history = []
        self.validation_failure_count = 0
        self.total_augmentation_count = 0

    def enhance_period_dm(self, channel: torch.Tensor) -> torch.Tensor:
        """
        Period-DM通道增强 - 针对峰值特征保留

        Args:
            channel: 单通道图像 [1, H, W]
        Returns:
            增强后的通道图像
        """
        if random.random() > self.prob:
            return channel

        # 1. 非线性对比度增强 - 增强峰值区域
        mean_val = channel.mean()
        std_val = channel.std()

        # 增强高于均值的区域（峰值区域）
        mask = channel > mean_val
        enhanced = channel.clone()
        # OPTIMIZATION 3.2: Apply intensity scaling to contrast enhancement
        contrast_factor = 1.0 + 0.2 * self.intensity

        # 范围保持的对比度增强
        enhanced_values = mean_val + (channel[mask] - mean_val) * contrast_factor
        # 确保增强后的值不超出[-1, 1]范围
        enhanced_values = torch.clamp(enhanced_values, -1.0, 1.0)
        enhanced[mask] = enhanced_values

        # 2. 峰值位置抖动 - 小范围随机移动
        if random.random() < 0.5 * self.intensity:  # OPTIMIZATION 3.2: Scale jitter probability with intensity
            max_shift = max(1, int(2 * self.intensity))  # Scale shift range with intensity
            shift_x = random.randint(-max_shift, max_shift)
            shift_y = random.randint(-max_shift, max_shift)
            enhanced = torch.roll(enhanced, shifts=(shift_y, shift_x), dims=(1, 2))

        # 3. 高斯噪声添加 - 增强鲁棒性
        if random.random() < 0.3 * self.intensity:  # OPTIMIZATION 3.2: Scale noise probability with intensity
            noise_std = 0.02 * self.intensity  # Scale noise strength with intensity
            noise = torch.randn_like(enhanced) * noise_std
            # 范围保持的噪声添加
            enhanced = torch.clamp(enhanced + noise, -1.0, 1.0)

        # 4. 峰值强度缩放
        if random.random() < 0.4:
            scale = random.uniform(0.95, 1.05)
            # 范围保持的强度缩放
            scaled_values = mean_val + (enhanced - mean_val) * scale
            enhanced = torch.clamp(scaled_values, -1.0, 1.0)

        # 最终范围确保（作为安全网，但前面的操作应该已经保持了范围）
        return torch.clamp(enhanced, -1.0, 1.0)

    def enhance_phase_subband(self, channel: torch.Tensor) -> torch.Tensor:
        """
        Phase-Subband通道增强 - 针对垂直条纹结构保留

        Args:
            channel: 单通道图像 [1, H, W]
        Returns:
            增强后的通道图像
        """
        if random.random() > self.prob:
            return channel

        enhanced = channel.clone()

        # 1. 水平形变 - 保持垂直条纹，允许水平变化
        if random.random() < 0.6 * self.intensity:  # OPTIMIZATION 3.2: Scale deformation probability with intensity
            h, w = enhanced.shape[1:]
            # 创建水平形变场
            max_displacement = max(1, int(2 * self.intensity))  # Scale displacement with intensity
            displacement = random.uniform(-max_displacement, max_displacement)

            # 简化的水平位移
            if abs(displacement) > 0.5:
                shift = int(displacement)
                enhanced = torch.roll(enhanced, shifts=shift, dims=2)

        # 2. 条纹对比度增强
        if random.random() < 0.5:
            # 计算垂直方向的梯度来识别条纹
            grad_x = torch.diff(enhanced, dim=2, prepend=enhanced[:, :, :1])
            high_grad_mask = torch.abs(grad_x) > grad_x.std()

            # 范围保持的条纹对比度增强
            enhanced_values = enhanced[high_grad_mask] * 1.1
            enhanced_values = torch.clamp(enhanced_values, -1.0, 1.0)
            enhanced[high_grad_mask] = enhanced_values

        # 3. 垂直拉伸 - 轻微调整条纹长度
        if random.random() < 0.3:
            scale_factor = random.uniform(0.98, 1.02)
            enhanced = F.interpolate(
                enhanced.unsqueeze(0),
                scale_factor=(scale_factor, 1.0),
                mode='bilinear',
                align_corners=False
            ).squeeze(0)

            # 裁剪或填充到原始大小
            if enhanced.shape[1] != channel.shape[1]:
                if enhanced.shape[1] > channel.shape[1]:
                    # 裁剪
                    start = (enhanced.shape[1] - channel.shape[1]) // 2
                    enhanced = enhanced[:, start:start+channel.shape[1], :]
                else:
                    # 填充
                    pad = channel.shape[1] - enhanced.shape[1]
                    enhanced = F.pad(enhanced, (0, 0, pad//2, pad-pad//2))

        return torch.clamp(enhanced, -1, 1)

    def enhance_phase_subintegration(self, channel: torch.Tensor) -> torch.Tensor:
        """
        Phase-Subintegration通道增强 - 针对细节特征保留

        Args:
            channel: 单通道图像 [1, H, W]
        Returns:
            增强后的通道图像
        """
        if random.random() > self.prob:
            return channel

        enhanced = channel.clone()

        # 1. 局部对比度增强 - 使用Unsharp Masking
        if random.random() < 0.6:
            # 创建高斯模糊
            kernel_size = 3
            sigma = 0.5
            kernel = self._gaussian_kernel(kernel_size, sigma).to(enhanced.device)

            # 应用模糊
            blurred = F.conv2d(
                enhanced.unsqueeze(0),
                kernel.unsqueeze(0).unsqueeze(0),
                padding=kernel_size//2
            ).squeeze(0)

            # 范围保持的Unsharp masking
            unsharp_result = enhanced + 0.3 * (enhanced - blurred)
            enhanced = torch.clamp(unsharp_result, -1.0, 1.0)

        # 2. 高频细节增强
        if random.random() < 0.4:
            # 拉普拉斯锐化
            laplacian_kernel = torch.tensor([[[
                [0, -1, 0],
                [-1, 5, -1],
                [0, -1, 0]
            ]]], dtype=enhanced.dtype, device=enhanced.device)

            sharpened = F.conv2d(
                enhanced.unsqueeze(0),
                laplacian_kernel,
                padding=1
            ).squeeze(0)

            # 范围保持的混合原图和锐化图
            sharpened_result = 0.8 * enhanced + 0.2 * sharpened
            enhanced = torch.clamp(sharpened_result, -1.0, 1.0)

        # 3. 随机旋转 - 小角度
        if random.random() < 0.3:
            angle = random.uniform(-3, 3)
            enhanced = TF.rotate(
                enhanced,
                angle,
                interpolation=TF.InterpolationMode.BILINEAR,
                fill=0
            )

        # 4. 随机裁剪和调整 - 增加多样性
        if random.random() < 0.2:
            h, w = enhanced.shape[1:]
            crop_size = int(min(h, w) * random.uniform(0.9, 0.98))

            # 随机裁剪位置
            top = random.randint(0, h - crop_size)
            left = random.randint(0, w - crop_size)

            cropped = enhanced[:, top:top+crop_size, left:left+crop_size]

            # 调整回原始大小
            enhanced = F.interpolate(
                cropped.unsqueeze(0),
                size=(h, w),
                mode='bilinear',
                align_corners=False
            ).squeeze(0)

        return torch.clamp(enhanced, -1, 1)

    def _gaussian_kernel(self, size: int, sigma: float) -> torch.Tensor:
        """生成高斯核"""
        coords = torch.arange(size, dtype=torch.float32)
        coords -= size // 2

        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g /= g.sum()

        return g.outer(g)

    def __call__(self, image: torch.Tensor) -> torch.Tensor:
        """
        应用通道特定增强

        Args:
            image: 输入图像 [3, H, W]
        Returns:
            增强后的图像 [3, H, W]
        """
        # OPTIMIZATION 3.1: Remove global probability check to ensure augmentation is applied
        # The probability is now handled at the individual channel level

        # 分离通道
        channels = [image[i:i+1] for i in range(3)]

        # 对每个通道应用特定增强
        enhancers = [
            self.enhance_period_dm,
            self.enhance_phase_subband,
            self.enhance_phase_subintegration
        ]

        enhanced_channels = []
        for i, (channel, enhancer) in enumerate(zip(channels, enhancers)):
            # Each enhancer has its own probability check inside
            enhanced_channels.append(enhancer(channel))

        # 合并通道
        enhanced_image = torch.cat(enhanced_channels, dim=0)

        # OPTIMIZATION 3.3: Apply physical constraint validation
        validation_passed = True
        if self.enable_physical_validation:
            validation_passed = self.validate_physical_constraints(image, enhanced_image)
            if not validation_passed:
                # If validation fails, return original image
                self.validation_failure_count += 1
                return image

        # OPTIMIZATION 3.4: Update adaptive probability based on validation results
        if self.adaptive_probability:
            self.total_augmentation_count += 1
            self._update_adaptive_probability(validation_passed)

        return enhanced_image

    # OPTIMIZATION 3.2: Method to update augmentation intensity
    def update_intensity(self, current_epoch: int, total_epochs: int):
        """Update augmentation intensity based on training progress"""
        if self.progressive_intensity:
            # Progressive intensity: start with min_intensity, gradually increase to max_intensity
            progress = min(1.0, current_epoch / (total_epochs * 0.6))  # Reach max intensity at 60% of training
            self.intensity = self.min_intensity + (self.max_intensity - self.min_intensity) * progress

        return self.intensity

    # OPTIMIZATION 3.3: Physical constraint validation methods
    def validate_peak_preservation(self, original: torch.Tensor, enhanced: torch.Tensor) -> bool:
        """验证Period-DM通道的峰值保留"""
        # 检测原始图像中的峰值
        original_mean = original.mean()
        original_std = original.std()
        peak_threshold = original_mean + 1.5 * original_std

        original_peaks = (original > peak_threshold).float().sum()
        enhanced_peaks = (enhanced > peak_threshold).float().sum()

        if original_peaks == 0:
            return True  # 没有峰值，无需验证

        # 峰值保留率
        preservation_ratio = enhanced_peaks / original_peaks
        return preservation_ratio >= self.peak_preservation_threshold

    def validate_stripe_coherence(self, original: torch.Tensor, enhanced: torch.Tensor) -> bool:
        """验证Phase-Subband通道的条纹一致性"""
        # 计算垂直方向的方差（条纹应该在垂直方向上有较大变化）
        original_vertical_var = original.var(dim=1).mean()  # 沿高度方向的方差
        enhanced_vertical_var = enhanced.var(dim=1).mean()

        if original_vertical_var == 0:
            return True  # 没有条纹结构，无需验证

        # 条纹一致性比率
        coherence_ratio = enhanced_vertical_var / original_vertical_var
        return coherence_ratio >= self.stripe_coherence_threshold

    def validate_detail_variance(self, original: torch.Tensor, enhanced: torch.Tensor) -> bool:
        """验证Phase-Subintegration通道的细节方差"""
        # 计算高频细节的方差
        laplacian_kernel = torch.tensor([[[[0, -1, 0], [-1, 4, -1], [0, -1, 0]]]],
                                       dtype=original.dtype, device=original.device)

        original_detail = F.conv2d(original, laplacian_kernel, padding=1)
        enhanced_detail = F.conv2d(enhanced, laplacian_kernel, padding=1)

        original_detail_var = original_detail.var()
        enhanced_detail_var = enhanced_detail.var()

        if original_detail_var == 0:
            return True  # 没有细节，无需验证

        # 细节保留比率
        detail_ratio = enhanced_detail_var / original_detail_var
        return detail_ratio >= self.detail_variance_threshold

    def validate_physical_constraints(self, original: torch.Tensor, enhanced: torch.Tensor) -> bool:
        """验证增强后图像的物理约束"""
        if not self.enable_physical_validation:
            return True

        # 分离通道进行验证
        original_channels = [original[i:i+1] for i in range(3)]
        enhanced_channels = [enhanced[i:i+1] for i in range(3)]

        # 验证Period-DM通道的峰值保留
        if not self.validate_peak_preservation(original_channels[0], enhanced_channels[0]):
            return False

        # 验证Phase-Subband通道的条纹一致性
        if not self.validate_stripe_coherence(original_channels[1], enhanced_channels[1]):
            return False

        # 验证Phase-Subintegration通道的细节方差
        if not self.validate_detail_variance(original_channels[2], enhanced_channels[2]):
            return False

        return True

    # OPTIMIZATION 3.4: Adaptive augmentation probability methods
    def _update_adaptive_probability(self, validation_passed: bool):
        """Update augmentation probability based on validation results and training progress"""
        if not self.adaptive_probability:
            return

        # Track validation success rate
        validation_success_rate = 1.0 - (self.validation_failure_count / max(1, self.total_augmentation_count))

        # Adjust probability based on validation success rate
        if validation_success_rate > 0.8:
            # High success rate: can increase augmentation probability
            self.prob = min(self.max_prob, self.prob + self.prob_adjustment_factor * 0.1)
        elif validation_success_rate < 0.6:
            # Low success rate: should decrease augmentation probability
            self.prob = max(self.min_prob, self.prob - self.prob_adjustment_factor * 0.2)

        # Ensure probability stays within bounds
        self.prob = max(self.min_prob, min(self.max_prob, self.prob))

    def update_adaptive_probability_with_loss(self, current_loss: float):
        """Update augmentation probability based on training loss"""
        if not self.adaptive_probability:
            return self.prob

        # Add current loss to history
        self.loss_history.append(current_loss)

        # Keep only recent history
        if len(self.loss_history) > self.loss_history_window:
            self.loss_history.pop(0)

        # Calculate loss trend if we have enough history
        if len(self.loss_history) >= 3:
            recent_avg = sum(self.loss_history[-3:]) / 3
            older_avg = sum(self.loss_history[:-3]) / max(1, len(self.loss_history) - 3)

            # If loss is decreasing (improving), can increase augmentation
            if recent_avg < older_avg * 0.95:  # 5% improvement threshold
                self.prob = min(self.max_prob, self.prob + self.prob_adjustment_factor * 0.05)
            # If loss is increasing (getting worse), decrease augmentation
            elif recent_avg > older_avg * 1.05:  # 5% degradation threshold
                self.prob = max(self.min_prob, self.prob - self.prob_adjustment_factor * 0.1)

        return self.prob

    def get_adaptive_probability_stats(self) -> dict:
        """Get statistics about adaptive probability adjustment"""
        if not self.adaptive_probability:
            return {'adaptive_enabled': False}

        validation_success_rate = 1.0 - (self.validation_failure_count / max(1, self.total_augmentation_count))

        return {
            'adaptive_enabled': True,
            'current_prob': self.prob,
            'base_prob': self.base_prob,
            'validation_success_rate': validation_success_rate,
            'validation_failures': self.validation_failure_count,
            'total_augmentations': self.total_augmentation_count,
            'loss_history_length': len(self.loss_history),
            'recent_avg_loss': sum(self.loss_history[-3:]) / min(3, len(self.loss_history)) if self.loss_history else 0.0
        }

    def reset_adaptive_stats(self):
        """Reset adaptive probability statistics"""
        self.loss_history = []
        self.validation_failure_count = 0
        self.total_augmentation_count = 0
        self.prob = self.base_prob


# OPTIMIZATION 2.3: Lightweight Perceptual Loss Network for Pulsar Data
class PulsarPerceptualNetwork(nn.Module):
    """
    轻量级感知网络，专门为脉冲星数据设计
    避免使用大型预训练网络，减少内存占用
    """

    def __init__(self, input_channels=3):
        super().__init__()

        # 轻量级特征提取器
        self.features = nn.Sequential(
            # 第一层：基础特征
            nn.Conv2d(input_channels, 32, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 32, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 16x16

            # 第二层：中级特征
            nn.Conv2d(32, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 8x8

            # 第三层：高级特征
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 4x4
        )

        # 冻结参数，作为固定特征提取器
        for param in self.parameters():
            param.requires_grad = False

    def forward(self, x):
        """提取多层特征"""
        features = []

        # 逐层提取特征
        for i, layer in enumerate(self.features):
            x = layer(x)
            # 在池化层后保存特征
            if isinstance(layer, nn.MaxPool2d):
                features.append(x)

        return features


# OPTIMIZATION 2.4: Lightweight Adversarial Loss for Pulsar Data
class PulsarLightweightDiscriminator(nn.Module):
    """
    轻量级判别器，专门为脉冲星数据设计
    用于提供对抗损失，提高生成质量
    """

    def __init__(self, input_channels=3, base_channels=32):
        super().__init__()

        # 轻量级判别器网络
        self.discriminator = nn.Sequential(
            # 第一层：32x32 -> 16x16
            nn.Conv2d(input_channels, base_channels, 4, 2, 1),
            nn.LeakyReLU(0.2, inplace=True),

            # 第二层：16x16 -> 8x8
            nn.Conv2d(base_channels, base_channels * 2, 4, 2, 1),
            nn.BatchNorm2d(base_channels * 2),
            nn.LeakyReLU(0.2, inplace=True),

            # 第三层：8x8 -> 4x4
            nn.Conv2d(base_channels * 2, base_channels * 4, 4, 2, 1),
            nn.BatchNorm2d(base_channels * 4),
            nn.LeakyReLU(0.2, inplace=True),

            # 第四层：4x4 -> 2x2
            nn.Conv2d(base_channels * 4, base_channels * 8, 4, 2, 1),
            nn.BatchNorm2d(base_channels * 8),
            nn.LeakyReLU(0.2, inplace=True),

            # 输出层：2x2 -> 1x1
            nn.Conv2d(base_channels * 8, 1, 2, 1, 0),
            nn.Sigmoid()
        )

        # 冻结参数，作为固定判别器
        for param in self.parameters():
            param.requires_grad = False

    def forward(self, x):
        """判别输入是否为真实数据"""
        return self.discriminator(x).view(-1)


class ChannelWeightedLoss:
    """
    通道权重损失函数 - OPTIMIZATION 2.2: 增强物理特征保留损失
    为不同通道分配不同的损失权重，并添加脉冲星物理特征保留损失
    """

    def __init__(self,
                 channel_weights: List[float] = [0.4, 0.35, 0.25],
                 adaptive_weights: bool = True,
                 # OPTIMIZATION 2.2: Physical feature preservation parameters
                 use_physical_loss: bool = True,
                 peak_loss_weight: float = 0.15,
                 stripe_loss_weight: float = 0.12,
                 detail_loss_weight: float = 0.10,
                 edge_loss_weight: float = 0.08,
                 texture_loss_weight: float = 0.05,
                 # OPTIMIZATION 2.3: Perceptual loss parameters
                 use_perceptual_loss: bool = True,
                 perceptual_loss_weight: float = 0.1,
                 perceptual_layers: List[int] = [0, 1, 2],
                 # OPTIMIZATION 2.4: Adversarial loss parameters
                 use_adversarial_loss: bool = True,
                 adversarial_loss_weight: float = 0.05,
                 discriminator_base_channels: int = 32):
        """
        Args:
            channel_weights: 各通道的基础权重 [Period-DM, Phase-Subband, Phase-Subintegration]
            adaptive_weights: 是否使用自适应权重调整
            use_physical_loss: 是否使用物理特征保留损失
            peak_loss_weight: 峰值保留损失权重
            stripe_loss_weight: 条纹结构损失权重
            detail_loss_weight: 细节保留损失权重
            edge_loss_weight: 边缘保留损失权重
            texture_loss_weight: 纹理保留损失权重
        """
        self.base_weights = torch.tensor(channel_weights)
        self.adaptive_weights = adaptive_weights
        self.weight_history = []

        # OPTIMIZATION 2.2: Physical feature preservation settings
        self.use_physical_loss = use_physical_loss
        self.peak_loss_weight = peak_loss_weight
        self.stripe_loss_weight = stripe_loss_weight
        self.detail_loss_weight = detail_loss_weight
        self.edge_loss_weight = edge_loss_weight
        self.texture_loss_weight = texture_loss_weight

        # OPTIMIZATION 2.3: Perceptual loss settings
        self.use_perceptual_loss = use_perceptual_loss
        self.perceptual_loss_weight = perceptual_loss_weight
        self.perceptual_layers = perceptual_layers
        self.perceptual_network = None

        if self.use_perceptual_loss:
            self.perceptual_network = PulsarPerceptualNetwork(input_channels=3)
            # 初始化网络权重（简单的随机初始化）
            self._initialize_perceptual_network()

        # OPTIMIZATION 2.4: Adversarial loss settings
        self.use_adversarial_loss = use_adversarial_loss
        self.adversarial_loss_weight = adversarial_loss_weight
        self.discriminator = None

        if self.use_adversarial_loss:
            self.discriminator = PulsarLightweightDiscriminator(
                input_channels=3,
                base_channels=discriminator_base_channels
            )
            # 初始化判别器权重
            self._initialize_discriminator()

    def compute_channel_losses(self,
                             pred: torch.Tensor,
                             target: torch.Tensor) -> torch.Tensor:
        """
        计算各通道的损失 - OPTIMIZATION 2.2: 增强物理特征保留

        Args:
            pred: 预测图像 [B, 3, H, W]
            target: 目标图像 [B, 3, H, W]
        Returns:
            各通道损失 [3]
        """
        channel_losses = []

        for i in range(3):
            pred_channel = pred[:, i:i+1]
            target_channel = target[:, i:i+1]

            # 基础MSE损失
            mse_loss = F.mse_loss(pred_channel, target_channel)
            total_loss = mse_loss

            if self.use_physical_loss:
                # OPTIMIZATION 2.2: 通道特定的物理特征保留损失
                if i == 0:  # Period-DM: 峰值和边缘保留
                    peak_loss = self._peak_preservation_loss(pred_channel, target_channel)
                    edge_loss = self._edge_preservation_loss(pred_channel, target_channel)
                    total_loss = mse_loss + self.peak_loss_weight * peak_loss + self.edge_loss_weight * edge_loss

                elif i == 1:  # Phase-Subband: 条纹结构和纹理保留
                    stripe_loss = self._stripe_structure_loss(pred_channel, target_channel)
                    texture_loss = self._texture_preservation_loss(pred_channel, target_channel)
                    total_loss = mse_loss + self.stripe_loss_weight * stripe_loss + self.texture_loss_weight * texture_loss

                else:  # Phase-Subintegration: 细节和高频保留
                    detail_loss = self._detail_preservation_loss(pred_channel, target_channel)
                    high_freq_loss = self._high_frequency_loss(pred_channel, target_channel)
                    total_loss = mse_loss + self.detail_loss_weight * detail_loss + self.texture_loss_weight * high_freq_loss
            else:
                # 原始的简化物理损失
                if i == 0:  # Period-DM: 峰值保留损失
                    peak_loss = self._peak_preservation_loss(pred_channel, target_channel)
                    total_loss = mse_loss + 0.1 * peak_loss
                elif i == 1:  # Phase-Subband: 条纹结构损失
                    stripe_loss = self._stripe_structure_loss(pred_channel, target_channel)
                    total_loss = mse_loss + 0.1 * stripe_loss
                else:  # Phase-Subintegration: 细节保留损失
                    detail_loss = self._detail_preservation_loss(pred_channel, target_channel)
                    total_loss = mse_loss + 0.1 * detail_loss

            channel_losses.append(total_loss)

        return torch.stack(channel_losses)

    def _peak_preservation_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """峰值保留损失 - 确保峰值区域得到正确重建"""
        # 识别峰值区域（高于均值+标准差的区域）
        target_mean = target.mean()
        target_std = target.std()
        peak_mask = target > (target_mean + 0.5 * target_std)

        if peak_mask.sum() > 0:
            peak_loss = F.mse_loss(pred[peak_mask], target[peak_mask])
        else:
            peak_loss = torch.tensor(0.0, device=pred.device)

        return peak_loss

    def _stripe_structure_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """条纹结构损失 - 确保垂直条纹结构得到保留"""
        # 计算水平方向梯度（垂直条纹的特征）
        pred_grad = torch.diff(pred, dim=3)
        target_grad = torch.diff(target, dim=3)

        # 梯度损失
        grad_loss = F.mse_loss(pred_grad, target_grad)

        return grad_loss

    def _detail_preservation_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """细节保留损失 - 确保高频细节得到保留"""
        # 使用拉普拉斯算子检测细节
        laplacian_kernel = torch.tensor([[[[0, -1, 0], [-1, 4, -1], [0, -1, 0]]]],
                                       dtype=pred.dtype, device=pred.device)

        pred_detail = F.conv2d(pred, laplacian_kernel, padding=1)
        target_detail = F.conv2d(target, laplacian_kernel, padding=1)

        detail_loss = F.mse_loss(pred_detail, target_detail)

        return detail_loss

    # OPTIMIZATION 2.2: 新增物理特征保留损失函数
    def _edge_preservation_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """边缘保留损失 - 使用Sobel算子检测边缘特征"""
        # Sobel X 算子
        sobel_x = torch.tensor([[[[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]]],
                              dtype=pred.dtype, device=pred.device)
        # Sobel Y 算子
        sobel_y = torch.tensor([[[[-1, -2, -1], [0, 0, 0], [1, 2, 1]]]],
                              dtype=pred.dtype, device=pred.device)

        # 计算预测图像的边缘
        pred_edge_x = F.conv2d(pred, sobel_x, padding=1)
        pred_edge_y = F.conv2d(pred, sobel_y, padding=1)
        pred_edge = torch.sqrt(pred_edge_x**2 + pred_edge_y**2 + 1e-8)

        # 计算目标图像的边缘
        target_edge_x = F.conv2d(target, sobel_x, padding=1)
        target_edge_y = F.conv2d(target, sobel_y, padding=1)
        target_edge = torch.sqrt(target_edge_x**2 + target_edge_y**2 + 1e-8)

        # 边缘损失
        edge_loss = F.mse_loss(pred_edge, target_edge)

        return edge_loss

    def _texture_preservation_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """纹理保留损失 - 使用局部二值模式(LBP)风格的纹理特征"""
        # 简化的纹理检测：使用多个方向的梯度
        kernels = [
            torch.tensor([[[[-1, 0, 1], [-1, 0, 1], [-1, 0, 1]]]]),  # 垂直纹理
            torch.tensor([[[[-1, -1, -1], [0, 0, 0], [1, 1, 1]]]]),  # 水平纹理
            torch.tensor([[[[-1, 0, 0], [0, 0, 0], [0, 0, 1]]]]),    # 对角纹理1
            torch.tensor([[[[0, 0, 1], [0, 0, 0], [-1, 0, 0]]]]),    # 对角纹理2
        ]

        texture_loss = 0.0
        for kernel in kernels:
            kernel = kernel.to(pred.dtype).to(pred.device)

            pred_texture = F.conv2d(pred, kernel, padding=1)
            target_texture = F.conv2d(target, kernel, padding=1)

            texture_loss += F.mse_loss(pred_texture, target_texture)

        return texture_loss / len(kernels)

    def _high_frequency_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """高频损失 - 使用高通滤波器检测高频成分"""
        # 高通滤波器核
        high_pass_kernel = torch.tensor([[[[0, -1, 0], [-1, 5, -1], [0, -1, 0]]]],
                                       dtype=pred.dtype, device=pred.device)

        # 计算高频成分
        pred_high_freq = F.conv2d(pred, high_pass_kernel, padding=1)
        target_high_freq = F.conv2d(target, high_pass_kernel, padding=1)

        # 高频损失
        high_freq_loss = F.mse_loss(pred_high_freq, target_high_freq)

        return high_freq_loss

    def _spectral_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """频谱损失 - 在频域中比较图像特征"""
        # 计算2D FFT
        pred_fft = torch.fft.fft2(pred)
        target_fft = torch.fft.fft2(target)

        # 计算幅度谱
        pred_magnitude = torch.abs(pred_fft)
        target_magnitude = torch.abs(target_fft)

        # 频谱损失
        spectral_loss = F.mse_loss(pred_magnitude, target_magnitude)

        return spectral_loss

    def _phase_coherence_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """相位一致性损失 - 特别适用于Phase通道"""
        # 计算局部相位一致性
        # 使用多个尺度的Gabor滤波器
        scales = [2, 4, 8]
        orientations = [0, 45, 90, 135]

        phase_loss = 0.0
        count = 0

        for scale in scales:
            for orientation in orientations:
                # 简化的Gabor滤波器（使用高斯导数近似）
                angle_rad = orientation * 3.14159 / 180

                # 创建方向导数核
                if orientation == 0:  # 水平
                    kernel = torch.tensor([[[[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]]],
                                         dtype=pred.dtype, device=pred.device) / scale
                elif orientation == 90:  # 垂直
                    kernel = torch.tensor([[[[-1, -2, -1], [0, 0, 0], [1, 2, 1]]]],
                                         dtype=pred.dtype, device=pred.device) / scale
                elif orientation == 45:  # 对角1
                    kernel = torch.tensor([[[[0, 1, 2], [-1, 0, 1], [-2, -1, 0]]]],
                                         dtype=pred.dtype, device=pred.device) / scale
                else:  # 对角2
                    kernel = torch.tensor([[[[2, 1, 0], [1, 0, -1], [0, -1, -2]]]],
                                         dtype=pred.dtype, device=pred.device) / scale

                pred_response = F.conv2d(pred, kernel, padding=1)
                target_response = F.conv2d(target, kernel, padding=1)

                phase_loss += F.mse_loss(pred_response, target_response)
                count += 1

        return phase_loss / count if count > 0 else torch.tensor(0.0, device=pred.device)

    # OPTIMIZATION 2.3: Perceptual loss methods
    def _initialize_perceptual_network(self):
        """初始化感知网络权重"""
        if self.perceptual_network is None:
            return

        # 简单的Xavier初始化
        for module in self.perceptual_network.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)

    def _perceptual_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算感知损失"""
        if not self.use_perceptual_loss or self.perceptual_network is None:
            return torch.tensor(0.0, device=pred.device)

        # 确保输入在正确的设备上
        if self.perceptual_network.features[0].weight.device != pred.device:
            self.perceptual_network = self.perceptual_network.to(pred.device)

        # 提取特征
        with torch.no_grad():
            pred_features = self.perceptual_network(pred)
            target_features = self.perceptual_network(target)

        # 计算各层特征的损失
        perceptual_loss = 0.0
        for i, layer_idx in enumerate(self.perceptual_layers):
            if layer_idx < len(pred_features):
                pred_feat = pred_features[layer_idx]
                target_feat = target_features[layer_idx]

                # 归一化特征以避免数值不稳定
                pred_feat = F.normalize(pred_feat, p=2, dim=1)
                target_feat = F.normalize(target_feat, p=2, dim=1)

                # 计算特征损失
                feat_loss = F.mse_loss(pred_feat, target_feat)
                perceptual_loss += feat_loss

        return perceptual_loss / len(self.perceptual_layers) if self.perceptual_layers else torch.tensor(0.0, device=pred.device)

    # OPTIMIZATION 2.4: Adversarial loss methods
    def _initialize_discriminator(self):
        """初始化判别器权重"""
        if self.discriminator is None:
            return

        # 使用Xavier初始化
        for module in self.discriminator.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.BatchNorm2d):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)

    def _adversarial_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算对抗损失"""
        if not self.use_adversarial_loss or self.discriminator is None:
            return torch.tensor(0.0, device=pred.device)

        # 确保判别器在正确的设备上
        if next(self.discriminator.parameters()).device != pred.device:
            self.discriminator = self.discriminator.to(pred.device)

        # 计算对抗损失
        with torch.no_grad():
            # 判别器对真实数据的输出（应该接近1）
            real_scores = self.discriminator(target)

            # 判别器对生成数据的输出（我们希望它接近1，即欺骗判别器）
            fake_scores = self.discriminator(pred)

        # 生成器的对抗损失：希望判别器认为生成的数据是真实的
        # 使用二元交叉熵损失的简化形式
        adversarial_loss = -torch.log(fake_scores + 1e-8).mean()

        return adversarial_loss

    def _feature_matching_adversarial_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """基于特征匹配的对抗损失"""
        if not self.use_adversarial_loss or self.discriminator is None:
            return torch.tensor(0.0, device=pred.device)

        # 确保判别器在正确的设备上
        if next(self.discriminator.parameters()).device != pred.device:
            self.discriminator = self.discriminator.to(pred.device)

        # 提取中间特征进行匹配
        def get_intermediate_features(x):
            features = []
            for i, layer in enumerate(self.discriminator.discriminator):
                x = layer(x)
                # 在每个卷积层后保存特征
                if isinstance(layer, nn.Conv2d) and i < len(self.discriminator.discriminator) - 1:
                    features.append(x)
            return features

        with torch.no_grad():
            real_features = get_intermediate_features(target)
            fake_features = get_intermediate_features(pred)

        # 计算特征匹配损失
        feature_loss = 0.0
        for real_feat, fake_feat in zip(real_features, fake_features):
            feature_loss += F.mse_loss(fake_feat, real_feat)

        return feature_loss / len(real_features) if real_features else torch.tensor(0.0, device=pred.device)

    def __call__(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算加权损失

        Args:
            pred: 预测图像 [B, 3, H, W]
            target: 目标图像 [B, 3, H, W]
        Returns:
            加权总损失
        """
        # 计算各通道损失
        channel_losses = self.compute_channel_losses(pred, target)

        # 获取当前权重
        weights = self.base_weights.to(pred.device)

        # 自适应权重调整
        if self.adaptive_weights and len(self.weight_history) > 10:
            # 基于历史损失调整权重
            recent_losses = torch.stack(self.weight_history[-10:])
            avg_losses = recent_losses.mean(dim=0)

            # 损失高的通道获得更高权重
            adaptive_factor = avg_losses / avg_losses.mean()
            weights = weights * (0.8 + 0.4 * adaptive_factor)
            weights = weights / weights.sum() * weights.sum()  # 归一化

        # 记录当前损失
        self.weight_history.append(channel_losses.detach())
        if len(self.weight_history) > 50:
            self.weight_history.pop(0)

        # 计算加权损失
        weighted_loss = (weights * channel_losses).sum()

        # OPTIMIZATION 2.3: 添加感知损失
        if self.use_perceptual_loss:
            perceptual_loss = self._perceptual_loss(pred, target)
            weighted_loss = weighted_loss + self.perceptual_loss_weight * perceptual_loss

        # OPTIMIZATION 2.4: 添加对抗损失
        if self.use_adversarial_loss:
            # 使用特征匹配对抗损失，更稳定
            adversarial_loss = self._feature_matching_adversarial_loss(pred, target)
            weighted_loss = weighted_loss + self.adversarial_loss_weight * adversarial_loss

        return weighted_loss


class ProgressiveChannelTraining:
    """
    渐进式通道训练策略
    分阶段训练不同通道的特征
    """

    def __init__(self, total_epochs: int = 2000):
        """
        Args:
            total_epochs: 总训练轮数
        """
        self.total_epochs = total_epochs
        self.stage_transitions = [
            int(total_epochs * 0.3),   # 600 epochs: Period-DM主导
            int(total_epochs * 0.6),   # 1200 epochs: 添加Phase-Subband
            total_epochs               # 2000 epochs: 全通道训练
        ]

    def get_channel_weights(self, epoch: int) -> List[float]:
        """
        根据当前epoch获取通道权重

        Args:
            epoch: 当前训练轮数
        Returns:
            通道权重列表 [Period-DM, Phase-Subband, Phase-Subintegration]
        """
        if epoch < self.stage_transitions[0]:
            # 阶段1: 主要训练Period-DM通道
            return [0.7, 0.2, 0.1]
        elif epoch < self.stage_transitions[1]:
            # 阶段2: 增加Phase-Subband通道训练
            progress = (epoch - self.stage_transitions[0]) / (self.stage_transitions[1] - self.stage_transitions[0])
            period_weight = 0.7 - 0.25 * progress  # 0.7 -> 0.45
            subband_weight = 0.2 + 0.15 * progress  # 0.2 -> 0.35
            subint_weight = 0.1 + 0.1 * progress   # 0.1 -> 0.2
            return [period_weight, subband_weight, subint_weight]
        else:
            # 阶段3: 全通道联合训练
            return [0.4, 0.35, 0.25]

    def get_augmentation_prob(self, epoch: int) -> float:
        """
        根据训练阶段调整数据增强概率

        Args:
            epoch: 当前训练轮数
        Returns:
            数据增强概率
        """
        if epoch < self.stage_transitions[0]:
            return 0.5  # 早期较少增强
        elif epoch < self.stage_transitions[1]:
            return 0.7  # 中期增加增强
        else:
            return 0.8  # 后期最大增强


class ChannelSpecificPostProcessor:
    """
    通道特定后处理器
    在生成后对各通道进行特定的后处理优化
    """

    def __init__(self):
        self.period_dm_enhancer = self._create_period_dm_enhancer()
        self.phase_subband_enhancer = self._create_phase_subband_enhancer()
        self.phase_subint_enhancer = self._create_phase_subint_enhancer()

    def _create_period_dm_enhancer(self):
        """创建Period-DM通道增强器"""
        def enhance(channel):
            # 峰值锐化
            mean_val = channel.mean()
            std_val = channel.std()

            # 增强高于均值的区域
            mask = channel > mean_val
            enhanced = channel.clone()
            enhanced[mask] = torch.clamp(
                mean_val + (channel[mask] - mean_val) * 1.1,
                -1, 1
            )

            return enhanced
        return enhance

    def _create_phase_subband_enhancer(self):
        """创建Phase-Subband通道增强器"""
        def enhance(channel):
            # 条纹对比度增强
            grad_x = torch.diff(channel, dim=2, prepend=channel[:, :, :1])
            high_grad_mask = torch.abs(grad_x) > grad_x.std() * 0.5

            enhanced = channel.clone()
            enhanced[high_grad_mask] = torch.clamp(
                enhanced[high_grad_mask] * 1.05,
                -1, 1
            )

            return enhanced
        return enhance

    def _create_phase_subint_enhancer(self):
        """创建Phase-Subintegration通道增强器"""
        def enhance(channel):
            # 细节锐化
            laplacian_kernel = torch.tensor([[[
                [0, -1, 0],
                [-1, 5, -1],
                [0, -1, 0]
            ]]], dtype=channel.dtype, device=channel.device)

            sharpened = F.conv2d(
                channel.unsqueeze(0),
                laplacian_kernel,
                padding=1
            ).squeeze(0)

            # 轻微混合
            enhanced = 0.95 * channel + 0.05 * sharpened
            return torch.clamp(enhanced, -1, 1)
        return enhance

    def process(self, generated_images: torch.Tensor) -> torch.Tensor:
        """
        对生成的图像进行通道特定后处理

        Args:
            generated_images: 生成的图像 [B, 3, H, W]
        Returns:
            后处理后的图像 [B, 3, H, W]
        """
        batch_size = generated_images.shape[0]
        processed_images = []

        for i in range(batch_size):
            image = generated_images[i]

            # 分离通道
            period_dm = image[0:1]
            phase_subband = image[1:2]
            phase_subint = image[2:3]

            # 应用通道特定增强
            enhanced_period_dm = self.period_dm_enhancer(period_dm)
            enhanced_phase_subband = self.phase_subband_enhancer(phase_subband)
            enhanced_phase_subint = self.phase_subint_enhancer(phase_subint)

            # 合并通道
            processed_image = torch.cat([
                enhanced_period_dm,
                enhanced_phase_subband,
                enhanced_phase_subint
            ], dim=0)

            processed_images.append(processed_image)

        return torch.stack(processed_images)


def create_integrated_training_strategy(total_epochs: int = 2000) -> Dict:
    """
    创建集成的训练策略

    Args:
        total_epochs: 总训练轮数
    Returns:
        包含所有策略组件的字典
    """
    return {
        'augmenter': ChannelSpecificAugmenter(prob=0.7),
        'loss_function': ChannelWeightedLoss(
            channel_weights=[0.4, 0.35, 0.25],
            adaptive_weights=True
        ),
        'progressive_trainer': ProgressiveChannelTraining(total_epochs),
        'post_processor': ChannelSpecificPostProcessor()
    }


# 使用示例
if __name__ == "__main__":
    # 创建策略组件
    strategy = create_integrated_training_strategy(2000)

    # 模拟数据
    batch_size = 8
    sample_image = torch.randn(3, 32, 32)
    sample_batch = torch.randn(batch_size, 3, 32, 32)

    print("替代通道处理策略测试")
    print("=" * 40)

    # 测试数据增强
    print("1. 测试通道特定数据增强...")
    augmented = strategy['augmenter'](sample_image)
    print(f"   原始图像形状: {sample_image.shape}")
    print(f"   增强后形状: {augmented.shape}")
    print(f"   数值范围: [{augmented.min():.3f}, {augmented.max():.3f}]")

    # 测试损失函数
    print("\n2. 测试通道权重损失...")
    pred = torch.randn(batch_size, 3, 32, 32)
    target = torch.randn(batch_size, 3, 32, 32)
    loss = strategy['loss_function'](pred, target)
    print(f"   加权损失值: {loss.item():.6f}")

    # 测试渐进式训练
    print("\n3. 测试渐进式训练策略...")
    for epoch in [100, 800, 1500]:
        weights = strategy['progressive_trainer'].get_channel_weights(epoch)
        aug_prob = strategy['progressive_trainer'].get_augmentation_prob(epoch)
        print(f"   Epoch {epoch}: 权重={weights}, 增强概率={aug_prob}")

    # 测试后处理
    print("\n4. 测试通道特定后处理...")
    processed = strategy['post_processor'].process(sample_batch)
    print(f"   处理前形状: {sample_batch.shape}")
    print(f"   处理后形状: {processed.shape}")
    print(f"   处理后数值范围: [{processed.min():.3f}, {processed.max():.3f}]")

    print("\n✅ 所有策略组件测试完成！")
