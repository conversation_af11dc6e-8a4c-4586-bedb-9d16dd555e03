#!/usr/bin/env python3
"""
简化的训练测试脚本
用于验证修复后的训练流程
"""

import sys
import os
sys.path.insert(0, os.getcwd())

import torch
import time
from datetime import datetime

def test_basic_training():
    """测试基础训练功能"""
    print("🔍 测试基础训练功能")
    
    try:
        from training.full_trainer import TrainingConfig, create_training_pipeline
        from utils.random_seed import set_random_seed
        
        # 设置随机种子
        set_random_seed(42)
        print("✅ 随机种子设置完成")
        
        # 创建最小配置
        config = TrainingConfig(
            latent_dim=32,  # 更小的潜在维度
            batch_size=2,   # 最小批次
            device='cuda',
            random_seed=42,
            vae_lr=1e-4,
            discriminator_lr=2e-4,
            stage1_epochs=1,
            stage2_epochs=1,
            stage3_epochs=1,
            data_augment=False,
            augment_factor=1,
            num_workers=0,  # 禁用多进程
            save_dir='./test_checkpoints',
            save_interval=1000,  # 不保存
            log_interval=1
        )
        print("✅ 配置创建完成")
        
        # 创建训练管道
        pipeline = create_training_pipeline(config)
        print("✅ 训练管道创建完成")
        
        # 设置数据
        print("📊 设置数据...")
        pipeline.setup_data()
        print(f"✅ 数据设置完成: {len(pipeline.dataloader)} 批次")
        
        # 设置模型
        print("🧠 设置模型...")
        pipeline.setup_model()
        print(f"✅ 模型设置完成: {pipeline.model.total_params/1e6:.2f}M 参数")
        
        # 测试单个批次
        print("🧪 测试单个批次...")
        test_batch = next(iter(pipeline.dataloader))
        print(f"✅ 测试批次: {test_batch.shape}")
        
        # 测试阶段1 (VAE预训练)
        print("\n🎯 测试阶段1: VAE预训练")
        pipeline.trainer.set_stage(1)
        
        start_time = time.time()
        metrics1 = pipeline.trainer.train_step(test_batch)
        stage1_time = time.time() - start_time
        
        print(f"✅ 阶段1完成 ({stage1_time:.2f}s)")
        print(f"📊 指标数量: {len(metrics1)}")
        
        # 测试阶段2 (WGAN-GP集成) - 关键测试
        print("\n🎯 测试阶段2: WGAN-GP集成")
        pipeline.trainer.set_stage(2)
        
        start_time = time.time()
        metrics2 = pipeline.trainer.train_step(test_batch)
        stage2_time = time.time() - start_time
        
        print(f"✅ 阶段2完成 ({stage2_time:.2f}s)")
        print(f"📊 指标数量: {len(metrics2)}")
        
        # 测试阶段3 (联合优化)
        print("\n🎯 测试阶段3: 联合优化")
        pipeline.trainer.set_stage(3)
        
        start_time = time.time()
        metrics3 = pipeline.trainer.train_step(test_batch)
        stage3_time = time.time() - start_time
        
        print(f"✅ 阶段3完成 ({stage3_time:.2f}s)")
        print(f"📊 指标数量: {len(metrics3)}")
        
        # 总结
        total_time = stage1_time + stage2_time + stage3_time
        print(f"\n📈 性能总结:")
        print(f"  阶段1时间: {stage1_time:.2f}s")
        print(f"  阶段2时间: {stage2_time:.2f}s")
        print(f"  阶段3时间: {stage3_time:.2f}s")
        print(f"  总时间: {total_time:.2f}s")
        
        if stage2_time > 10:
            print("⚠️ 阶段2时间较长，可能仍有性能问题")
        else:
            print("✅ 所有阶段性能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mini_training():
    """测试迷你训练流程"""
    print("\n🔍 测试迷你训练流程")
    
    try:
        from training.full_trainer import TrainingConfig, create_training_pipeline
        from utils.random_seed import set_random_seed
        
        # 设置随机种子
        set_random_seed(42)
        
        # 创建迷你配置
        config = TrainingConfig(
            latent_dim=32,
            batch_size=2,
            device='cuda',
            random_seed=42,
            vae_lr=1e-4,
            discriminator_lr=2e-4,
            stage1_epochs=1,
            stage2_epochs=1,
            stage3_epochs=1,
            data_augment=False,
            augment_factor=1,
            num_workers=0,
            save_dir='./test_checkpoints',
            save_interval=1000,
            log_interval=1
        )
        
        # 创建并运行训练
        pipeline = create_training_pipeline(config)
        
        print("🚀 开始迷你训练...")
        start_time = time.time()
        
        training_history = pipeline.run_full_training()
        
        total_time = time.time() - start_time
        print(f"✅ 迷你训练完成 ({total_time:.2f}s)")
        
        # 检查训练历史
        for stage, history in training_history.items():
            print(f"  {stage}: {len(history)} epochs")
        
        return True
        
    except Exception as e:
        print(f"❌ 迷你训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 WGAN-GP+VAE训练修复验证")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"🖥️ GPU: {torch.cuda.get_device_name(0)}")
        torch.cuda.empty_cache()  # 清理GPU内存
    else:
        print("⚠️ 使用CPU训练")
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 基础训练功能
    if test_basic_training():
        success_count += 1
    
    # 测试2: 迷你训练流程
    if test_mini_training():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！训练冻结问题已修复")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit(main())
