# PulsarTransformerDDPM A100 GPU训练配置
# 基于阶段1-3优化结果的完整训练配置

# 数据配置 (Phase 1优化)
data:
  data_dir: "/Pulsar/PulsarFenlei/data/htru1-batches-py"
  batch_size: 32  # Phase 1优化: 64 → 32 (提高训练稳定性)
  num_workers: 16  # 多进程数据加载 (A100优化)
  pin_memory: true
  persistent_workers: true

# 模型配置 (Phase 1优化)
model:
  img_channels: 3  # Period-DM, Phase-Subband, Phase-Subintegration
  base_channels: 66  # Phase 1优化: 48→66 (能被3整除且为偶数)
  channel_mults: "1,2,3,4"  # 增强通道倍数
  num_res_blocks: 3  # 增强残差块数量
  transformer_dim: 120  # Phase 1优化: 96→120 (能被6整除)
  num_heads: 6  # Phase 1优化: 4→6 (与transformer_dim匹配)
  num_timesteps: 300  # Phase 1优化: 150 → 300 (增强生成质量)
  dropout: 0.05  # Phase 1优化: 0.1 → 0.05 (降低过拟合风险)

# 训练配置 (Phase 2紧急修复)
training:
  lr: 1.0e-5  # 基础学习率 (Phase 2修复: 2e-5 → 1e-5, 进一步降低)
  weight_decay: 1.0e-5  # 权重衰减
  max_epochs: 400  # 最大训练轮数
  warmup_epochs: 10  # 预热轮数 (Phase 1优化: 5 → 10)
  gradient_clip_val: 0.5  # 梯度裁剪
  mixed_precision: true  # 混合精度训练
  compile_model: true  # 模型编译 (PyTorch 2.0+)

# 三阶段训练策略 (Phase 2紧急修复)
progressive_training:
  stage_epochs: "150,150,100"  # basic, physics, fine_tune
  stage_lr_factors: "1.0,0.5,0.3"  # 学习率因子 (Phase 2修复: 0.8→0.5, 更激进的阶段2衰减)

  # 阶段描述
  stages:
    basic:
      description: "基础扩散学习阶段"
      epochs: 150
      lr_factor: 1.0
      physics_constraints: false

    physics:
      description: "物理约束强化阶段"
      epochs: 150
      lr_factor: 0.5
      physics_constraints: true

    fine_tune:
      description: "精细调优阶段"
      epochs: 100
      lr_factor: 0.1
      physics_constraints: reduced

# 物理约束损失配置 (Phase 2紧急修复)
physics_loss:
  mse_weight: 0.8  # MSE损失权重 (Phase 2修复: 0.7 → 0.8, 确保基础扩散稳定)
  consistency_weight: 0.1  # 通道一致性损失权重
  physics_weight: 0.05  # 物理特征损失权重 (Phase 2修复: 0.2 → 0.05, 避免损失暴增)
  spectral_weight: 0.0  # 频谱一致性损失权重 (Phase 2修复: 暂时禁用频谱损失避免数值爆炸)
  channel_diffusion_weight: 0.02  # 通道特定扩散损失权重 (Phase 2修复: 0.05 → 0.02, 降低通道扩散权重)

# 通道特定扩散配置
channel_diffusion:
  # 基于物理复杂度的噪声强度因子
  noise_factors:
    period_dm: 0.8  # Period-DM (最简单)
    phase_subband: 1.0  # Phase-Subband (中等复杂)
    phase_subintegration: 1.2  # Phase-Subintegration (最复杂)

  # 扩散速度因子
  speed_factors:
    period_dm: 0.9
    phase_subband: 1.0
    phase_subintegration: 1.1

  # 通道权重 (Phase 2修复: 基于实际性能表现重新分配)
  channel_weights:
    period_dm: 0.2  # Phase 2修复: 0.5 → 0.2 (降低最差通道权重)
    phase_subband: 0.3  # Phase 2修复: 保持中等权重
    phase_subintegration: 0.5  # Phase 2修复: 0.2 → 0.5 (提高最好通道权重)

# PulsarAdaptiveSampler配置 (替代DPM-Solver++)
pulsar_adaptive_sampler:
  enabled: true  # 启用PulsarAdaptiveSampler (默认替代DPM-Solver++)
  num_inference_steps: 20  # 推理步数 (优化后的步数)
  numerical_stability: true  # 启用数值稳定性保护
  pulsar_optimization: true  # 启用脉冲星特定优化
  channel_aware: true  # 启用通道感知处理

  # 高级配置
  advanced:
    adaptive_step_size: true  # 自适应步长
    noise_schedule_optimization: true  # 噪声调度优化
    memory_efficient: true  # 内存效率优化

  # 性能配置
  performance:
    target_speed: "15x"  # 相对1000步的加速目标
    quality_preservation: 0.95  # 质量保持率
    numerical_precision: "float32"  # 数值精度

# DDIM采样配置 (向后兼容)
ddim_sampling:
  num_inference_steps: 100  # Phase 1优化: 50 → 100 (提高生成质量)
  eta: 0.0  # 确定性采样
  speedup_target: "10x"  # 相对1000步的加速目标 (Phase 1优化: 更保守的加速)
  deprecated: true  # 标记为已弃用，推荐使用PulsarAdaptiveSampler

# 评估配置 (A100优化)
evaluation:
  eval_every: 50  # 评估间隔
  eval_num_samples: 995  # 评估样本数 (与训练集正样本数量一致)
  eval_batch_size: 100  # 评估批次大小 (A100优化)
  inception_model_path: "evaluation/inception_v3_google-0cc3c7bd.pth"

  # PulsarAdaptiveSampler评估配置
  sampler_config:
    use_pulsar_adaptive: true  # 使用PulsarAdaptiveSampler进行评估
    num_inference_steps: 20  # 评估时的推理步数
    numerical_stability: true  # 评估时启用数值稳定性
    pulsar_optimization: true  # 评估时启用脉冲星优化
    channel_aware: true  # 评估时启用通道感知

  # 性能目标 (基于PulsarAdaptiveSampler优化后的预期)
  targets:
    fid_score: 65  # 目标FID分数 (PulsarAdaptiveSampler优化后)
    fid_tolerance: 8  # FID容差范围
    is_score_min: 2.5  # IS分数最小值 (提高预期)
    is_score_max: 3.5  # IS分数最大值 (提高预期)

  # 早停配置
  early_stopping:
    patience: 3  # 连续3次评估无改善则停止
    min_delta: 0.01  # 最小改善阈值

# 输出配置
output:
  output_dir: "outputs_a100"
  save_every: 50  # 保存检查点间隔
  log_every: 10  # 日志记录间隔

  # 监控配置
  monitoring:
    enable_real_time: true
    refresh_interval: 300  # 5分钟刷新
    plot_generation: true

# A100 GPU优化配置
gpu_optimization:
  device: "cuda"
  mixed_precision: true
  gradient_checkpointing: false  # 内存充足时关闭
  pin_memory: true
  non_blocking: true

  # 内存优化 (A100优化)
  memory:
    empty_cache_every: 200  # 每200步清理缓存 (A100优化)
    max_memory_usage: 38  # 最大内存使用 (GB, A100优化)

  # 计算优化
  compute:
    torch_compile: true  # PyTorch 2.0编译
    cudnn_benchmark: true
    cudnn_deterministic: false

# 实验配置
experiment:
  name: "PulsarTransformerDDPM_A100"
  description: "三阶段物理约束训练 - A100 GPU优化"
  version: "v1.0"
  tags: ["pulsar", "transformer", "ddpm", "physics", "a100"]

  # 预期性能 (A100优化 + PulsarAdaptiveSampler)
  expected_performance:
    parameter_count: "~12M"  # 增强模型
    training_time_per_epoch: "~120s"  # A100加速
    pulsar_adaptive_sampling_time: "~0.5s (20步)"  # PulsarAdaptiveSampler加速
    ddim_sampling_time: "~0.8s (25步)"  # A100加速 (向后兼容)
    memory_usage: "~25GB"  # 增强模型内存使用
    target_fid: "60±8"  # PulsarAdaptiveSampler优化后的更好目标
    target_is: "3.5±0.5"  # PulsarAdaptiveSampler优化后的更好目标
    numerical_stability: "100%"  # 零NaN/Inf保证

# 随机种子
seed: 42

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_rotation: true
  max_file_size: "100MB"
  backup_count: 5
