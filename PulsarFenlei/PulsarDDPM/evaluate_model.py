#!/usr/bin/env python3
"""
WGAN-GP+VAE模型评估脚本

训练完成后的全面评估，包括：
1. FID/IS指标计算
2. 样本生成和可视化
3. 物理特征验证
4. 性能报告生成
"""

import sys
import os
sys.path.insert(0, os.getcwd())

import torch
import numpy as np
import matplotlib.pyplot as plt
import argparse
import json
from datetime import datetime
from pathlib import Path

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='WGAN-GP+VAE模型评估')
    
    parser.add_argument('--checkpoint', type=str, required=True, 
                       help='模型检查点路径')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results',
                       help='评估结果输出目录')
    parser.add_argument('--num_samples', type=int, default=995,
                       help='生成样本数量')
    parser.add_argument('--batch_size', type=int, default=32,
                       help='评估批次大小')
    parser.add_argument('--device', type=str, default='auto',
                       help='计算设备')
    parser.add_argument('--save_samples', action='store_true',
                       help='保存生成的样本')
    
    return parser.parse_args()

def load_model(checkpoint_path, device):
    """加载训练好的模型"""
    print(f"🔄 加载模型检查点: {checkpoint_path}")
    
    try:
        from models.wgan_vae import PulsarWGANVAE
        
        # 创建模型
        model = PulsarWGANVAE(latent_dim=64).to(device)
        
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location=device)
        
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model.eval()
        print(f"✅ 模型加载成功: {model.total_params/1e6:.2f}M 参数")
        
        return model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def load_real_data(batch_size, device):
    """加载真实数据用于对比"""
    print("📊 加载真实HTRU1数据...")
    
    try:
        from utils.data_loader import create_htru1_dataloader
        
        dataloader = create_htru1_dataloader(
            batch_size=batch_size,
            augment=False,
            num_workers=0
        )
        
        # 收集所有真实样本
        real_samples = []
        for batch in dataloader:
            real_samples.append(batch.to(device))
        
        real_data = torch.cat(real_samples, dim=0)
        print(f"✅ 真实数据加载完成: {real_data.shape}")
        
        return real_data
        
    except Exception as e:
        print(f"❌ 真实数据加载失败: {e}")
        return None

def generate_samples(model, num_samples, batch_size, device):
    """生成样本"""
    print(f"🎨 生成 {num_samples} 个样本...")
    
    generated_samples = []
    num_batches = (num_samples + batch_size - 1) // batch_size
    
    with torch.no_grad():
        for i in range(num_batches):
            current_batch_size = min(batch_size, num_samples - i * batch_size)
            
            # 从潜在空间采样
            z = torch.randn(current_batch_size, model.latent_dim).to(device)
            
            # 生成样本
            fake_samples = model.vae.decode(z)
            generated_samples.append(fake_samples.cpu())
            
            if (i + 1) % 10 == 0:
                print(f"  进度: {i+1}/{num_batches} 批次")
    
    generated_data = torch.cat(generated_samples, dim=0)[:num_samples]
    print(f"✅ 样本生成完成: {generated_data.shape}")
    
    return generated_data

def compute_fid_is_metrics(real_data, fake_data, device):
    """计算FID和IS指标 - 包括整体和通道级别"""
    print("📈 计算FID/IS指标...")

    try:
        from evaluation.fid_is_evaluator import compute_fid, compute_is

        # 整体FID/IS计算
        fid_score = compute_fid(real_data, fake_data, device)
        is_mean, is_std = compute_is(fake_data, device)

        print(f"✅ 整体FID: {fid_score:.2f}")
        print(f"✅ 整体IS: {is_mean:.2f} ± {is_std:.2f}")

        # 通道级别FID/IS计算
        channel_names = ["Period-DM", "Phase-Subband", "Phase-Subintegration"]
        channel_metrics = {}

        for ch_idx, ch_name in enumerate(channel_names):
            try:
                # 提取单通道数据并复制为3通道（Inception模型需要3通道输入）
                real_ch = real_data[:, ch_idx:ch_idx+1]  # [N, 1, H, W]
                fake_ch = fake_data[:, ch_idx:ch_idx+1]  # [N, 1, H, W]

                # 复制为3通道
                real_ch_3 = real_ch.repeat(1, 3, 1, 1)  # [N, 3, H, W]
                fake_ch_3 = fake_ch.repeat(1, 3, 1, 1)  # [N, 3, H, W]

                # 计算通道级别指标
                ch_fid = compute_fid(real_ch_3, fake_ch_3, device)
                ch_is_mean, ch_is_std = compute_is(fake_ch_3, device)

                channel_metrics[f'channel_{ch_idx}'] = {
                    'name': ch_name,
                    'fid': ch_fid,
                    'is_mean': ch_is_mean,
                    'is_std': ch_is_std
                }

                print(f"  ✅ {ch_name}: FID={ch_fid:.2f}, IS={ch_is_mean:.2f}±{ch_is_std:.2f}")

            except Exception as e:
                print(f"  ❌ {ch_name} 指标计算失败: {e}")
                channel_metrics[f'channel_{ch_idx}'] = {
                    'name': ch_name,
                    'fid': -1,
                    'is_mean': -1,
                    'is_std': -1
                }

        return {
            'overall': {
                'fid': fid_score,
                'is_mean': is_mean,
                'is_std': is_std
            },
            'channels': channel_metrics
        }

    except Exception as e:
        print(f"❌ FID/IS计算失败: {e}")
        return {
            'overall': {
                'fid': -1,
                'is_mean': -1,
                'is_std': -1
            },
            'channels': {}
        }

def visualize_samples(real_data, fake_data, output_dir):
    """可视化样本对比 - 包括通道级别可视化"""
    print("🖼️ 生成样本可视化...")

    try:
        # 通道名称
        channel_names = ["Period-DM", "Phase-Subband", "Phase-Subintegration"]

        # 1. 整体对比图
        fig, axes = plt.subplots(4, 8, figsize=(16, 8))
        fig.suptitle('真实样本 vs 生成样本对比 (Channel 0)', fontsize=16)

        # 显示真实样本 vs 生成样本 (Channel 0)
        for i in range(16):
            row = i // 4
            col = i % 4

            # 真实样本
            real_sample = real_data[i].cpu().numpy()
            axes[row, col].imshow(real_sample[0], cmap='viridis')
            axes[row, col].set_title(f'真实 {i+1}')
            axes[row, col].axis('off')

            # 生成样本
            fake_sample = fake_data[i].cpu().numpy()
            axes[row, col+4].imshow(fake_sample[0], cmap='viridis')
            axes[row, col+4].set_title(f'生成 {i+1}')
            axes[row, col+4].axis('off')

        plt.tight_layout()
        comparison_path = os.path.join(output_dir, 'sample_comparison.png')
        plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 通道级别对比图
        for ch_idx, ch_name in enumerate(channel_names):
            fig, axes = plt.subplots(2, 8, figsize=(16, 6))
            fig.suptitle(f'{ch_name} 通道对比', fontsize=16)

            # 显示8个样本的真实和生成对比
            for i in range(8):
                # 真实样本
                real_sample = real_data[i, ch_idx].cpu().numpy()
                axes[0, i].imshow(real_sample, cmap='viridis')
                axes[0, i].set_title(f'真实 {i+1}')
                axes[0, i].axis('off')

                # 生成样本
                fake_sample = fake_data[i, ch_idx].cpu().numpy()
                axes[1, i].imshow(fake_sample, cmap='viridis')
                axes[1, i].set_title(f'生成 {i+1}')
                axes[1, i].axis('off')

            plt.tight_layout()
            channel_path = os.path.join(output_dir, f'channel_{ch_idx}_{ch_name.lower().replace("-", "_")}_comparison.png')
            plt.savefig(channel_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"  ✅ {ch_name} 通道对比图保存")

        # 3. 三通道合成图
        fig, axes = plt.subplots(2, 6, figsize=(18, 6))
        fig.suptitle('三通道合成对比', fontsize=16)

        for i in range(6):
            # 真实样本 - RGB合成
            real_sample = real_data[i].cpu().numpy()
            real_rgb = np.stack([
                (real_sample[0] - real_sample[0].min()) / (real_sample[0].max() - real_sample[0].min()),
                (real_sample[1] - real_sample[1].min()) / (real_sample[1].max() - real_sample[1].min()),
                (real_sample[2] - real_sample[2].min()) / (real_sample[2].max() - real_sample[2].min())
            ], axis=-1)

            axes[0, i].imshow(real_rgb)
            axes[0, i].set_title(f'真实 {i+1}')
            axes[0, i].axis('off')

            # 生成样本 - RGB合成
            fake_sample = fake_data[i].cpu().numpy()
            fake_rgb = np.stack([
                (fake_sample[0] - fake_sample[0].min()) / (fake_sample[0].max() - fake_sample[0].min()),
                (fake_sample[1] - fake_sample[1].min()) / (fake_sample[1].max() - fake_sample[1].min()),
                (fake_sample[2] - fake_sample[2].min()) / (fake_sample[2].max() - fake_sample[2].min())
            ], axis=-1)

            axes[1, i].imshow(fake_rgb)
            axes[1, i].set_title(f'生成 {i+1}')
            axes[1, i].axis('off')

        plt.tight_layout()
        rgb_path = os.path.join(output_dir, 'three_channel_rgb_comparison.png')
        plt.savefig(rgb_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 主要对比图保存到: {comparison_path}")
        print(f"✅ 三通道RGB合成图保存到: {rgb_path}")

        # 4. 保存样本网格（如果visualization模块可用）
        try:
            from evaluation.visualization import save_sample_grid
            save_sample_grid(fake_data[:64], os.path.join(output_dir, 'generated_grid.png'))
            save_sample_grid(real_data[:64], os.path.join(output_dir, 'real_grid.png'))
            print("✅ 样本网格图保存完成")
        except ImportError:
            print("⚠️ visualization模块不可用，跳过网格图生成")

        return True

    except Exception as e:
        print(f"❌ 样本可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_physical_features(real_data, fake_data):
    """分析物理特征 - 包括通道级别分析"""
    print("🔬 分析物理特征...")

    try:
        # 通道名称
        channel_names = [
            "Channel 0 (Period-DM)",
            "Channel 1 (Phase-Subband)",
            "Channel 2 (Phase-Subintegration)"
        ]

        # 整体统计特征
        real_stats = {
            'mean': real_data.mean().item(),
            'std': real_data.std().item(),
            'min': real_data.min().item(),
            'max': real_data.max().item()
        }

        fake_stats = {
            'mean': fake_data.mean().item(),
            'std': fake_data.std().item(),
            'min': fake_data.min().item(),
            'max': fake_data.max().item()
        }

        # 通道级别统计特征
        channel_stats = {}
        for i, channel_name in enumerate(channel_names):
            real_ch = real_data[:, i]
            fake_ch = fake_data[:, i]

            channel_stats[f'channel_{i}'] = {
                'name': channel_name,
                'real': {
                    'mean': real_ch.mean().item(),
                    'std': real_ch.std().item(),
                    'min': real_ch.min().item(),
                    'max': real_ch.max().item(),
                    'energy': (real_ch ** 2).mean().item()
                },
                'fake': {
                    'mean': fake_ch.mean().item(),
                    'std': fake_ch.std().item(),
                    'min': fake_ch.min().item(),
                    'max': fake_ch.max().item(),
                    'energy': (fake_ch ** 2).mean().item()
                }
            }

            print(f"  ✅ {channel_name} 分析完成")

        # 计算通道间相关性
        real_corr = []
        fake_corr = []
        corr_pairs = []

        for i in range(3):
            for j in range(i+1, 3):
                real_ch_i = real_data[:, i].flatten()
                real_ch_j = real_data[:, j].flatten()
                real_corr.append(torch.corrcoef(torch.stack([real_ch_i, real_ch_j]))[0, 1].item())

                fake_ch_i = fake_data[:, i].flatten()
                fake_ch_j = fake_data[:, j].flatten()
                fake_corr.append(torch.corrcoef(torch.stack([fake_ch_i, fake_ch_j]))[0, 1].item())

                corr_pairs.append(f"Ch{i}-Ch{j}")

        # 计算频域特征（简化版）
        frequency_analysis = {}
        for i, channel_name in enumerate(channel_names):
            real_ch = real_data[:, i]
            fake_ch = fake_data[:, i]

            # 计算功率谱密度的近似
            real_fft = torch.fft.fft2(real_ch)
            fake_fft = torch.fft.fft2(fake_ch)

            real_power = torch.abs(real_fft).mean().item()
            fake_power = torch.abs(fake_fft).mean().item()

            frequency_analysis[f'channel_{i}'] = {
                'name': channel_name,
                'real_power': real_power,
                'fake_power': fake_power,
                'power_ratio': fake_power / real_power if real_power > 0 else 0
            }

        analysis = {
            'overall_stats': {
                'real': real_stats,
                'fake': fake_stats
            },
            'channel_stats': channel_stats,
            'correlations': {
                'pairs': corr_pairs,
                'real': real_corr,
                'fake': fake_corr
            },
            'frequency_analysis': frequency_analysis
        }

        print("✅ 物理特征分析完成（包括通道级别）")
        return analysis

    except Exception as e:
        print(f"❌ 物理特征分析失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def generate_evaluation_report(metrics, analysis, output_dir):
    """生成评估报告"""
    print("📋 生成评估报告...")
    
    try:
        # 创建报告数据
        # 处理指标结构
        overall_metrics = metrics.get('overall', metrics)  # 兼容旧格式

        report = {
            'evaluation_time': datetime.now().isoformat(),
            'metrics': metrics,
            'physical_analysis': analysis,
            'performance_assessment': {
                'fid_target': 40,
                'is_target': 5,
                'overall_fid_achieved': overall_metrics['fid'] < 40 if overall_metrics['fid'] > 0 else False,
                'overall_is_achieved': overall_metrics['is_mean'] > 5 if overall_metrics['is_mean'] > 0 else False
            }
        }
        
        # 保存JSON报告
        json_path = os.path.join(output_dir, 'evaluation_report.json')
        with open(json_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        # 生成Markdown报告
        md_path = os.path.join(output_dir, 'evaluation_report.md')
        with open(md_path, 'w') as f:
            f.write("# WGAN-GP+VAE模型评估报告\n\n")
            f.write(f"**评估时间**: {report['evaluation_time']}\n\n")
            
            f.write("## 生成质量指标\n\n")

            # 整体指标
            f.write("### 整体指标\n\n")
            f.write(f"- **FID**: {overall_metrics['fid']:.2f} (目标: <40)\n")
            f.write(f"- **IS**: {overall_metrics['is_mean']:.2f} ± {overall_metrics['is_std']:.2f} (目标: >5)\n\n")

            # 通道级别指标
            if 'channels' in metrics and metrics['channels']:
                f.write("### 通道级别指标\n\n")
                f.write("| 通道 | FID | IS (均值±标准差) |\n")
                f.write("|------|-----|------------------|\n")
                for ch_key, ch_metrics in metrics['channels'].items():
                    name = ch_metrics['name']
                    fid = ch_metrics['fid']
                    is_mean = ch_metrics['is_mean']
                    is_std = ch_metrics['is_std']
                    if fid > 0:
                        f.write(f"| {name} | {fid:.2f} | {is_mean:.2f} ± {is_std:.2f} |\n")
                    else:
                        f.write(f"| {name} | 计算失败 | 计算失败 |\n")
                f.write("\n")

            f.write("## 性能评估\n\n")
            fid_status = "✅ 达标" if report['performance_assessment']['overall_fid_achieved'] else "❌ 未达标"
            is_status = "✅ 达标" if report['performance_assessment']['overall_is_achieved'] else "❌ 未达标"
            f.write(f"- **整体FID目标**: {fid_status}\n")
            f.write(f"- **整体IS目标**: {is_status}\n\n")
            
            if analysis:
                f.write("## 物理特征分析\n\n")

                # 整体统计特征
                if 'overall_stats' in analysis:
                    f.write("### 整体统计特征对比\n\n")
                    f.write("| 特征 | 真实数据 | 生成数据 | 差异 |\n")
                    f.write("|------|----------|----------|------|\n")
                    real_stats = analysis['overall_stats']['real']
                    fake_stats = analysis['overall_stats']['fake']
                    for key in ['mean', 'std', 'min', 'max']:
                        real_val = real_stats[key]
                        fake_val = fake_stats[key]
                        diff = abs(fake_val - real_val) / abs(real_val) * 100 if real_val != 0 else 0
                        f.write(f"| {key} | {real_val:.4f} | {fake_val:.4f} | {diff:.2f}% |\n")

                # 通道级别统计特征
                if 'channel_stats' in analysis:
                    f.write("\n### 通道级别统计特征\n\n")
                    for ch_key, ch_data in analysis['channel_stats'].items():
                        f.write(f"#### {ch_data['name']}\n\n")
                        f.write("| 特征 | 真实数据 | 生成数据 | 差异 |\n")
                        f.write("|------|----------|----------|------|\n")
                        for key in ['mean', 'std', 'min', 'max', 'energy']:
                            real_val = ch_data['real'][key]
                            fake_val = ch_data['fake'][key]
                            diff = abs(fake_val - real_val) / abs(real_val) * 100 if real_val != 0 else 0
                            f.write(f"| {key} | {real_val:.4f} | {fake_val:.4f} | {diff:.2f}% |\n")
                        f.write("\n")

                # 通道间相关性
                if 'correlations' in analysis:
                    f.write("### 通道间相关性\n\n")
                    f.write("| 通道对 | 真实数据 | 生成数据 | 差异 |\n")
                    f.write("|--------|----------|----------|------|\n")
                    corr_data = analysis['correlations']
                    for i, pair in enumerate(corr_data['pairs']):
                        real_corr = corr_data['real'][i]
                        fake_corr = corr_data['fake'][i]
                        diff = abs(fake_corr - real_corr)
                        f.write(f"| {pair} | {real_corr:.4f} | {fake_corr:.4f} | {diff:.4f} |\n")

                # 频域分析
                if 'frequency_analysis' in analysis:
                    f.write("\n### 频域特征分析\n\n")
                    f.write("| 通道 | 真实功率 | 生成功率 | 功率比 |\n")
                    f.write("|------|----------|----------|--------|\n")
                    for ch_key, freq_data in analysis['frequency_analysis'].items():
                        name = freq_data['name']
                        real_power = freq_data['real_power']
                        fake_power = freq_data['fake_power']
                        ratio = freq_data['power_ratio']
                        f.write(f"| {name} | {real_power:.4f} | {fake_power:.4f} | {ratio:.4f} |\n")
        
        print(f"✅ 评估报告保存到:")
        print(f"  - JSON: {json_path}")
        print(f"  - Markdown: {md_path}")
        
        return report
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return {}

def main():
    """主评估函数"""
    args = parse_args()
    
    print("🚀 WGAN-GP+VAE模型评估开始")
    print(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    print(f"使用设备: {device}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载模型
    model = load_model(args.checkpoint, device)
    if model is None:
        return 1
    
    # 加载真实数据
    real_data = load_real_data(args.batch_size, device)
    if real_data is None:
        return 1
    
    # 生成样本
    fake_data = generate_samples(model, args.num_samples, args.batch_size, device)
    
    # 计算指标
    metrics = compute_fid_is_metrics(real_data, fake_data, device)
    
    # 可视化
    visualize_samples(real_data, fake_data, args.output_dir)
    
    # 物理特征分析
    analysis = analyze_physical_features(real_data, fake_data)
    
    # 生成报告
    report = generate_evaluation_report(metrics, analysis, args.output_dir)
    
    # 保存样本
    if args.save_samples:
        torch.save(fake_data, os.path.join(args.output_dir, 'generated_samples.pt'))
        torch.save(real_data, os.path.join(args.output_dir, 'real_samples.pt'))
        print(f"✅ 样本数据保存到: {args.output_dir}")
    
    # 输出最终结果
    print("\n" + "=" * 60)
    print("🎉 评估完成！")

    # 显示整体指标
    overall_metrics = metrics['overall']
    print(f"📊 整体FID: {overall_metrics['fid']:.2f} (目标: <40)")
    print(f"📊 整体IS: {overall_metrics['is_mean']:.2f} ± {overall_metrics['is_std']:.2f} (目标: >5)")

    # 显示通道级别指标
    if 'channels' in metrics and metrics['channels']:
        print("\n📊 通道级别指标:")
        for ch_key, ch_metrics in metrics['channels'].items():
            name = ch_metrics['name']
            fid = ch_metrics['fid']
            is_mean = ch_metrics['is_mean']
            is_std = ch_metrics['is_std']
            if fid > 0:  # 有效指标
                print(f"  {name}: FID={fid:.2f}, IS={is_mean:.2f}±{is_std:.2f}")
            else:
                print(f"  {name}: 指标计算失败")

    print(f"\n📁 结果保存在: {args.output_dir}")

    # 返回状态码
    overall_fid_ok = overall_metrics['fid'] < 40 if overall_metrics['fid'] > 0 else False
    overall_is_ok = overall_metrics['is_mean'] > 5 if overall_metrics['is_mean'] > 0 else False

    if overall_fid_ok and overall_is_ok:
        print("🎯 所有性能目标均已达成！")
        return 0
    else:
        print("⚠️ 部分性能目标未达成")
        return 1

if __name__ == "__main__":
    exit(main())
