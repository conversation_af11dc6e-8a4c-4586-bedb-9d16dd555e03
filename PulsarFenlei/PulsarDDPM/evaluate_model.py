#!/usr/bin/env python3
"""
WGAN-GP+VAE模型评估脚本

训练完成后的全面评估，包括：
1. FID/IS指标计算
2. 样本生成和可视化
3. 物理特征验证
4. 性能报告生成
"""

import sys
import os
sys.path.insert(0, os.getcwd())

import torch
import numpy as np
import matplotlib.pyplot as plt
import argparse
import json
from datetime import datetime
from pathlib import Path

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='WGAN-GP+VAE模型评估')
    
    parser.add_argument('--checkpoint', type=str, required=True, 
                       help='模型检查点路径')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results',
                       help='评估结果输出目录')
    parser.add_argument('--num_samples', type=int, default=995,
                       help='生成样本数量')
    parser.add_argument('--batch_size', type=int, default=32,
                       help='评估批次大小')
    parser.add_argument('--device', type=str, default='auto',
                       help='计算设备')
    parser.add_argument('--save_samples', action='store_true',
                       help='保存生成的样本')
    
    return parser.parse_args()

def load_model(checkpoint_path, device):
    """加载训练好的模型"""
    print(f"🔄 加载模型检查点: {checkpoint_path}")
    
    try:
        from models.wgan_vae import PulsarWGANVAE
        
        # 创建模型
        model = PulsarWGANVAE(latent_dim=64).to(device)
        
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location=device)
        
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model.eval()
        print(f"✅ 模型加载成功: {model.total_params/1e6:.2f}M 参数")
        
        return model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def load_real_data(batch_size, device):
    """加载真实数据用于对比"""
    print("📊 加载真实HTRU1数据...")
    
    try:
        from utils.data_loader import create_htru1_dataloader
        
        dataloader = create_htru1_dataloader(
            batch_size=batch_size,
            augment=False,
            num_workers=0
        )
        
        # 收集所有真实样本
        real_samples = []
        for batch in dataloader:
            real_samples.append(batch.to(device))
        
        real_data = torch.cat(real_samples, dim=0)
        print(f"✅ 真实数据加载完成: {real_data.shape}")
        
        return real_data
        
    except Exception as e:
        print(f"❌ 真实数据加载失败: {e}")
        return None

def generate_samples(model, num_samples, batch_size, device):
    """生成样本"""
    print(f"🎨 生成 {num_samples} 个样本...")
    
    generated_samples = []
    num_batches = (num_samples + batch_size - 1) // batch_size
    
    with torch.no_grad():
        for i in range(num_batches):
            current_batch_size = min(batch_size, num_samples - i * batch_size)
            
            # 从潜在空间采样
            z = torch.randn(current_batch_size, model.latent_dim).to(device)
            
            # 生成样本
            fake_samples = model.vae.decode(z)
            generated_samples.append(fake_samples.cpu())
            
            if (i + 1) % 10 == 0:
                print(f"  进度: {i+1}/{num_batches} 批次")
    
    generated_data = torch.cat(generated_samples, dim=0)[:num_samples]
    print(f"✅ 样本生成完成: {generated_data.shape}")
    
    return generated_data

def compute_fid_is_metrics(real_data, fake_data, device):
    """计算FID和IS指标"""
    print("📈 计算FID/IS指标...")
    
    try:
        from evaluation.fid_is_evaluator import compute_fid, compute_is
        
        # 计算FID
        fid_score = compute_fid(real_data, fake_data, device)
        print(f"✅ FID计算完成: {fid_score:.2f}")
        
        # 计算IS
        is_mean, is_std = compute_is(fake_data, device)
        print(f"✅ IS计算完成: {is_mean:.2f} ± {is_std:.2f}")
        
        return {
            'fid': fid_score,
            'is_mean': is_mean,
            'is_std': is_std
        }
        
    except Exception as e:
        print(f"❌ FID/IS计算失败: {e}")
        return {
            'fid': -1,
            'is_mean': -1,
            'is_std': -1
        }

def visualize_samples(real_data, fake_data, output_dir):
    """可视化样本对比"""
    print("🖼️ 生成样本可视化...")
    
    try:
        from evaluation.visualization import visualize_samples, save_sample_grid
        
        # 创建对比图
        fig, axes = plt.subplots(4, 8, figsize=(16, 8))
        fig.suptitle('真实样本 vs 生成样本对比', fontsize=16)
        
        # 显示真实样本 (前16个)
        for i in range(16):
            row = i // 4
            col = i % 4
            
            # 真实样本
            real_sample = real_data[i].cpu().numpy()
            axes[row, col].imshow(real_sample[0], cmap='viridis')
            axes[row, col].set_title(f'真实 {i+1}')
            axes[row, col].axis('off')
            
            # 生成样本
            fake_sample = fake_data[i].cpu().numpy()
            axes[row, col+4].imshow(fake_sample[0], cmap='viridis')
            axes[row, col+4].set_title(f'生成 {i+1}')
            axes[row, col+4].axis('off')
        
        plt.tight_layout()
        comparison_path = os.path.join(output_dir, 'sample_comparison.png')
        plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 样本对比图保存到: {comparison_path}")
        
        # 保存样本网格
        save_sample_grid(fake_data[:64], os.path.join(output_dir, 'generated_grid.png'))
        save_sample_grid(real_data[:64], os.path.join(output_dir, 'real_grid.png'))
        
        return True
        
    except Exception as e:
        print(f"❌ 样本可视化失败: {e}")
        return False

def analyze_physical_features(real_data, fake_data):
    """分析物理特征"""
    print("🔬 分析物理特征...")
    
    try:
        # 计算统计特征
        real_stats = {
            'mean': real_data.mean().item(),
            'std': real_data.std().item(),
            'min': real_data.min().item(),
            'max': real_data.max().item()
        }
        
        fake_stats = {
            'mean': fake_data.mean().item(),
            'std': fake_data.std().item(),
            'min': fake_data.min().item(),
            'max': fake_data.max().item()
        }
        
        # 计算通道间相关性
        real_corr = []
        fake_corr = []
        
        for i in range(3):
            for j in range(i+1, 3):
                real_ch_i = real_data[:, i].flatten()
                real_ch_j = real_data[:, j].flatten()
                real_corr.append(torch.corrcoef(torch.stack([real_ch_i, real_ch_j]))[0, 1].item())
                
                fake_ch_i = fake_data[:, i].flatten()
                fake_ch_j = fake_data[:, j].flatten()
                fake_corr.append(torch.corrcoef(torch.stack([fake_ch_i, fake_ch_j]))[0, 1].item())
        
        analysis = {
            'real_stats': real_stats,
            'fake_stats': fake_stats,
            'real_channel_correlations': real_corr,
            'fake_channel_correlations': fake_corr
        }
        
        print("✅ 物理特征分析完成")
        return analysis
        
    except Exception as e:
        print(f"❌ 物理特征分析失败: {e}")
        return {}

def generate_evaluation_report(metrics, analysis, output_dir):
    """生成评估报告"""
    print("📋 生成评估报告...")
    
    try:
        # 创建报告数据
        report = {
            'evaluation_time': datetime.now().isoformat(),
            'metrics': metrics,
            'physical_analysis': analysis,
            'performance_assessment': {
                'fid_target': 40,
                'is_target': 5,
                'fid_achieved': metrics['fid'] < 40 if metrics['fid'] > 0 else False,
                'is_achieved': metrics['is_mean'] > 5 if metrics['is_mean'] > 0 else False
            }
        }
        
        # 保存JSON报告
        json_path = os.path.join(output_dir, 'evaluation_report.json')
        with open(json_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        # 生成Markdown报告
        md_path = os.path.join(output_dir, 'evaluation_report.md')
        with open(md_path, 'w') as f:
            f.write("# WGAN-GP+VAE模型评估报告\n\n")
            f.write(f"**评估时间**: {report['evaluation_time']}\n\n")
            
            f.write("## 生成质量指标\n\n")
            f.write(f"- **FID**: {metrics['fid']:.2f} (目标: <40)\n")
            f.write(f"- **IS**: {metrics['is_mean']:.2f} ± {metrics['is_std']:.2f} (目标: >5)\n\n")
            
            f.write("## 性能评估\n\n")
            fid_status = "✅ 达标" if report['performance_assessment']['fid_achieved'] else "❌ 未达标"
            is_status = "✅ 达标" if report['performance_assessment']['is_achieved'] else "❌ 未达标"
            f.write(f"- **FID目标**: {fid_status}\n")
            f.write(f"- **IS目标**: {is_status}\n\n")
            
            if analysis:
                f.write("## 物理特征分析\n\n")
                f.write("### 统计特征对比\n\n")
                f.write("| 特征 | 真实数据 | 生成数据 |\n")
                f.write("|------|----------|----------|\n")
                for key in ['mean', 'std', 'min', 'max']:
                    real_val = analysis['real_stats'][key]
                    fake_val = analysis['fake_stats'][key]
                    f.write(f"| {key} | {real_val:.4f} | {fake_val:.4f} |\n")
        
        print(f"✅ 评估报告保存到:")
        print(f"  - JSON: {json_path}")
        print(f"  - Markdown: {md_path}")
        
        return report
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return {}

def main():
    """主评估函数"""
    args = parse_args()
    
    print("🚀 WGAN-GP+VAE模型评估开始")
    print(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    print(f"使用设备: {device}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载模型
    model = load_model(args.checkpoint, device)
    if model is None:
        return 1
    
    # 加载真实数据
    real_data = load_real_data(args.batch_size, device)
    if real_data is None:
        return 1
    
    # 生成样本
    fake_data = generate_samples(model, args.num_samples, args.batch_size, device)
    
    # 计算指标
    metrics = compute_fid_is_metrics(real_data, fake_data, device)
    
    # 可视化
    visualize_samples(real_data, fake_data, args.output_dir)
    
    # 物理特征分析
    analysis = analyze_physical_features(real_data, fake_data)
    
    # 生成报告
    report = generate_evaluation_report(metrics, analysis, args.output_dir)
    
    # 保存样本
    if args.save_samples:
        torch.save(fake_data, os.path.join(args.output_dir, 'generated_samples.pt'))
        torch.save(real_data, os.path.join(args.output_dir, 'real_samples.pt'))
        print(f"✅ 样本数据保存到: {args.output_dir}")
    
    # 输出最终结果
    print("\n" + "=" * 60)
    print("🎉 评估完成！")
    print(f"📊 FID: {metrics['fid']:.2f} (目标: <40)")
    print(f"📊 IS: {metrics['is_mean']:.2f} ± {metrics['is_std']:.2f} (目标: >5)")
    print(f"📁 结果保存在: {args.output_dir}")
    
    # 返回状态码
    if metrics['fid'] < 40 and metrics['is_mean'] > 5:
        print("🎯 所有性能目标均已达成！")
        return 0
    else:
        print("⚠️ 部分性能目标未达成")
        return 1

if __name__ == "__main__":
    exit(main())
