#!/usr/bin/env python3
"""
Optuna超参数自动优化脚本
为PulsarTransformerDDPM实现系统性超参数优化，目标：FID<40, IS>5
"""

import os
import sys
import json
import time
import logging
import argparse
from typing import Dict, <PERSON><PERSON>, Optional
from pathlib import Path

import torch
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
import numpy as np

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from evaluation.evaluate_model import evaluate_model
from utils.seed_utils import set_random_seed


class OptunaOptimizer:
    """Optuna超参数优化器"""

    def _find_data_directory(self):
        """智能查找HTRU1数据集路径"""
        # 尝试多个可能的数据集路径
        possible_data_dirs = [
            "/Pulsar/PulsarFenlei/data/htru1-batches-py",
            "/yanyb/jmy/Pulsar/PulsarFenlei/data/htru1-batches-py",
            "../data/htru1-batches-py",
            "../../data/htru1-batches-py",
            "../../../data/htru1-batches-py",
            os.path.join(os.path.dirname(__file__), "..", "..", "data", "htru1-batches-py"),
            os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "htru1-batches-py")
        ]

        for data_dir in possible_data_dirs:
            # 转换为绝对路径
            abs_data_dir = os.path.abspath(data_dir)

            # 检查目录是否存在
            if os.path.exists(abs_data_dir):
                # 检查是否包含必要的数据文件
                required_files = ['data_batch_1', 'data_batch_2', 'data_batch_3', 'data_batch_4', 'data_batch_5']
                missing_files = []

                for file_name in required_files:
                    file_path = os.path.join(abs_data_dir, file_name)
                    if not os.path.exists(file_path):
                        missing_files.append(file_name)

                if not missing_files:
                    print(f"✅ 找到完整的HTRU1数据集: {abs_data_dir}")
                    return abs_data_dir
                else:
                    print(f"⚠️ 数据目录存在但缺少文件: {abs_data_dir}, 缺少: {missing_files}")
            else:
                print(f"❌ 数据目录不存在: {abs_data_dir}")

        # 如果都没找到，返回None
        print("❌ 未找到有效的HTRU1数据集路径")
        print("请确保数据集位于以下任一路径:")
        for data_dir in possible_data_dirs:
            print(f"  - {os.path.abspath(data_dir)}")

        return None

    def __init__(
        self,
        study_name: str = "pulsar_ddpm_optimization",
        storage_url: str = "sqlite:///optuna_study.db",
        n_trials: int = 50,
        timeout: int = 115200,  # 32小时
        n_jobs: int = 1,
        device: str = "cuda"
    ):
        self.study_name = study_name
        self.storage_url = storage_url
        self.n_trials = n_trials
        self.timeout = timeout
        self.n_jobs = n_jobs
        self.device = device

        # 设置日志
        self.setup_logging()

        # 创建输出目录
        self.output_dir = Path("optuna_results")
        self.output_dir.mkdir(exist_ok=True)

        # 智能查找数据路径
        data_dir = self._find_data_directory()
        if not data_dir:
            raise FileNotFoundError("无法找到HTRU1数据集，请检查数据路径配置")

        # 基础配置（基于根本原因分析的优化配置）
        self.base_config = {
            'data_dir': data_dir,
            'img_channels': 3,
            'max_epochs': 200,  # 增加训练轮数以充分训练
            'gradient_clip_val': 0.5,
            'eval_every': 50,   # 减少评估频率，让模型充分训练
            'save_every': 50,
            'eval_num_samples': 995,
            'eval_batch_size': 50,
            'mixed_precision': True,
            'compile_model': True,
            'device': device,
            'seed': 42,
            # 优化的损失权重配置
            'mse_weight': 0.8,
            'spectral_weight': 0.0,  # 暂时禁用
            'stage_epochs': "100,60,40",  # 重新分配训练阶段，更多基础训练
            'stage_lr_factors': "1.0,0.6,0.3",  # 更保守的学习率衰减
        }

    def setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('optuna_optimization.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def define_parameter_space(self, trial: optuna.Trial) -> Dict:
        """定义优化参数空间 - 基于根本原因分析的优化版本"""
        params = {
            # 学习率优化 - 显著提高学习率范围
            'lr': trial.suggest_float('lr', 1e-5, 5e-5, log=True),  # 提高学习率
            'stage_lr_factor_2': trial.suggest_float('stage_lr_factor_2', 0.5, 0.8),  # 更保守的衰减

            # 物理损失权重优化
            'physics_weight': trial.suggest_float('physics_weight', 0.01, 0.08),
            'consistency_weight': trial.suggest_float('consistency_weight', 0.05, 0.15),
            'channel_diffusion_weight': trial.suggest_float('channel_diffusion_weight', 0.01, 0.05),

            # 模型容量参数 - 显著增加模型容量
            'base_channels': trial.suggest_categorical('base_channels', [96, 120, 144]),  # 大幅增加容量
            'transformer_dim': trial.suggest_categorical('transformer_dim', [192, 240, 288]),  # 增加表示能力
            'num_heads': trial.suggest_categorical('num_heads', [8, 12, 16]),  # 增加注意力头
            'num_res_blocks': trial.suggest_categorical('num_res_blocks', [3, 4]),  # 增加深度

            # 扩散参数优化
            'num_timesteps': trial.suggest_categorical('num_timesteps', [300, 400, 500]),  # 增加时间步

            # PulsarAdaptiveSampler参数优化 - 提高采样质量
            'use_pulsar_adaptive': True,  # 默认启用PulsarAdaptiveSampler
            'num_inference_steps': trial.suggest_categorical('num_inference_steps', [25, 50, 75]),  # 增加采样步数
            'numerical_stability': True,  # 始终启用数值稳定性
            'pulsar_optimization': True,  # 始终启用脉冲星优化
            'channel_aware': True,  # 始终启用通道感知

            # DDIM参数（向后兼容）
            'ddim_steps': trial.suggest_categorical('ddim_steps', [50, 75, 100]),

            # 训练策略参数 - 优化训练稳定性
            'batch_size': trial.suggest_categorical('batch_size', [16, 24, 32]),  # 适应更大模型
            'dropout': trial.suggest_float('dropout', 0.05, 0.15),  # 增加正则化
            'weight_decay': trial.suggest_float('weight_decay', 1e-5, 5e-5, log=True),  # 增加权重衰减
            'warmup_epochs': trial.suggest_categorical('warmup_epochs', [20, 30, 40]),  # 延长预热
        }

        # 更新stage_lr_factors
        params['stage_lr_factors'] = f"1.0,{params['stage_lr_factor_2']},0.3"

        return params

    def create_trial_config(self, trial: optuna.Trial) -> Dict:
        """创建试验配置"""
        # 获取优化参数
        trial_params = self.define_parameter_space(trial)

        # 合并基础配置和试验参数
        config = self.base_config.copy()
        config.update(trial_params)

        # 创建试验特定的输出目录
        trial_output_dir = self.output_dir / f"trial_{trial.number}"
        trial_output_dir.mkdir(exist_ok=True)
        config['output_dir'] = str(trial_output_dir)

        return config

    def objective(self, trial: optuna.Trial) -> float:
        """优化目标函数"""
        try:
            self.logger.info(f"开始试验 {trial.number}")

            # 设置随机种子
            set_random_seed(42)

            # 创建试验配置
            config = self.create_trial_config(trial)

            # 记录试验参数
            self.logger.info(f"试验 {trial.number} 参数: {trial.params}")

            # 执行训练
            start_time = time.time()
            training_success = self.run_training(config, trial)
            training_time = time.time() - start_time

            if not training_success:
                self.logger.warning(f"试验 {trial.number} 训练失败")
                return float('inf')  # 返回最差分数

            # 执行评估
            fid_score, is_score, eval_success = self.run_evaluation(config)

            if not eval_success:
                self.logger.warning(f"试验 {trial.number} 评估失败")
                return float('inf')

            # 计算训练稳定性指标
            stability_score = min(1.0, 3600 / training_time)  # 训练时间越短稳定性越高

            # 计算目标函数值
            objective_value = self.calculate_objective(fid_score, is_score, stability_score)

            # 记录结果
            self.logger.info(f"试验 {trial.number} 结果: FID={fid_score:.2f}, IS={is_score:.3f}, "
                           f"稳定性={stability_score:.3f}, 目标值={objective_value:.6f}")

            # 保存试验结果
            self.save_trial_result(trial.number, {
                'params': trial.params,
                'fid_score': fid_score,
                'is_score': is_score,
                'stability_score': stability_score,
                'objective_value': objective_value,
                'training_time': training_time
            })

            # 报告中间值用于剪枝
            trial.report(objective_value, step=0)

            return objective_value

        except Exception as e:
            self.logger.error(f"试验 {trial.number} 出现异常: {str(e)}")
            return float('inf')

    def calculate_objective(self, fid_score: float, is_score: float, stability_score: float) -> float:
        """计算多目标优化函数值"""
        # 目标：FID<40, IS>5
        fid_penalty = max(0, (fid_score - 40) / 260)  # 归一化FID惩罚 (40-300)
        is_penalty = max(0, (5 - is_score) / 3.64)   # 归一化IS惩罚 (1.36-5)
        stability_bonus = stability_score  # 稳定性奖励

        # 加权组合（FID权重0.6, IS权重0.3, 稳定性权重0.1）
        objective = 0.6 * fid_penalty + 0.3 * is_penalty - 0.1 * stability_bonus
        return objective

    def run_training(self, config: Dict, trial: optuna.Trial) -> bool:
        """执行训练"""
        try:
            # 使用subprocess调用训练脚本，避免函数签名问题
            import subprocess

            # 构建命令行参数 - 支持新的优化参数
            cmd = [
                "python", "train_pulsar_ddpm_a100.py",
                "--data_dir", config['data_dir'],
                "--batch_size", str(config['batch_size']),
                "--img_channels", str(config['img_channels']),
                "--base_channels", str(config['base_channels']),
                "--transformer_dim", str(config['transformer_dim']),
                "--num_heads", str(config.get('num_heads', 6)),  # 支持新的num_heads参数
                "--num_res_blocks", str(config.get('num_res_blocks', 2)),  # 支持新的num_res_blocks参数
                "--num_timesteps", str(config['num_timesteps']),
                "--dropout", str(config['dropout']),
                "--lr", str(config['lr']),
                "--weight_decay", str(config['weight_decay']),
                "--warmup_epochs", str(config.get('warmup_epochs', 10)),  # 支持新的warmup_epochs参数
                "--max_epochs", str(config['max_epochs']),
                "--stage_epochs", config['stage_epochs'],
                "--stage_lr_factors", config['stage_lr_factors'],
                "--mse_weight", str(config['mse_weight']),
                "--physics_weight", str(config['physics_weight']),
                "--consistency_weight", str(config['consistency_weight']),
                "--spectral_weight", str(config['spectral_weight']),
                "--channel_diffusion_weight", str(config['channel_diffusion_weight']),
                "--ddim_steps", str(config.get('ddim_steps', 100)),
                "--output_dir", config['output_dir'],
                "--eval_every", str(config.get('eval_every', 50)),
                "--save_every", str(config.get('save_every', 50)),
                "--eval_num_samples", str(config['eval_num_samples']),
                "--eval_batch_size", str(config['eval_batch_size']),
                "--device", config['device'],
                "--seed", str(config['seed'])
            ]

            # 添加PulsarAdaptiveSampler参数
            if config.get('use_pulsar_adaptive', True):
                cmd.extend([
                    "--use_pulsar_adaptive",
                    "--num_inference_steps", str(config.get('num_inference_steps', 20))
                ])
                if config.get('numerical_stability', True):
                    cmd.append("--numerical_stability")
                if config.get('pulsar_optimization', True):
                    cmd.append("--pulsar_optimization")
                if config.get('channel_aware', True):
                    cmd.append("--channel_aware")

            # 添加其他布尔标志
            if config.get('mixed_precision', True):
                cmd.append("--mixed_precision")
            if config.get('compile_model', True):
                cmd.append("--compile_model")

            # 执行训练
            self.logger.info(f"执行训练命令: {' '.join(cmd[:10])}...")  # 只显示前10个参数

            # 设置环境变量，确保Python路径正确
            env = os.environ.copy()
            current_dir = str(Path(__file__).parent)
            if 'PYTHONPATH' in env:
                env['PYTHONPATH'] = f"{current_dir}:{env['PYTHONPATH']}"
            else:
                env['PYTHONPATH'] = current_dir

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=7200,  # 2小时超时
                cwd=current_dir,
                env=env
            )

            if result.returncode == 0:
                self.logger.info(f"训练成功完成")
                return True
            else:
                self.logger.error(f"训练失败，返回码: {result.returncode}")
                self.logger.error(f"标准输出: {result.stdout[-1000:]}...")  # 显示最后1000字符
                self.logger.error(f"错误输出: {result.stderr[-1000:]}...")  # 显示最后1000字符

                # 分析错误类型
                stderr_lower = result.stderr.lower()
                if "dataload" in stderr_lower or "dataloader" in stderr_lower:
                    self.logger.error("错误类型: 数据加载器相关")
                elif "import" in stderr_lower or "modulenotfound" in stderr_lower:
                    self.logger.error("错误类型: 模块导入相关")
                elif "cuda" in stderr_lower or "gpu" in stderr_lower:
                    self.logger.error("错误类型: CUDA/GPU相关")
                elif "memory" in stderr_lower or "oom" in stderr_lower:
                    self.logger.error("错误类型: 内存不足相关")
                else:
                    self.logger.error("错误类型: 其他未知错误")

                return False

        except subprocess.TimeoutExpired:
            self.logger.error("训练超时")
            return False
        except Exception as e:
            self.logger.error(f"训练过程出现错误: {str(e)}")
            return False

    def run_evaluation(self, config: Dict) -> Tuple[float, float, bool]:
        """执行评估"""
        try:
            # 查找最新的检查点
            checkpoint_dir = Path(config['output_dir']) / "checkpoints"
            if not checkpoint_dir.exists():
                return float('inf'), 0.0, False

            checkpoints = list(checkpoint_dir.glob("*.pth"))
            if not checkpoints:
                return float('inf'), 0.0, False

            # 使用最新的检查点
            latest_checkpoint = max(checkpoints, key=lambda x: x.stat().st_mtime)

            # 执行评估（支持PulsarAdaptiveSampler）
            eval_kwargs = {
                'checkpoint_path': str(latest_checkpoint),
                'data_dir': config['data_dir'],
                'num_samples': config['eval_num_samples'],
                'batch_size': config['eval_batch_size'],
                'device': config['device']
            }

            # 添加PulsarAdaptiveSampler参数
            if config.get('use_pulsar_adaptive', True):
                eval_kwargs.update({
                    'use_pulsar_adaptive': True,
                    'num_inference_steps': config.get('num_inference_steps', 20),
                    'numerical_stability': config.get('numerical_stability', True),
                    'pulsar_optimization': config.get('pulsar_optimization', True),
                    'channel_aware': config.get('channel_aware', True)
                })

            results = evaluate_model(**eval_kwargs)

            # 尝试多种可能的键名来获取FID和IS分数
            fid_score = results.get('overall_fid') or results.get('fid') or float('inf')
            is_score = results.get('overall_is_mean') or results.get('is_mean') or 0.0

            # 如果仍然没有找到，尝试从JSON文件直接读取
            if fid_score == float('inf') or is_score == 0.0:
                trial_output_dir = Path(config['output_dir'])
                eval_dir = trial_output_dir / "logs" / "eval"
                if eval_dir.exists():
                    json_files = list(eval_dir.glob("results_epoch_*.json"))
                    if json_files:
                        latest_json = max(json_files, key=lambda x: int(x.stem.split("_")[-1]))
                        try:
                            import json
                            with open(latest_json, 'r') as f:
                                json_results = json.load(f)
                            fid_score = json_results.get('fid', fid_score)
                            is_score = json_results.get('is_mean', is_score)
                            self.logger.info(f"从JSON文件读取结果: FID={fid_score:.2f}, IS={is_score:.3f}")
                        except Exception as e:
                            self.logger.warning(f"无法读取JSON结果文件: {e}")

            return fid_score, is_score, True

        except Exception as e:
            self.logger.error(f"评估过程出现错误: {str(e)}")
            return float('inf'), 0.0, False

    def config_to_args(self, config: Dict) -> argparse.Namespace:
        """将配置字典转换为argparse.Namespace"""
        args = argparse.Namespace()
        for key, value in config.items():
            setattr(args, key, value)
        return args

    def save_trial_result(self, trial_number: int, result: Dict):
        """保存试验结果"""
        result_file = self.output_dir / f"trial_{trial_number}_result.json"
        with open(result_file, 'w') as f:
            json.dump(result, f, indent=2)

    def create_study(self) -> optuna.Study:
        """创建或加载Optuna研究"""
        # 配置采样器和剪枝器
        sampler = TPESampler(
            n_startup_trials=10,  # 前10次试验使用随机采样
            n_ei_candidates=24,   # 期望改善候选数
            seed=42
        )

        pruner = MedianPruner(
            n_startup_trials=5,   # 前5次试验不剪枝
            n_warmup_steps=10,    # 预热步数
            interval_steps=5      # 剪枝检查间隔
        )

        # 创建或加载研究
        study = optuna.create_study(
            study_name=self.study_name,
            storage=self.storage_url,
            load_if_exists=True,
            direction='minimize',  # 最小化目标函数
            sampler=sampler,
            pruner=pruner
        )

        return study

    def optimize(self):
        """执行优化"""
        self.logger.info(f"开始Optuna超参数优化，目标：FID<40, IS>5")
        self.logger.info(f"计划试验次数: {self.n_trials}, 超时时间: {self.timeout}秒")

        # 创建研究
        study = self.create_study()

        # 如果有之前的试验，显示最佳结果
        if len(study.trials) > 0:
            self.logger.info(f"发现 {len(study.trials)} 个历史试验")
            if study.best_trial:
                self.logger.info(f"当前最佳试验: {study.best_trial.number}, "
                               f"目标值: {study.best_value:.6f}")

        try:
            # 执行优化
            study.optimize(
                self.objective,
                n_trials=self.n_trials,
                timeout=self.timeout,
                n_jobs=self.n_jobs,
                show_progress_bar=True
            )

            # 输出最佳结果
            self.report_best_results(study)

            # 保存最佳配置
            self.save_best_config(study)

        except KeyboardInterrupt:
            self.logger.info("优化被用户中断")
            if len(study.trials) > 0:
                self.report_best_results(study)
        except Exception as e:
            self.logger.error(f"优化过程出现错误: {str(e)}")

    def report_best_results(self, study: optuna.Study):
        """报告最佳结果"""
        if not study.best_trial:
            self.logger.warning("没有找到有效的试验结果")
            return

        best_trial = study.best_trial
        self.logger.info("=" * 60)
        self.logger.info("🎯 优化完成！最佳结果:")
        self.logger.info(f"试验编号: {best_trial.number}")
        self.logger.info(f"目标函数值: {best_trial.value:.6f}")

        # 尝试从保存的结果中获取详细信息
        result_file = self.output_dir / f"trial_{best_trial.number}_result.json"
        if result_file.exists():
            with open(result_file, 'r') as f:
                result = json.load(f)
            self.logger.info(f"FID评分: {result.get('fid_score', 'N/A')}")
            self.logger.info(f"IS评分: {result.get('is_score', 'N/A')}")
            self.logger.info(f"稳定性评分: {result.get('stability_score', 'N/A')}")
            self.logger.info(f"训练时间: {result.get('training_time', 'N/A'):.1f}秒")

        self.logger.info("最佳参数:")
        for key, value in best_trial.params.items():
            self.logger.info(f"  {key}: {value}")
        self.logger.info("=" * 60)

    def save_best_config(self, study: optuna.Study):
        """保存最佳配置"""
        if not study.best_trial:
            return

        best_params = study.best_trial.params

        # 创建最佳配置
        best_config = self.base_config.copy()
        best_config.update(best_params)

        # 恢复完整训练配置
        best_config['max_epochs'] = 400
        best_config['stage_epochs'] = "150,150,100"
        best_config['eval_every'] = 50

        # 保存配置文件
        config_file = self.output_dir / "best_config.json"
        with open(config_file, 'w') as f:
            json.dump(best_config, f, indent=2)

        # 生成启动脚本
        self.generate_best_training_script(best_config)

        self.logger.info(f"最佳配置已保存到: {config_file}")

    def generate_best_training_script(self, config: Dict):
        """生成最佳配置的训练脚本"""
        script_content = f"""#!/bin/bash
# 基于Optuna优化的最佳配置训练脚本
# 自动生成于: {time.strftime('%Y-%m-%d %H:%M:%S')}

cd "$(dirname "$0")"

python train_pulsar_ddpm_a100.py \\
    --data_dir "{config['data_dir']}" \\
    --batch_size {config['batch_size']} \\
    --img_channels {config['img_channels']} \\
    --base_channels {config['base_channels']} \\
    --transformer_dim {config['transformer_dim']} \\
    --num_heads {config['num_heads']} \\
    --num_timesteps {config['num_timesteps']} \\
    --dropout {config['dropout']} \\
    --lr {config['lr']} \\
    --weight_decay {config['weight_decay']} \\
    --max_epochs {config['max_epochs']} \\
    --stage_epochs "{config['stage_epochs']}" \\
    --stage_lr_factors "{config['stage_lr_factors']}" \\
    --mse_weight {config['mse_weight']} \\
    --physics_weight {config['physics_weight']} \\
    --consistency_weight {config['consistency_weight']} \\
    --spectral_weight {config['spectral_weight']} \\
    --channel_diffusion_weight {config['channel_diffusion_weight']} \\
    --ddim_steps {config['ddim_steps']} \\
    --use_pulsar_adaptive \\
    --num_inference_steps {config.get('num_inference_steps', 20)} \\
    --numerical_stability \\
    --pulsar_optimization \\
    --channel_aware \\
    --output_dir "outputs_optuna_best" \\
    --eval_every {config['eval_every']} \\
    --save_every 50 \\
    --eval_num_samples {config['eval_num_samples']} \\
    --mixed_precision \\
    --compile_model \\
    --device {config['device']} \\
    --seed {config['seed']}
"""

        script_file = self.output_dir / "train_best_config.sh"
        with open(script_file, 'w') as f:
            f.write(script_content)

        # 设置执行权限
        os.chmod(script_file, 0o755)

        self.logger.info(f"最佳配置训练脚本已生成: {script_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Optuna超参数优化")
    parser.add_argument("--study_name", type=str, default="pulsar_ddpm_optimization",
                       help="研究名称")
    parser.add_argument("--n_trials", type=int, default=50,
                       help="试验次数")
    parser.add_argument("--timeout", type=int, default=115200,
                       help="超时时间（秒）")
    parser.add_argument("--n_jobs", type=int, default=1,
                       help="并行作业数")
    parser.add_argument("--device", type=str, default="cuda",
                       help="设备")

    args = parser.parse_args()

    # 创建优化器
    optimizer = OptunaOptimizer(
        study_name=args.study_name,
        n_trials=args.n_trials,
        timeout=args.timeout,
        n_jobs=args.n_jobs,
        device=args.device
    )

    # 执行优化
    optimizer.optimize()


if __name__ == "__main__":
    main()
