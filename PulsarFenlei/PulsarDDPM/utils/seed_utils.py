#!/usr/bin/env python3
"""
随机种子管理工具模块
提供统一的随机种子设置功能，确保实验的可重现性
"""

import os
import torch
import numpy as np
import random
import logging
from typing import Optional

logger = logging.getLogger(__name__)

def set_random_seed(
    seed: Optional[int] = None,
    deterministic: bool = True,
    benchmark: bool = False,
    verbose: bool = True
) -> int:
    """
    设置所有相关库的随机种子，确保实验的可重现性
    
    Args:
        seed (Optional[int]): 随机种子值。如果为None，将使用当前时间生成
        deterministic (bool): 是否启用确定性模式（可能影响性能）
        benchmark (bool): 是否启用CUDNN基准模式（与确定性模式互斥）
        verbose (bool): 是否输出详细日志
    
    Returns:
        int: 实际使用的随机种子值
    """
    # 如果没有提供种子，使用当前时间生成
    if seed is None:
        import time
        seed = int(time.time() * 1000) % (2**32)
        if verbose:
            logger.info(f"No seed provided, generated seed: {seed}")
    
    # 验证种子值范围
    if not (0 <= seed < 2**32):
        raise ValueError(f"Seed must be in range [0, 2^32), got {seed}")
    
    if verbose:
        logger.info(f"Setting random seed to {seed}")
    
    # 设置Python内置random模块的种子
    random.seed(seed)
    if verbose:
        logger.debug("Set Python random seed")
    
    # 设置NumPy的随机种子
    np.random.seed(seed)
    if verbose:
        logger.debug("Set NumPy random seed")
    
    # 设置PyTorch的随机种子
    torch.manual_seed(seed)
    if verbose:
        logger.debug("Set PyTorch CPU random seed")
    
    # 设置CUDA随机种子（如果可用）
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)  # 为所有GPU设置种子
        if verbose:
            logger.debug("Set PyTorch CUDA random seed for all devices")
    
    # 设置CUDNN行为
    if torch.cuda.is_available():
        if deterministic and benchmark:
            logger.warning("deterministic=True and benchmark=True are mutually exclusive. "
                         "Setting benchmark=False to maintain determinism.")
            benchmark = False
        
        torch.backends.cudnn.deterministic = deterministic
        torch.backends.cudnn.benchmark = benchmark
        
        if verbose:
            logger.debug(f"Set CUDNN deterministic={deterministic}, benchmark={benchmark}")
    
    # 设置环境变量以确保其他库的确定性行为
    if deterministic:
        os.environ['PYTHONHASHSEED'] = str(seed)
        if verbose:
            logger.debug(f"Set PYTHONHASHSEED={seed}")
    
    if verbose:
        logger.info(f"Random seed {seed} set successfully for all libraries")
        if deterministic:
            logger.info("Deterministic mode enabled - results should be reproducible")
        else:
            logger.info("Deterministic mode disabled - performance may be better but results may vary")
    
    return seed

def verify_random_seed(seed: int, num_samples: int = 10) -> dict:
    """
    验证随机种子设置是否生效
    
    Args:
        seed (int): 要验证的随机种子
        num_samples (int): 生成的测试样本数量
    
    Returns:
        dict: 包含各个库生成的随机数样本
    """
    logger.info(f"Verifying random seed {seed} with {num_samples} samples")
    
    # 设置种子
    set_random_seed(seed, verbose=False)
    
    # 生成测试样本
    results = {}
    
    # Python random
    results['python_random'] = [random.random() for _ in range(num_samples)]
    
    # NumPy random
    results['numpy_random'] = np.random.random(num_samples).tolist()
    
    # PyTorch random
    results['torch_random'] = torch.rand(num_samples).tolist()
    
    # CUDA random (如果可用)
    if torch.cuda.is_available():
        results['torch_cuda_random'] = torch.cuda.FloatTensor(num_samples).uniform_().tolist()
    
    logger.info("Random seed verification completed")
    return results

def compare_random_outputs(seed: int, num_runs: int = 2, num_samples: int = 5) -> bool:
    """
    比较多次运行的随机输出是否一致
    
    Args:
        seed (int): 随机种子
        num_runs (int): 运行次数
        num_samples (int): 每次运行的样本数
    
    Returns:
        bool: 是否所有运行的结果都一致
    """
    logger.info(f"Comparing random outputs across {num_runs} runs with seed {seed}")
    
    all_results = []
    
    for run in range(num_runs):
        results = verify_random_seed(seed, num_samples)
        all_results.append(results)
        logger.debug(f"Run {run + 1} completed")
    
    # 比较所有运行的结果
    is_consistent = True
    for key in all_results[0].keys():
        first_result = all_results[0][key]
        for i in range(1, num_runs):
            if all_results[i][key] != first_result:
                logger.error(f"Inconsistent results for {key} between run 1 and run {i + 1}")
                logger.error(f"Run 1: {first_result}")
                logger.error(f"Run {i + 1}: {all_results[i][key]}")
                is_consistent = False
    
    if is_consistent:
        logger.info("✅ All random outputs are consistent across runs")
    else:
        logger.error("❌ Random outputs are inconsistent across runs")
    
    return is_consistent

def get_random_state_info() -> dict:
    """
    获取当前随机状态信息
    
    Returns:
        dict: 包含各个库的随机状态信息
    """
    info = {}
    
    # PyTorch状态
    info['torch_rng_state'] = torch.get_rng_state()
    if torch.cuda.is_available():
        info['torch_cuda_rng_state'] = torch.cuda.get_rng_state()
    
    # NumPy状态
    info['numpy_rng_state'] = np.random.get_state()
    
    # Python random状态
    info['python_rng_state'] = random.getstate()
    
    # CUDNN设置
    if torch.cuda.is_available():
        info['cudnn_deterministic'] = torch.backends.cudnn.deterministic
        info['cudnn_benchmark'] = torch.backends.cudnn.benchmark
    
    # 环境变量
    info['pythonhashseed'] = os.environ.get('PYTHONHASHSEED', 'Not set')
    
    return info

def save_random_state(filepath: str) -> None:
    """
    保存当前随机状态到文件
    
    Args:
        filepath (str): 保存文件路径
    """
    import pickle
    
    state_info = get_random_state_info()
    
    with open(filepath, 'wb') as f:
        pickle.dump(state_info, f)
    
    logger.info(f"Random state saved to {filepath}")

def load_random_state(filepath: str) -> None:
    """
    从文件加载随机状态
    
    Args:
        filepath (str): 状态文件路径
    """
    import pickle
    
    with open(filepath, 'rb') as f:
        state_info = pickle.load(f)
    
    # 恢复PyTorch状态
    if 'torch_rng_state' in state_info:
        torch.set_rng_state(state_info['torch_rng_state'])
    
    if 'torch_cuda_rng_state' in state_info and torch.cuda.is_available():
        torch.cuda.set_rng_state(state_info['torch_cuda_rng_state'])
    
    # 恢复NumPy状态
    if 'numpy_rng_state' in state_info:
        np.random.set_state(state_info['numpy_rng_state'])
    
    # 恢复Python random状态
    if 'python_rng_state' in state_info:
        random.setstate(state_info['python_rng_state'])
    
    # 恢复CUDNN设置
    if torch.cuda.is_available():
        if 'cudnn_deterministic' in state_info:
            torch.backends.cudnn.deterministic = state_info['cudnn_deterministic']
        if 'cudnn_benchmark' in state_info:
            torch.backends.cudnn.benchmark = state_info['cudnn_benchmark']
    
    logger.info(f"Random state loaded from {filepath}")

# 便捷函数
def set_seed_for_training(seed: int) -> int:
    """
    为训练设置随机种子（启用确定性模式，禁用基准模式）
    
    Args:
        seed (int): 随机种子
    
    Returns:
        int: 设置的随机种子
    """
    return set_random_seed(seed, deterministic=True, benchmark=False, verbose=True)

def set_seed_for_evaluation(seed: int) -> int:
    """
    为评估设置随机种子（启用确定性模式，禁用基准模式）
    
    Args:
        seed (int): 随机种子
    
    Returns:
        int: 设置的随机种子
    """
    return set_random_seed(seed, deterministic=True, benchmark=False, verbose=True)

def set_seed_for_inference(seed: int) -> int:
    """
    为推理设置随机种子（可以启用基准模式以提高性能）
    
    Args:
        seed (int): 随机种子
    
    Returns:
        int: 设置的随机种子
    """
    return set_random_seed(seed, deterministic=False, benchmark=True, verbose=True)
