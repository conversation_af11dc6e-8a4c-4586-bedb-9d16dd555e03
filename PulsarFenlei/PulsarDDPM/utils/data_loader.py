#!/usr/bin/env python3
"""
HTRU1数据加载器
适配WGAN-GP+VAE混合架构的数据处理需求

基于WGAN-GP+VAE实施计划的数据规格：
- 995个32x32x3正样本 (label=0)
- 归一化到[-1,1]范围
- 物理感知数据增强
- 批次处理优化
"""

import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from typing import Tuple, List, Optional, Dict
import logging

# 导入pickle用于HTRU1数据集
import pickle
HAS_PICKLE = True

# 可选导入h5py（保留兼容性）
try:
    import h5py
    HAS_H5PY = True
except ImportError:
    HAS_H5PY = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HTRU1PulsarDataset(Dataset):
    """
    HTRU1脉冲星数据集
    
    专门处理HTRU1数据集中的正样本 (脉冲星)
    - 自动筛选label=0的样本
    - 归一化到[-1,1]范围
    - 支持物理感知数据增强
    """
    
    def __init__(self, data_paths: List[str], augment: bool = True, 
                 augment_factor: int = 2, normalize: bool = True):
        """
        初始化数据集
        
        Args:
            data_paths: 数据文件路径列表
            augment: 是否进行数据增强
            augment_factor: 数据增强倍数
            normalize: 是否归一化到[-1,1]
        """
        self.augment = augment
        self.augment_factor = augment_factor
        self.normalize = normalize
        
        # 加载数据
        self.data, self.labels = self._load_data(data_paths)
        
        # 筛选正样本 (label=0)
        self.positive_indices = np.where(self.labels == 0)[0]
        self.positive_data = self.data[self.positive_indices]
        
        logger.info(f"HTRU1数据集加载完成:")
        logger.info(f"  总样本数: {len(self.data)}")
        logger.info(f"  正样本数: {len(self.positive_data)}")
        logger.info(f"  数据形状: {self.positive_data.shape}")
        logger.info(f"  数据增强: {augment} (倍数: {augment_factor})")
        logger.info(f"  归一化: {normalize}")
        
        # 验证正样本数量（允许一定范围的变化）
        expected_range = (900, 1200)  # 允许的正样本数量范围
        actual_count = len(self.positive_data)
        if not (expected_range[0] <= actual_count <= expected_range[1]):
            logger.warning(f"正样本数量超出预期范围 {expected_range}，实际{actual_count}个")

        logger.info(f"使用 {actual_count} 个正样本进行训练")
    
    def _load_data(self, data_paths: List[str]) -> Tuple[np.ndarray, np.ndarray]:
        """加载HTRU1数据（支持pickle格式）"""
        all_data = []
        all_labels = []

        for data_path in data_paths:
            if not os.path.exists(data_path):
                logger.warning(f"数据文件不存在: {data_path}")
                continue

            logger.info(f"加载数据文件: {data_path}")

            try:
                # 尝试加载pickle格式（HTRU1标准格式）
                with open(data_path, 'rb') as f:
                    # 尝试不同的pickle加载方式
                    try:
                        batch_dict = pickle.load(f, encoding='latin1')  # Python 2兼容
                    except:
                        f.seek(0)
                        batch_dict = pickle.load(f, encoding='bytes')

                    # 处理不同的键格式
                    data_key = None
                    labels_key = None

                    # 查找数据和标签键
                    for key in batch_dict.keys():
                        if str(key).lower() in ['data', "b'data'"] or key == b'data':
                            data_key = key
                        elif str(key).lower() in ['labels', "b'labels'"] or key == b'labels':
                            labels_key = key

                    if data_key is not None and labels_key is not None:
                        data = batch_dict[data_key]
                        labels = batch_dict[labels_key]

                        # 转换为numpy数组
                        if not isinstance(data, np.ndarray):
                            data = np.array(data)
                        if not isinstance(labels, np.ndarray):
                            labels = np.array(labels)

                        # 重塑数据: [N, 3072] → [N, 3, 32, 32]
                        if data.ndim == 2 and data.shape[1] == 3072:
                            data = data.reshape(-1, 3, 32, 32)
                        elif data.ndim == 4:
                            # 如果已经是4D，转换通道顺序
                            data = np.transpose(data, (0, 3, 1, 2))

                    else:
                        logger.warning(f"未找到数据或标签键: {list(batch_dict.keys())}")
                        continue

                    all_data.append(data)
                    all_labels.append(labels)

                    logger.info(f"  加载样本数: {len(data)}")
                    logger.info(f"  正样本数: {np.sum(labels == 0)}")

            except Exception as e:
                # 如果pickle失败，尝试h5py格式
                if HAS_H5PY:
                    try:
                        with h5py.File(data_path, 'r') as f:
                            data = f['data'][:]  # [N, 32, 32, 3]
                            labels = f['labels'][:]  # [N]

                            # 转换数据格式: [N, 32, 32, 3] → [N, 3, 32, 32]
                            data = np.transpose(data, (0, 3, 1, 2))

                            all_data.append(data)
                            all_labels.append(labels)

                            logger.info(f"  (h5py) 加载样本数: {len(data)}")
                            logger.info(f"  (h5py) 正样本数: {np.sum(labels == 0)}")

                    except Exception as e2:
                        logger.error(f"加载数据文件失败 {data_path}: pickle={e}, h5py={e2}")
                        continue
                else:
                    logger.error(f"加载数据文件失败 {data_path}: {e}")
                    continue

        if not all_data:
            logger.warning("没有成功加载任何数据文件，使用模拟数据")
            return self._create_mock_data()

        # 合并数据
        data = np.concatenate(all_data, axis=0)
        labels = np.concatenate(all_labels, axis=0)

        return data, labels

    def _create_mock_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """创建模拟HTRU1数据"""
        # 创建995个正样本和一些负样本
        total_samples = 2000
        positive_samples = 995

        data = np.random.randn(total_samples, 3, 32, 32).astype(np.float32)
        labels = np.ones(total_samples, dtype=np.int32)  # 默认负样本
        labels[:positive_samples] = 0  # 前995个为正样本

        # 为正样本添加一些结构
        for i in range(positive_samples):
            # Channel 0: 周期性结构
            x = np.linspace(0, 4*np.pi, 32)
            y = np.linspace(0, 4*np.pi, 32)
            X, Y = np.meshgrid(x, y)
            data[i, 0] += 0.3 * np.sin(X) * np.cos(Y)

            # Channel 1&2: 相位结构
            data[i, 1] += 0.2 * np.sin(2*X + Y)
            data[i, 2] += 0.2 * np.cos(X + 2*Y)

        # 归一化到合理范围
        data = np.tanh(data) * 100 + 126  # 模拟[24, 229]范围

        return data, labels
    
    def _normalize_data(self, data: np.ndarray) -> np.ndarray:
        """归一化数据到[-1,1]范围"""
        if not self.normalize:
            return data
            
        # HTRU1原始数据范围 [24, 229]
        data_min, data_max = 24.0, 229.0
        
        # 归一化到[-1, 1]
        normalized = 2.0 * (data - data_min) / (data_max - data_min) - 1.0
        
        # 确保范围
        normalized = np.clip(normalized, -1.0, 1.0)
        
        return normalized
    
    def _augment_sample(self, sample: np.ndarray) -> np.ndarray:
        """
        物理感知数据增强
        
        Args:
            sample: 输入样本 [3, 32, 32]
            
        Returns:
            augmented: 增强后样本 [3, 32, 32]
        """
        sample = torch.from_numpy(sample).float()
        
        # 随机选择增强策略
        augment_type = np.random.choice(['rotation', 'amplitude', 'noise'])
        
        if augment_type == 'rotation':
            # 保持周期性的旋转 (90度倍数)
            k = np.random.choice([0, 1, 2, 3])
            sample = torch.rot90(sample, k, dims=[1, 2])
            
        elif augment_type == 'amplitude':
            # 幅度缩放 (±10%)
            scale = np.random.uniform(0.9, 1.1)
            sample = sample * scale
            
        elif augment_type == 'noise':
            # 高斯噪声注入
            noise = torch.randn_like(sample) * 0.01
            sample = sample + noise
        
        # 确保范围
        sample = torch.clamp(sample, -1.0, 1.0)
        
        return sample.numpy()
    
    def __len__(self) -> int:
        """数据集大小"""
        base_size = len(self.positive_data)
        if self.augment:
            return base_size * self.augment_factor
        return base_size
    
    def __getitem__(self, idx: int) -> torch.Tensor:
        """获取单个样本"""
        # 映射到原始索引
        original_idx = idx % len(self.positive_data)
        sample = self.positive_data[original_idx].copy()
        
        # 归一化
        sample = self._normalize_data(sample)
        
        # 数据增强
        if self.augment and idx >= len(self.positive_data):
            sample = self._augment_sample(sample)
        
        return torch.from_numpy(sample).float()

def find_htru1_data_paths() -> List[str]:
    """
    查找HTRU1数据文件路径
    支持多个可能的数据位置和格式
    """
    possible_base_paths = [
        "/Pulsar/PulsarFenlei/data/htru1-batches-py",
        "/yanyb/jmy/Pulsar/PulsarFenlei/data/htru1-batches-py",
        "../data/htru1-batches-py",
        "data/htru1-batches-py",
        "./htru1-batches-py"
    ]

    found_paths = []

    for base_path in possible_base_paths:
        if os.path.exists(base_path):
            logger.info(f"检查数据目录: {base_path}")

            # 查找HTRU1 pickle格式的批次文件
            batch_files = []
            for i in range(1, 6):  # data_batch_1 到 data_batch_5
                batch_file = os.path.join(base_path, f"data_batch_{i}")
                if os.path.exists(batch_file):
                    batch_files.append(batch_file)
                    logger.info(f"  找到训练批次: data_batch_{i}")

            # 查找测试批次
            test_file = os.path.join(base_path, "test_batch")
            if os.path.exists(test_file):
                batch_files.append(test_file)
                logger.info(f"  找到测试批次: test_batch")

            # 如果找到批次文件，添加到路径列表
            if batch_files:
                found_paths.extend(batch_files)
                logger.info(f"  总共找到 {len(batch_files)} 个数据文件")
                break  # 找到一个有效目录就停止搜索

            # 备选：查找h5格式文件
            h5_files = []
            for filename in ["train_data.h5", "test_data.h5"]:
                h5_file = os.path.join(base_path, filename)
                if os.path.exists(h5_file):
                    h5_files.append(h5_file)
                    logger.info(f"  找到H5文件: {filename}")

            if h5_files:
                found_paths.extend(h5_files)
                logger.info(f"  总共找到 {len(h5_files)} 个H5文件")
                break

    if not found_paths:
        logger.warning("未找到HTRU1数据文件，将使用模拟数据")
    else:
        logger.info(f"成功找到 {len(found_paths)} 个HTRU1数据文件")

    return found_paths

def create_htru1_dataloader(batch_size: int = 16, augment: bool = True,
                           augment_factor: int = 2, num_workers: int = 4,
                           shuffle: bool = True) -> DataLoader:
    """
    创建HTRU1数据加载器
    
    Args:
        batch_size: 批次大小
        augment: 是否数据增强
        augment_factor: 增强倍数
        num_workers: 工作进程数
        shuffle: 是否打乱数据
        
    Returns:
        dataloader: 数据加载器
    """
    # 查找数据路径
    data_paths = find_htru1_data_paths()
    
    if not data_paths:
        # 创建模拟数据集用于测试
        logger.warning("使用模拟HTRU1数据集")
        return create_mock_dataloader(batch_size, shuffle)
    
    # 创建数据集
    dataset = HTRU1PulsarDataset(
        data_paths=data_paths,
        augment=augment,
        augment_factor=augment_factor
    )
    
    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=True  # 确保批次大小一致
    )
    
    logger.info(f"HTRU1数据加载器创建完成:")
    logger.info(f"  数据集大小: {len(dataset)}")
    logger.info(f"  批次大小: {batch_size}")
    logger.info(f"  批次数量: {len(dataloader)}")
    
    return dataloader

def create_mock_dataloader(batch_size: int = 16, shuffle: bool = True) -> DataLoader:
    """创建模拟数据加载器用于测试"""
    
    class MockHTRU1Dataset(Dataset):
        def __init__(self):
            # 模拟995个正样本
            self.size = 995 * 2  # 包含数据增强
            
        def __len__(self):
            return self.size
            
        def __getitem__(self, idx):
            # 生成模拟的脉冲星数据
            # 添加一些周期性和相位结构
            sample = torch.randn(3, 32, 32)
            
            # Channel 0: 添加周期性结构
            x = torch.linspace(0, 4*np.pi, 32)
            y = torch.linspace(0, 4*np.pi, 32)
            X, Y = torch.meshgrid(x, y, indexing='ij')
            sample[0] += 0.3 * torch.sin(X) * torch.cos(Y)
            
            # Channel 1&2: 添加相位结构
            sample[1] += 0.2 * torch.sin(2*X + Y)
            sample[2] += 0.2 * torch.cos(X + 2*Y)
            
            # 归一化到[-1, 1]
            sample = torch.tanh(sample)
            
            return sample
    
    dataset = MockHTRU1Dataset()
    dataloader = DataLoader(
        dataset, batch_size=batch_size, shuffle=shuffle, drop_last=True
    )
    
    logger.info(f"模拟HTRU1数据加载器创建完成 (大小: {len(dataset)})")
    
    return dataloader

def validate_dataloader(dataloader: DataLoader) -> Dict[str, float]:
    """
    验证数据加载器
    
    Args:
        dataloader: 数据加载器
        
    Returns:
        stats: 数据统计信息
    """
    logger.info("验证数据加载器...")
    
    # 获取一个批次
    batch = next(iter(dataloader))
    
    stats = {
        'batch_shape': list(batch.shape),
        'data_min': batch.min().item(),
        'data_max': batch.max().item(),
        'data_mean': batch.mean().item(),
        'data_std': batch.std().item(),
        'num_batches': len(dataloader)
    }
    
    logger.info(f"数据加载器验证结果:")
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")
    
    # 验证数据范围 (允许一定偏差)
    if not (-1.5 <= stats['data_min'] <= 0.5):
        logger.warning(f"数据最小值可能异常: {stats['data_min']}")
    if not (-0.5 <= stats['data_max'] <= 1.5):
        logger.warning(f"数据最大值可能异常: {stats['data_max']}")

    logger.info(f"数据范围检查: [{stats['data_min']:.3f}, {stats['data_max']:.3f}]")
    assert batch.shape[1:] == (3, 32, 32), f"数据形状异常: {batch.shape}"
    
    logger.info("✅ 数据加载器验证通过")
    
    return stats
