#!/usr/bin/env python3
"""
通道特定扩散强度设计
基于脉冲星三通道物理复杂度的差异化噪声强度
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional


class ChannelSpecificDiffusion:
    """
    通道特定扩散处理器
    
    基于脉冲星信号的物理复杂度设计差异化的扩散强度：
    - Period-DM (通道0): 最简单，低噪声强度
    - Phase-Subband (通道1): 中等复杂，中等噪声强度  
    - Phase-Subintegration (通道2): 最复杂，高噪声强度
    """
    
    def __init__(
        self,
        num_timesteps: int = 1000,
        # 通道特定的噪声强度因子
        channel_noise_factors: Tuple[float, float, float] = (0.8, 1.0, 1.2),
        # 通道特定的扩散速度因子
        channel_speed_factors: Tuple[float, float, float] = (0.9, 1.0, 1.1),
        device: str = "cuda"
    ):
        self.num_timesteps = num_timesteps
        self.channel_noise_factors = torch.tensor(channel_noise_factors, device=device)
        self.channel_speed_factors = torch.tensor(channel_speed_factors, device=device)
        self.device = device
        
        # 通道权重（基于物理重要性）
        self.channel_weights = torch.tensor([0.5, 0.3, 0.2], device=device)
        
        # 通道名称
        self.channel_names = ["Period-DM", "Phase-Subband", "Phase-Subintegration"]
        
    def apply_channel_specific_noise(
        self, 
        x: torch.Tensor, 
        noise: torch.Tensor, 
        timesteps: torch.Tensor,
        sqrt_alphas_cumprod: torch.Tensor,
        sqrt_one_minus_alphas_cumprod: torch.Tensor
    ) -> torch.Tensor:
        """
        应用通道特定的噪声强度
        
        Args:
            x: 原始图像 (B, 3, H, W)
            noise: 基础噪声 (B, 3, H, W)
            timesteps: 时间步 (B,)
            sqrt_alphas_cumprod: sqrt(alpha_cumprod) (num_timesteps,)
            sqrt_one_minus_alphas_cumprod: sqrt(1-alpha_cumprod) (num_timesteps,)
        Returns:
            通道特定加噪后的图像 (B, 3, H, W)
        """
        B, C, H, W = x.shape
        
        # 获取时间步对应的系数
        sqrt_alpha_t = sqrt_alphas_cumprod[timesteps].view(B, 1, 1, 1)
        sqrt_one_minus_alpha_t = sqrt_one_minus_alphas_cumprod[timesteps].view(B, 1, 1, 1)
        
        # 分别处理三个通道
        x_noisy_channels = []
        
        for c in range(3):
            # 获取当前通道的数据
            x_c = x[:, c:c+1]  # (B, 1, H, W)
            noise_c = noise[:, c:c+1]  # (B, 1, H, W)
            
            # 应用通道特定的噪声强度
            channel_noise_factor = self.channel_noise_factors[c]
            adjusted_noise = noise_c * channel_noise_factor
            
            # 应用通道特定的扩散速度
            channel_speed_factor = self.channel_speed_factors[c]
            
            # 调整扩散系数
            adjusted_sqrt_one_minus_alpha_t = sqrt_one_minus_alpha_t * channel_speed_factor
            
            # 确保系数在合理范围内
            adjusted_sqrt_one_minus_alpha_t = torch.clamp(adjusted_sqrt_one_minus_alpha_t, 0.0, 1.0)
            
            # 重新归一化以保持总能量
            total_coeff_sq = sqrt_alpha_t**2 + adjusted_sqrt_one_minus_alpha_t**2
            sqrt_alpha_t_normalized = sqrt_alpha_t / torch.sqrt(total_coeff_sq + 1e-8)
            sqrt_one_minus_alpha_t_normalized = adjusted_sqrt_one_minus_alpha_t / torch.sqrt(total_coeff_sq + 1e-8)
            
            # 应用扩散
            x_noisy_c = sqrt_alpha_t_normalized * x_c + sqrt_one_minus_alpha_t_normalized * adjusted_noise
            x_noisy_channels.append(x_noisy_c)
        
        # 拼接所有通道
        x_noisy = torch.cat(x_noisy_channels, dim=1)  # (B, 3, H, W)
        
        return x_noisy
        
    def compute_channel_specific_loss(
        self, 
        pred_noise: torch.Tensor, 
        target_noise: torch.Tensor,
        timesteps: torch.Tensor
    ) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算通道特定的扩散损失
        
        Args:
            pred_noise: 预测噪声 (B, 3, H, W)
            target_noise: 目标噪声 (B, 3, H, W)
            timesteps: 时间步 (B,)
        Returns:
            总损失和各通道损失字典
        """
        B, C, H, W = pred_noise.shape
        
        # 计算每个通道的损失
        channel_losses = []
        loss_dict = {}
        
        for c in range(3):
            # 获取当前通道的预测和目标
            pred_c = pred_noise[:, c]  # (B, H, W)
            target_c = target_noise[:, c]  # (B, H, W)
            
            # 计算MSE损失
            channel_loss = F.mse_loss(pred_c, target_c, reduction='mean')
            
            # 应用通道权重
            weighted_loss = channel_loss * self.channel_weights[c]
            channel_losses.append(weighted_loss)
            
            # 记录损失
            loss_dict[f'{self.channel_names[c]}_loss'] = channel_loss.item()
            loss_dict[f'{self.channel_names[c]}_weighted_loss'] = weighted_loss.item()
        
        # 总损失
        total_loss = sum(channel_losses)
        loss_dict['total_channel_loss'] = total_loss.item()
        
        return total_loss, loss_dict
        
    def get_channel_specific_timesteps(
        self, 
        base_timesteps: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        生成通道特定的时间步
        
        Args:
            base_timesteps: 基础时间步 (B,)
        Returns:
            三个通道的时间步 (B,), (B,), (B,)
        """
        B = base_timesteps.shape[0]
        
        # 为每个通道生成调整后的时间步
        period_dm_timesteps = (base_timesteps.float() * self.channel_speed_factors[0]).long()
        phase_subband_timesteps = (base_timesteps.float() * self.channel_speed_factors[1]).long()
        phase_subintegration_timesteps = (base_timesteps.float() * self.channel_speed_factors[2]).long()
        
        # 确保时间步在有效范围内
        period_dm_timesteps = torch.clamp(period_dm_timesteps, 0, self.num_timesteps - 1)
        phase_subband_timesteps = torch.clamp(phase_subband_timesteps, 0, self.num_timesteps - 1)
        phase_subintegration_timesteps = torch.clamp(phase_subintegration_timesteps, 0, self.num_timesteps - 1)
        
        return period_dm_timesteps, phase_subband_timesteps, phase_subintegration_timesteps
        
    def adaptive_noise_scheduling(
        self, 
        timesteps: torch.Tensor,
        channel_complexity_scores: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        自适应噪声调度
        
        Args:
            timesteps: 时间步 (B,)
            channel_complexity_scores: 通道复杂度分数 (B, 3) 或 None
        Returns:
            调整后的噪声强度因子 (B, 3)
        """
        B = timesteps.shape[0]
        
        # 基础噪声强度因子
        base_factors = self.channel_noise_factors.unsqueeze(0).repeat(B, 1)  # (B, 3)
        
        if channel_complexity_scores is not None:
            # 根据复杂度分数动态调整
            # 复杂度越高，噪声强度越大
            complexity_adjustment = 0.1 * (channel_complexity_scores - 0.5)  # 范围 [-0.05, 0.05]
            adjusted_factors = base_factors + complexity_adjustment
        else:
            adjusted_factors = base_factors
        
        # 根据时间步调整噪声强度
        # 早期时间步（高噪声）使用更强的通道差异
        # 后期时间步（低噪声）减少通道差异
        time_factor = timesteps.float() / self.num_timesteps  # (B,)
        time_factor = time_factor.unsqueeze(1)  # (B, 1)
        
        # 时间调制：早期差异大，后期差异小
        time_modulation = 0.5 + 0.5 * time_factor  # 范围 [0.5, 1.0]
        
        # 应用时间调制
        final_factors = 1.0 + (adjusted_factors - 1.0) * time_modulation
        
        # 确保因子在合理范围内
        final_factors = torch.clamp(final_factors, 0.5, 2.0)
        
        return final_factors
        
    def get_diffusion_info(self) -> Dict[str, any]:
        """获取扩散配置信息"""
        return {
            'num_timesteps': self.num_timesteps,
            'channel_noise_factors': self.channel_noise_factors.cpu().tolist(),
            'channel_speed_factors': self.channel_speed_factors.cpu().tolist(),
            'channel_weights': self.channel_weights.cpu().tolist(),
            'channel_names': self.channel_names,
            'design_principle': {
                'Period-DM': 'Low noise (simple structure)',
                'Phase-Subband': 'Medium noise (moderate complexity)',
                'Phase-Subintegration': 'High noise (complex temporal features)'
            }
        }
