#!/usr/bin/env python3
"""
简化版CNN-based U-Net架构
解决通道数匹配问题的稳定版本
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Tuple

try:
    from .channel_specific_processing import ChannelSpecificProcessor
except ImportError:
    from channel_specific_processing import ChannelSpecificProcessor

class SimpleResNetBlock(nn.Module):
    """简化的ResNet块"""
    
    def __init__(self, 
                 in_channels: int,
                 out_channels: int,
                 time_emb_dim: int,
                 dropout: float = 0.1):
        super().__init__()
        
        # 时间嵌入投影
        self.time_mlp = nn.Sequential(
            nn.SiLU(),
            nn.Linear(time_emb_dim, out_channels)
        )
        
        # 卷积块
        self.conv1 = nn.Sequential(
            nn.GroupNorm(min(8, in_channels), in_channels),
            nn.SiLU(),
            nn.Conv2d(in_channels, out_channels, 3, padding=1)
        )
        
        self.conv2 = nn.Sequential(
            nn.GroupNorm(min(8, out_channels), out_channels),
            nn.<PERSON>L<PERSON>(),
            nn.Dropout(dropout),
            nn.Conv2d(out_channels, out_channels, 3, padding=1)
        )
        
        # 残差连接
        if in_channels != out_channels:
            self.residual_conv = nn.Conv2d(in_channels, out_channels, 1)
        else:
            self.residual_conv = nn.Identity()
    
    def forward(self, x: torch.Tensor, time_emb: torch.Tensor) -> torch.Tensor:
        residual = self.residual_conv(x)
        
        h = self.conv1(x)
        
        # 添加时间嵌入
        time_emb = self.time_mlp(time_emb)[:, :, None, None]
        h = h + time_emb
        
        h = self.conv2(h)
        
        return h + residual

class SimplifiedCNNUNet(nn.Module):
    """
    简化版CNN-based U-Net
    
    解决通道数匹配问题，确保稳定性
    """
    
    def __init__(self,
                 in_channels: int = 3,
                 out_channels: int = 3,
                 base_channels: int = 64,
                 time_emb_dim: int = 128,
                 dropout: float = 0.1):
        """
        初始化简化版CNN U-Net
        
        Args:
            in_channels: 输入通道数
            out_channels: 输出通道数
            base_channels: 基础通道数
            time_emb_dim: 时间嵌入维度
            dropout: Dropout概率
        """
        super().__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.base_channels = base_channels
        self.time_emb_dim = time_emb_dim
        
        # 通道特定处理器
        self.channel_processor = ChannelSpecificProcessor(
            input_channels=in_channels,
            base_channels=base_channels,
            adaptive_weights=True
        )
        
        # 时间嵌入
        self.time_embedding = nn.Sequential(
            nn.Linear(base_channels, time_emb_dim),
            nn.SiLU(),
            nn.Linear(time_emb_dim, time_emb_dim)
        )
        
        # 编码器
        self.enc1 = SimpleResNetBlock(base_channels, base_channels, time_emb_dim, dropout)
        self.enc2 = SimpleResNetBlock(base_channels, base_channels * 2, time_emb_dim, dropout)
        self.enc3 = SimpleResNetBlock(base_channels * 2, base_channels * 4, time_emb_dim, dropout)
        
        # 下采样
        self.down1 = nn.Conv2d(base_channels, base_channels, 3, stride=2, padding=1)
        self.down2 = nn.Conv2d(base_channels * 2, base_channels * 2, 3, stride=2, padding=1)
        
        # 中间层
        self.mid = SimpleResNetBlock(base_channels * 4, base_channels * 4, time_emb_dim, dropout)
        
        # 解码器
        self.up1 = nn.ConvTranspose2d(base_channels * 4, base_channels * 2, 4, stride=2, padding=1)
        self.dec1 = SimpleResNetBlock(base_channels * 4, base_channels * 2, time_emb_dim, dropout)
        
        self.up2 = nn.ConvTranspose2d(base_channels * 2, base_channels, 4, stride=2, padding=1)
        self.dec2 = SimpleResNetBlock(base_channels * 2, base_channels, time_emb_dim, dropout)
        
        # 输出层
        self.output_conv = nn.Sequential(
            nn.GroupNorm(min(8, base_channels), base_channels),
            nn.SiLU(),
            nn.Conv2d(base_channels, out_channels, 3, padding=1)
        )
    
    def forward(self, x: torch.Tensor, timesteps: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入图像 (B, C, H, W)
            timesteps: 时间步 (B,)
            
        Returns:
            预测的噪声 (B, C, H, W)
        """
        # 通道特定处理
        h = self.channel_processor(x)
        
        # 时间嵌入
        time_emb = self.get_time_embedding(timesteps)
        time_emb = self.time_embedding(time_emb)
        
        # 编码器路径
        # Level 1: 32x32
        h1 = self.enc1(h, time_emb)
        h1_down = self.down1(h1)
        
        # Level 2: 16x16
        h2 = self.enc2(h1_down, time_emb)
        h2_down = self.down2(h2)
        
        # Level 3: 8x8
        h3 = self.enc3(h2_down, time_emb)
        
        # 中间层
        h_mid = self.mid(h3, time_emb)
        
        # 解码器路径
        # Level 2: 16x16
        h_up1 = self.up1(h_mid)
        h_cat1 = torch.cat([h_up1, h2], dim=1)
        h_dec1 = self.dec1(h_cat1, time_emb)
        
        # Level 1: 32x32
        h_up2 = self.up2(h_dec1)
        h_cat2 = torch.cat([h_up2, h1], dim=1)
        h_dec2 = self.dec2(h_cat2, time_emb)
        
        # 输出
        output = self.output_conv(h_dec2)
        
        return output
    
    def get_time_embedding(self, timesteps: torch.Tensor) -> torch.Tensor:
        """生成时间嵌入"""
        half_dim = self.base_channels // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=timesteps.device) * -emb)
        emb = timesteps[:, None] * emb[None, :]
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=-1)
        return emb

def test_simplified_cnn_unet():
    """测试简化版CNN U-Net"""
    print("🧪 测试简化版CNN U-Net")
    
    # 创建模型
    model = SimplifiedCNNUNet(
        in_channels=3,
        out_channels=3,
        base_channels=64,
        time_emb_dim=128,
        dropout=0.1
    )
    
    # 计算参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    print(f"参数量 (MB): {total_params * 4 / 1024 / 1024:.2f}")
    
    # 测试前向传播
    batch_size = 4
    x = torch.randn(batch_size, 3, 32, 32)
    timesteps = torch.randint(0, 1000, (batch_size,))
    
    with torch.no_grad():
        output = model(x, timesteps)
    
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"时间步形状: {timesteps.shape}")
    
    # 验证输出形状
    assert output.shape == x.shape, f"输出形状不匹配: {output.shape} vs {x.shape}"
    
    # 测试梯度流
    model.train()
    output = model(x, timesteps)
    loss = output.mean()
    loss.backward()
    
    # 验证梯度存在
    has_gradients = any(p.grad is not None and torch.abs(p.grad).sum() > 0 
                       for p in model.parameters())
    assert has_gradients, "梯度未正确传播"
    
    print("✅ 简化版CNN U-Net测试通过")
    
    return model

if __name__ == "__main__":
    model = test_simplified_cnn_unet()
