#!/usr/bin/env python3
"""
WGAN-GP判别器模块
PulsarWGAN-VAE混合架构的WGAN-GP判别器组件

基于WGAN-GP+VAE实施计划的技术规格：
- 判别器: 900K参数，多尺度架构
- 梯度惩罚: λ=10 (适合小样本)
- 输入: 32x32x3 → 输出: Wasserstein距离评分
- 特殊设计: InstanceNorm, 无最后激活函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.autograd as autograd
from typing import Tuple, Dict

class PulsarWGANDiscriminator(nn.Module):
    """
    脉冲星WGAN-GP判别器 (多尺度架构)
    
    架构规格:
    - 输入: 32x32x3 → 输出: Wasserstein距离评分
    - 参数量: ~900K
    - 特殊设计: InstanceNorm, 多尺度特征提取, 无最后激活函数
    """
    
    def __init__(self, input_channels: int = 3):
        super().__init__()
        self.input_channels = input_channels
        
        # 轻量化主判别器路径 - 4层CNN (移除inplace操作)
        self.main_path = nn.Sequential(
            # Layer 1: 32x32x3 → 16x16x32
            nn.Conv2d(input_channels, 32, kernel_size=4, stride=2, padding=1),
            nn.LeakyReLU(0.2),

            # Layer 2: 16x16x32 → 8x8x64
            nn.Conv2d(32, 64, kernel_size=4, stride=2, padding=1),
            nn.InstanceNorm2d(64),  # WGAN-GP推荐使用InstanceNorm
            nn.LeakyReLU(0.2),

            # Layer 3: 8x8x64 → 4x4x128
            nn.Conv2d(64, 128, kernel_size=4, stride=2, padding=1),
            nn.InstanceNorm2d(128),
            nn.LeakyReLU(0.2),

            # Layer 4: 4x4x128 → 2x2x256
            nn.Conv2d(128, 256, kernel_size=4, stride=2, padding=1),
            nn.InstanceNorm2d(256),
            nn.LeakyReLU(0.2)
        )
        
        # 多尺度分支处理 (轻量化，移除inplace操作)
        # 16x16分辨率分支 (周期性特征)
        self.branch_16x16 = nn.Sequential(
            nn.Conv2d(32, 16, kernel_size=3, stride=1, padding=1),
            nn.InstanceNorm2d(16),
            nn.LeakyReLU(0.2),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(16, 32)
        )

        # 8x8分辨率分支 (相位特征)
        self.branch_8x8 = nn.Sequential(
            nn.Conv2d(64, 32, kernel_size=3, stride=1, padding=1),
            nn.InstanceNorm2d(32),
            nn.LeakyReLU(0.2),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(32, 32)
        )

        # 4x4分辨率分支 (幅度特征)
        self.branch_4x4 = nn.Sequential(
            nn.Conv2d(128, 64, kernel_size=3, stride=1, padding=1),
            nn.InstanceNorm2d(64),
            nn.LeakyReLU(0.2),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(64, 32)
        )

        # 最终分类层 (轻量化，移除inplace操作)
        self.classifier = nn.Sequential(
            nn.Flatten(),
            nn.Linear(2*2*256 + 32 + 32 + 32, 128),  # 主路径 + 三个分支
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(128, 1)  # 无激活函数 (WGAN要求)
        )
        
        # 初始化权重
        self._initialize_weights()
        
        # 统计参数量
        self.total_params = sum(p.numel() for p in self.parameters())
        print(f"PulsarWGANDiscriminator总参数量: {self.total_params/1e6:.2f}M")
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='leaky_relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.InstanceNorm2d):
                # InstanceNorm2d默认affine=False，没有weight和bias参数
                if hasattr(m, 'weight') and m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if hasattr(m, 'bias') and m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='leaky_relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入图像 [batch_size, 3, 32, 32]

        Returns:
            score: Wasserstein距离评分 [batch_size, 1]
        """
        # 主路径前向传播，同时提取多尺度特征
        features = []
        h = x

        for i, layer in enumerate(self.main_path):
            h = layer(h)

            # 在特定层提取多尺度特征 (修正层索引)
            if i == 1:  # 16x16x32 after layer 1
                branch_16_feat = self.branch_16x16(h)
                features.append(branch_16_feat)
            elif i == 3:  # 8x8x64 after layer 2
                branch_8_feat = self.branch_8x8(h)
                features.append(branch_8_feat)
            elif i == 5:  # 4x4x128 after layer 3
                branch_4_feat = self.branch_4x4(h)
                features.append(branch_4_feat)

        # 主路径最终特征 (2x2x256)
        main_feat = h.flatten(1)  # [batch_size, 1024]

        # 融合所有特征
        all_features = torch.cat([main_feat] + features, dim=1)

        # 最终分类
        score = self.classifier(all_features)

        return score

def compute_gradient_penalty(discriminator: nn.Module, real_samples: torch.Tensor, 
                           fake_samples: torch.Tensor, device: torch.device,
                           lambda_gp: float = 10.0) -> torch.Tensor:
    """
    计算WGAN-GP梯度惩罚
    
    Args:
        discriminator: 判别器模型
        real_samples: 真实样本 [batch_size, 3, 32, 32]
        fake_samples: 生成样本 [batch_size, 3, 32, 32]
        device: 设备
        lambda_gp: 梯度惩罚系数 (推荐10.0适合小样本)
        
    Returns:
        gradient_penalty: 梯度惩罚损失
    """
    batch_size = real_samples.size(0)
    
    # 随机插值系数
    epsilon = torch.rand(batch_size, 1, 1, 1, device=device)
    epsilon = epsilon.expand_as(real_samples)
    
    # 插值样本
    interpolated = epsilon * real_samples + (1 - epsilon) * fake_samples
    interpolated.requires_grad_(True)
    
    # 计算插值样本的判别器输出
    d_interpolated = discriminator(interpolated)
    
    # 计算梯度
    gradients = autograd.grad(
        outputs=d_interpolated,
        inputs=interpolated,
        grad_outputs=torch.ones_like(d_interpolated),
        create_graph=True,
        retain_graph=True,
        only_inputs=True
    )[0]
    
    # 计算梯度范数
    gradients = gradients.view(batch_size, -1)
    gradient_norm = gradients.norm(2, dim=1)
    
    # 梯度惩罚
    gradient_penalty = lambda_gp * ((gradient_norm - 1) ** 2).mean()
    
    return gradient_penalty

def compute_wgan_gp_loss(discriminator: nn.Module, real_samples: torch.Tensor,
                        fake_samples: torch.Tensor, device: torch.device,
                        lambda_gp: float = 10.0) -> Dict[str, torch.Tensor]:
    """
    计算完整的WGAN-GP损失
    
    Args:
        discriminator: 判别器模型
        real_samples: 真实样本 [batch_size, 3, 32, 32]
        fake_samples: 生成样本 [batch_size, 3, 32, 32]
        device: 设备
        lambda_gp: 梯度惩罚系数
        
    Returns:
        loss_dict: 包含各项损失的字典
    """
    # 判别器对真实和生成样本的评分
    d_real = discriminator(real_samples)
    d_fake = discriminator(fake_samples)
    
    # Wasserstein距离 (对抗损失)
    wasserstein_distance = torch.mean(d_fake) - torch.mean(d_real)
    
    # 梯度惩罚
    gradient_penalty = compute_gradient_penalty(
        discriminator, real_samples, fake_samples, device, lambda_gp
    )
    
    # 总损失 (判别器要最小化)
    d_loss = wasserstein_distance + gradient_penalty
    
    # 生成器损失 (要最大化判别器对生成样本的评分)
    g_loss = -torch.mean(d_fake)
    
    return {
        'd_loss': d_loss,
        'g_loss': g_loss,
        'wasserstein_distance': wasserstein_distance,
        'gradient_penalty': gradient_penalty,
        'd_real_score': torch.mean(d_real),
        'd_fake_score': torch.mean(d_fake)
    }

class WGANGPTrainer:
    """
    WGAN-GP训练器辅助类
    处理判别器和生成器的交替训练
    """
    
    def __init__(self, discriminator: nn.Module, generator: nn.Module,
                 d_optimizer: torch.optim.Optimizer, g_optimizer: torch.optim.Optimizer,
                 device: torch.device, lambda_gp: float = 10.0, n_critic: int = 5):
        self.discriminator = discriminator
        self.generator = generator
        self.d_optimizer = d_optimizer
        self.g_optimizer = g_optimizer
        self.device = device
        self.lambda_gp = lambda_gp
        self.n_critic = n_critic  # 判别器训练频率
    
    def train_discriminator(self, real_samples: torch.Tensor, 
                          fake_samples: torch.Tensor) -> Dict[str, float]:
        """训练判别器"""
        self.discriminator.train()
        self.d_optimizer.zero_grad()
        
        # 计算损失
        loss_dict = compute_wgan_gp_loss(
            self.discriminator, real_samples, fake_samples, 
            self.device, self.lambda_gp
        )
        
        # 反向传播
        loss_dict['d_loss'].backward()
        self.d_optimizer.step()
        
        # 返回标量值
        return {k: v.item() if torch.is_tensor(v) else v for k, v in loss_dict.items()}
    
    def train_generator(self, fake_samples: torch.Tensor) -> Dict[str, float]:
        """训练生成器"""
        self.discriminator.eval()
        self.g_optimizer.zero_grad()
        
        # 生成器损失
        d_fake = self.discriminator(fake_samples)
        g_loss = -torch.mean(d_fake)
        
        # 反向传播
        g_loss.backward()
        self.g_optimizer.step()
        
        return {'g_loss': g_loss.item()}
