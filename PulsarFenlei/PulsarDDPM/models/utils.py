import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Any, Callable


def extract(a: torch.Tensor, t: torch.Tensor, x_shape: torch.Size) -> torch.Tensor:
    """
    Extract values from a at indices t and reshape to match x_shape.

    BUGFIX: Ensure device consistency to prevent cuda:0 vs cpu errors.

    Args:
        a (torch.Tensor): Tensor to extract values from
        t (torch.Tensor): Indices to extract
        x_shape (torch.Size): Shape to reshape extracted values to

    Returns:
        torch.Tensor: Extracted values reshaped to match x_shape
    """
    batch_size = t.shape[0]

    # BUGFIX: Ensure both tensors are on the same device
    if a.device != t.device:
        # Move 'a' to the same device as 't' (which is typically the input device)
        a = a.to(t.device)

    out = a.gather(-1, t)
    return out.reshape(batch_size, *((1,) * (len(x_shape) - 1)))


def get_adaptive_groups(num_channels: int) -> int:
    """
    OPTIMIZATION 1.4: Calculate optimal number of groups for GroupNorm based on channel count.

    This function implements a dynamic strategy for pulsar data:
    - Low channels (≤32): Use fewer groups to avoid over-normalization
    - Medium channels (32-128): Use moderate groups for balance
    - High channels (≥128): Use more groups for fine-grained normalization

    Args:
        num_channels (int): Number of channels

    Returns:
        int: Optimal number of groups
    """
    # Ensure at least 1 channel
    num_channels = max(1, num_channels)

    # Dynamic group calculation based on channel count
    if num_channels <= 32:
        # For low channel counts, use fewer groups (better for pulsar data)
        target_groups = min(8, num_channels)
    elif num_channels <= 128:
        # For medium channel counts, use moderate groups
        target_groups = min(16, num_channels)
    elif num_channels <= 512:
        # For high channel counts, use more groups
        target_groups = min(32, num_channels)
    else:
        # For very high channel counts, use maximum groups
        target_groups = min(64, num_channels)

    # Find the largest divisor of num_channels that is <= target_groups
    for groups in range(target_groups, 0, -1):
        if num_channels % groups == 0:
            return groups

    # Fallback: return 1 if no divisor found (should never happen)
    return 1


def get_norm(norm_type: str, num_channels: int, num_groups: int = None) -> nn.Module:
    """
    Get normalization layer with dynamic GroupNorm optimization for pulsar data.

    Args:
        norm_type (str): Type of normalization (only 'gn' is supported now)
        num_channels (int): Number of channels
        num_groups (int, optional): Number of groups for GroupNorm. If None, uses adaptive calculation.

    Returns:
        nn.Module: Optimized GroupNorm layer
    """
    # Only support GroupNorm for simplified and stable training
    if norm_type == "gn" or norm_type is None:
        # OPTIMIZATION 1.4: Use adaptive groups if not specified
        if num_groups is None:
            num_groups = get_adaptive_groups(num_channels)
        else:
            # Ensure provided num_groups is valid
            num_channels = max(1, num_channels)
            num_groups = min(num_groups, num_channels)

            # Ensure channels are divisible by groups
            if num_channels % num_groups != 0:
                # Find the largest divisor of num_channels that is <= num_groups
                for i in range(num_groups, 0, -1):
                    if num_channels % i == 0:
                        num_groups = i
                        break

            # Ensure at least one group
            num_groups = max(1, num_groups)

        return nn.GroupNorm(num_groups, num_channels)
    else:
        # For backward compatibility, fall back to GroupNorm with a warning
        print(f"Warning: norm_type '{norm_type}' is no longer supported. Using GroupNorm instead.")
        return get_norm("gn", num_channels, num_groups)


def generate_cosine_schedule(timesteps: int, s: float = 0.008) -> np.ndarray:
    """
    Generate cosine noise schedule as proposed in the improved DDPM paper.

    Args:
        timesteps (int): Number of timesteps
        s (float): Small offset for the cosine function

    Returns:
        np.ndarray: Beta schedule for diffusion
    """
    def f(t, timesteps):
        return (np.cos((t / timesteps + s) / (1 + s) * np.pi / 2)) ** 2

    alphas = []
    f0 = f(0, timesteps)

    for t in range(timesteps + 1):
        alphas.append(f(t, timesteps) / f0)

    betas = []

    for t in range(1, timesteps + 1):
        betas.append(min(1 - alphas[t] / alphas[t - 1], 0.999))

    return np.array(betas)


def generate_linear_schedule(timesteps: int, beta_start: float = 1e-4, beta_end: float = 0.02) -> np.ndarray:
    """
    Generate linear noise schedule.

    Args:
        timesteps (int): Number of timesteps
        beta_start (float): Starting beta value
        beta_end (float): Ending beta value

    Returns:
        np.ndarray: Beta schedule for diffusion
    """
    return np.linspace(beta_start, beta_end, timesteps)


def update_ema(target_params: List[nn.Parameter], source_params: List[nn.Parameter], rate: float = 0.999) -> None:
    """
    Update target parameters with EMA of source parameters.

    Args:
        target_params (List[nn.Parameter]): Target parameters to update
        source_params (List[nn.Parameter]): Source parameters
        rate (float): EMA rate
    """
    for target, source in zip(target_params, source_params):
        target.data.mul_(rate).add_(source.data, alpha=1 - rate)


def cycle(dl: torch.utils.data.DataLoader) -> Any:
    """
    Cycle through a dataloader indefinitely.

    Args:
        dl (DataLoader): DataLoader to cycle through

    Yields:
        Any: Next batch from the dataloader
    """
    while True:
        for data in dl:
            yield data


def setup_logging(log_dir: str) -> None:
    """
    Setup logging directory.

    Args:
        log_dir (str): Directory to save logs
    """
    import os
    os.makedirs(log_dir, exist_ok=True)


def get_activation(activation_name: str) -> Callable:
    """
    Get activation function by name.

    Args:
        activation_name (str): Name of activation function

    Returns:
        Callable: Activation function
    """
    activations = {
        "relu": F.relu,
        "silu": F.silu,
        "swish": F.silu,
        "mish": F.mish,
        "gelu": F.gelu,
        "leakyrelu": F.leaky_relu,
    }

    if activation_name not in activations:
        raise ValueError(f"Unknown activation function: {activation_name}")

    return activations[activation_name]


def validate_model_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate model parameters for compatibility.

    Args:
        params (Dict[str, Any]): Model parameters

    Returns:
        Dict[str, Any]: Validated and potentially adjusted parameters
    """
    # Create a copy of the parameters to avoid modifying the original
    validated_params = params.copy()

    # Check if base_channels is divisible by num_groups for GroupNorm
    if 'base_channels' in params and 'num_groups' in params and params.get('norm', 'gn') == 'gn':
        base_channels = params['base_channels']
        num_groups = params['num_groups']

        if base_channels % num_groups != 0:
            # Find the largest divisor of base_channels that is <= num_groups
            for i in range(num_groups, 0, -1):
                if base_channels % i == 0:
                    print(f"Warning: base_channels ({base_channels}) is not divisible by num_groups ({num_groups}). "
                          f"Adjusting num_groups to {i}.")
                    validated_params['num_groups'] = i
                    break

    return validated_params
