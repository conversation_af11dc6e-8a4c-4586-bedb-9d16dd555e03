#!/usr/bin/env python3
"""
WGAN-GP+VAE混合架构主模型
集成VAE和WGAN-GP判别器的完整生成模型

基于WGAN-GP+VAE实施计划的技术规格：
- VAE: 0.56M参数 (编码器240K + 解码器320K)
- WGAN-GP判别器: 0.93M参数 (多尺度架构)
- 总参数: 1.49M (优于原计划1.8M)
- 渐进式训练: 三阶段训练策略
"""

import torch
import torch.nn as nn
from typing import Dict, Tuple, Optional
from .vae import PulsarVAE
from .discriminator import PulsarWGANDiscriminator
import sys
import os
# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from losses.combined_loss import PulsarCombinedLoss, ProgressiveLossScheduler, create_loss_function

class PulsarWGANVAE(nn.Module):
    """
    脉冲星WGAN-GP+VAE混合架构
    
    集成组件：
    - VAE: 编码器 + 解码器 + 重参数化
    - WGAN-GP判别器: 多尺度判别 + 梯度惩罚
    - 组合损失函数: VAE + WGAN-GP + 物理约束
    """
    
    def __init__(self, latent_dim: int = 64, input_channels: int = 3):
        super().__init__()
        self.latent_dim = latent_dim
        self.input_channels = input_channels
        
        # 核心组件
        self.vae = PulsarVAE(latent_dim, input_channels)
        self.discriminator = PulsarWGANDiscriminator(input_channels)
        
        # 统计总参数量
        vae_params = sum(p.numel() for p in self.vae.parameters())
        discriminator_params = sum(p.numel() for p in self.discriminator.parameters())
        total_params = vae_params + discriminator_params
        
        print(f"PulsarWGANVAE混合架构初始化:")
        print(f"  VAE参数量: {vae_params/1e6:.2f}M")
        print(f"  判别器参数量: {discriminator_params/1e6:.2f}M")
        print(f"  总参数量: {total_params/1e6:.2f}M")
        print(f"  参数/样本比: {total_params/995:.0f}:1 (995个HTRU1正样本)")
        
        self.total_params = total_params
    
    def encode(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        编码输入图像到潜在空间
        
        Args:
            x: 输入图像 [batch_size, 3, 32, 32]
            
        Returns:
            mu: 均值 [batch_size, latent_dim]
            logvar: 对数方差 [batch_size, latent_dim]
        """
        return self.vae.encoder(x)
    
    def decode(self, z: torch.Tensor) -> torch.Tensor:
        """
        从潜在空间解码生成图像
        
        Args:
            z: 潜在向量 [batch_size, latent_dim]
            
        Returns:
            x_recon: 重建图像 [batch_size, 3, 32, 32]
        """
        return self.vae.decoder(z)
    
    def reparameterize(self, mu: torch.Tensor, logvar: torch.Tensor) -> torch.Tensor:
        """重参数化技巧"""
        return self.vae.reparameterize(mu, logvar)
    
    def discriminate(self, x: torch.Tensor) -> torch.Tensor:
        """
        判别器评分
        
        Args:
            x: 输入图像 [batch_size, 3, 32, 32]
            
        Returns:
            score: Wasserstein距离评分 [batch_size, 1]
        """
        return self.discriminator(x)
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        完整前向传播
        
        Args:
            x: 输入图像 [batch_size, 3, 32, 32]
            
        Returns:
            output_dict: 包含所有输出的字典
        """
        # VAE前向传播
        x_recon, mu, logvar = self.vae(x)
        
        # 判别器评分
        d_real_score = self.discriminator(x)
        d_fake_score = self.discriminator(x_recon)
        
        return {
            'x_recon': x_recon,
            'mu': mu,
            'logvar': logvar,
            'd_real_score': d_real_score,
            'd_fake_score': d_fake_score
        }
    
    def sample(self, num_samples: int, device: torch.device) -> torch.Tensor:
        """
        从先验分布采样生成新样本
        
        Args:
            num_samples: 生成样本数量
            device: 设备
            
        Returns:
            samples: 生成的样本 [num_samples, 3, 32, 32]
        """
        return self.vae.sample(num_samples, device)
    
    def reconstruct(self, x: torch.Tensor) -> torch.Tensor:
        """
        重建输入图像
        
        Args:
            x: 输入图像 [batch_size, 3, 32, 32]
            
        Returns:
            x_recon: 重建图像 [batch_size, 3, 32, 32]
        """
        with torch.no_grad():
            x_recon, _, _ = self.vae(x)
        return x_recon
    
    def get_vae_parameters(self):
        """获取VAE参数 (用于优化器)"""
        return self.vae.parameters()
    
    def get_discriminator_parameters(self):
        """获取判别器参数 (用于优化器)"""
        return self.discriminator.parameters()
    
    def set_training_mode(self, vae_training: bool = True, discriminator_training: bool = True):
        """
        设置训练模式
        
        Args:
            vae_training: VAE是否训练模式
            discriminator_training: 判别器是否训练模式
        """
        if vae_training:
            self.vae.train()
        else:
            self.vae.eval()
            
        if discriminator_training:
            self.discriminator.train()
        else:
            self.discriminator.eval()

class PulsarWGANVAETrainer:
    """
    WGAN-GP+VAE混合架构训练器
    
    实现渐进式三阶段训练：
    1. VAE预训练 (50轮)
    2. WGAN-GP集成 (40轮)
    3. 联合优化 (60轮)
    """
    
    def __init__(self, model: PulsarWGANVAE, device: torch.device,
                 vae_lr: float = 2e-4, discriminator_lr: float = 4e-4,
                 beta_vae: float = 1.0, lambda_gp: float = 10.0,
                 n_critic: int = 5):
        self.model = model
        self.device = device
        self.n_critic = n_critic
        
        # 优化器
        self.vae_optimizer = torch.optim.AdamW(
            model.get_vae_parameters(), 
            lr=vae_lr, betas=(0.5, 0.999), weight_decay=1e-4
        )
        self.discriminator_optimizer = torch.optim.AdamW(
            model.get_discriminator_parameters(),
            lr=discriminator_lr, betas=(0.5, 0.999), weight_decay=1e-4
        )
        
        # 损失函数和调度器
        self.loss_function, self.loss_scheduler = create_loss_function(
            stage=1, beta_vae=beta_vae, lambda_gp=lambda_gp
        )
        
        # 训练状态
        self.current_stage = 1
        self.epoch = 0
        
        print(f"PulsarWGANVAETrainer初始化:")
        print(f"  VAE学习率: {vae_lr}")
        print(f"  判别器学习率: {discriminator_lr}")
        print(f"  β-VAE: {beta_vae}")
        print(f"  λ-GP: {lambda_gp}")
        print(f"  判别器训练频率: {n_critic}")
    
    def set_stage(self, stage: int):
        """设置训练阶段"""
        self.current_stage = stage
        self.loss_scheduler.set_stage(stage)
        self.loss_scheduler.update_loss_function(self.loss_function)
        
        # 根据阶段调整训练模式
        if stage == 1:  # VAE预训练
            self.model.set_training_mode(vae_training=True, discriminator_training=False)
        elif stage == 2:  # WGAN-GP集成
            self.model.set_training_mode(vae_training=True, discriminator_training=True)
        elif stage == 3:  # 联合优化
            self.model.set_training_mode(vae_training=True, discriminator_training=True)
    
    def train_step(self, real_batch: torch.Tensor) -> Dict[str, float]:
        """
        单步训练
        
        Args:
            real_batch: 真实样本批次 [batch_size, 3, 32, 32]
            
        Returns:
            metrics: 训练指标字典
        """
        batch_size = real_batch.size(0)
        real_batch = real_batch.to(self.device)
        
        # VAE前向传播
        x_recon, mu, logvar = self.model.vae(real_batch)
        
        metrics = {}
        
        # 根据训练阶段执行不同的训练逻辑
        if self.current_stage == 1:  # VAE预训练
            metrics.update(self._train_vae_only(real_batch, x_recon, mu, logvar))
            
        elif self.current_stage in [2, 3]:  # WGAN-GP集成或联合优化
            # 训练判别器 (n_critic次)
            for _ in range(self.n_critic):
                d_metrics = self._train_discriminator(real_batch, x_recon.detach())
                
            # 训练生成器 (VAE)
            g_metrics = self._train_generator(real_batch, x_recon, mu, logvar)
            
            metrics.update(d_metrics)
            metrics.update(g_metrics)
        
        return metrics
    
    def _train_vae_only(self, real_batch: torch.Tensor, x_recon: torch.Tensor,
                       mu: torch.Tensor, logvar: torch.Tensor) -> Dict[str, float]:
        """VAE预训练"""
        self.vae_optimizer.zero_grad()
        
        # 计算VAE损失
        vae_loss_dict = self.loss_function.compute_vae_loss_component(
            x_recon, real_batch, mu, logvar
        )
        
        # 反向传播
        vae_loss_dict['weighted_vae_loss'].backward()
        self.vae_optimizer.step()
        
        return {k: v.item() if torch.is_tensor(v) else v for k, v in vae_loss_dict.items()}
    
    def _train_discriminator(self, real_batch: torch.Tensor, 
                           fake_batch: torch.Tensor) -> Dict[str, float]:
        """训练判别器"""
        self.discriminator_optimizer.zero_grad()
        
        # 计算判别器损失
        d_loss_dict = self.loss_function.compute_discriminator_loss(
            self.model.discriminator, real_batch, fake_batch, self.device
        )
        
        # 反向传播
        d_loss_dict['discriminator_total_loss'].backward()
        self.discriminator_optimizer.step()
        
        return {k: v.item() if torch.is_tensor(v) else v for k, v in d_loss_dict.items()}
    
    def _train_generator(self, real_batch: torch.Tensor, x_recon: torch.Tensor,
                        mu: torch.Tensor, logvar: torch.Tensor) -> Dict[str, float]:
        """训练生成器 (VAE)"""
        self.vae_optimizer.zero_grad()
        
        # 计算生成器损失
        g_loss_dict = self.loss_function.compute_generator_loss(
            real_batch, x_recon, mu, logvar, self.model.discriminator, self.device
        )
        
        # 反向传播
        g_loss_dict['generator_total_loss'].backward()
        self.vae_optimizer.step()
        
        return {k: v.item() if torch.is_tensor(v) else v for k, v in g_loss_dict.items()}

def create_model_and_trainer(latent_dim: int = 64, device: torch.device = None,
                           vae_lr: float = 2e-4, discriminator_lr: float = 4e-4) -> Tuple[PulsarWGANVAE, PulsarWGANVAETrainer]:
    """
    创建模型和训练器
    
    Args:
        latent_dim: 潜在空间维度
        device: 设备
        vae_lr: VAE学习率
        discriminator_lr: 判别器学习率
        
    Returns:
        model: WGAN-GP+VAE模型
        trainer: 训练器
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = PulsarWGANVAE(latent_dim).to(device)
    
    # 创建训练器
    trainer = PulsarWGANVAETrainer(
        model, device, vae_lr, discriminator_lr
    )
    
    return model, trainer
