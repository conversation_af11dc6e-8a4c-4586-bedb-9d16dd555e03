#!/usr/bin/env python3
"""
CNN-based U-Net架构
基于Phase 1证据，完全替换Transformer组件的轻量级CNN架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, List, Tuple

try:
    from .channel_specific_processing import ChannelSpecificProcessor, ChannelAttention
except ImportError:
    from channel_specific_processing import ChannelSpecificProcessor, ChannelAttention

class ResNetBlock(nn.Module):
    """ResNet块，替代Transformer的核心组件"""

    def __init__(self,
                 in_channels: int,
                 out_channels: int,
                 time_emb_dim: int,
                 dropout: float = 0.1,
                 groups: int = 8):
        super().__init__()

        self.in_channels = in_channels
        self.out_channels = out_channels

        # 时间嵌入投影
        self.time_mlp = nn.Sequential(
            nn.SiLU(),
            nn.Linear(time_emb_dim, out_channels)
        )

        # 第一个卷积块
        self.conv1 = nn.Sequential(
            nn.GroupNorm(min(groups, in_channels), in_channels),
            nn.SiLU(),
            nn.Conv2d(in_channels, out_channels, 3, padding=1)
        )

        # 第二个卷积块
        self.conv2 = nn.Sequential(
            nn.GroupNorm(min(groups, out_channels), out_channels),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Conv2d(out_channels, out_channels, 3, padding=1)
        )

        # 残差连接
        if in_channels != out_channels:
            self.residual_conv = nn.Conv2d(in_channels, out_channels, 1)
        else:
            self.residual_conv = nn.Identity()

    def forward(self, x: torch.Tensor, time_emb: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入特征 (B, C, H, W)
            time_emb: 时间嵌入 (B, time_emb_dim)

        Returns:
            输出特征 (B, out_channels, H, W)
        """
        residual = self.residual_conv(x)

        # 第一个卷积
        h = self.conv1(x)

        # 添加时间嵌入
        time_emb = self.time_mlp(time_emb)[:, :, None, None]
        h = h + time_emb

        # 第二个卷积
        h = self.conv2(h)

        return h + residual

class SpatialAttention(nn.Module):
    """空间注意力机制，替代自注意力"""

    def __init__(self, channels: int, num_heads: int = 8):
        super().__init__()

        self.channels = channels
        self.num_heads = num_heads
        self.head_dim = channels // num_heads

        assert channels % num_heads == 0, "channels must be divisible by num_heads"

        self.norm = nn.GroupNorm(min(8, channels), channels)
        self.qkv = nn.Conv2d(channels, channels * 3, 1)
        self.proj = nn.Conv2d(channels, channels, 1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        空间注意力前向传播

        Args:
            x: 输入特征 (B, C, H, W)

        Returns:
            注意力输出 (B, C, H, W)
        """
        B, C, H, W = x.shape

        # 归一化
        h = self.norm(x)

        # 计算QKV
        qkv = self.qkv(h)  # (B, 3*C, H, W)
        q, k, v = qkv.chunk(3, dim=1)  # 每个都是 (B, C, H, W)

        # 重塑为多头注意力格式
        q = q.view(B, self.num_heads, self.head_dim, H * W)
        k = k.view(B, self.num_heads, self.head_dim, H * W)
        v = v.view(B, self.num_heads, self.head_dim, H * W)

        # 计算注意力权重
        scale = self.head_dim ** -0.5
        attn = torch.einsum('bhdn,bhdm->bhnm', q, k) * scale
        attn = F.softmax(attn, dim=-1)

        # 应用注意力
        out = torch.einsum('bhnm,bhdm->bhdn', attn, v)
        out = out.reshape(B, C, H, W)

        # 投影
        out = self.proj(out)

        return x + out

class CNNBasedUNet(nn.Module):
    """
    基于CNN的U-Net架构

    完全移除Transformer组件，使用ResNet块和空间注意力
    参数量: 15-25M (vs 当前23M Transformer版本)
    """

    def __init__(self,
                 in_channels: int = 3,
                 out_channels: int = 3,
                 base_channels: int = 48,
                 channel_multipliers: Tuple[int, ...] = (1, 2, 3, 4),
                 num_res_blocks: int = 2,
                 time_emb_dim: int = 128,
                 dropout: float = 0.1,
                 use_attention: bool = True,
                 attention_resolutions: Tuple[int, ...] = (16, 8)):
        """
        初始化CNN-based U-Net

        Args:
            in_channels: 输入通道数
            out_channels: 输出通道数
            base_channels: 基础通道数
            channel_multipliers: 通道倍数
            num_res_blocks: 每个分辨率的ResNet块数量
            time_emb_dim: 时间嵌入维度
            dropout: Dropout概率
            use_attention: 是否使用注意力机制
            attention_resolutions: 使用注意力的分辨率
        """
        super().__init__()

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.base_channels = base_channels
        self.channel_multipliers = channel_multipliers
        self.num_res_blocks = num_res_blocks
        self.time_emb_dim = time_emb_dim

        # 通道特定处理器
        self.channel_processor = ChannelSpecificProcessor(
            input_channels=in_channels,
            base_channels=base_channels,
            adaptive_weights=True
        )

        # 时间嵌入
        self.time_embedding = nn.Sequential(
            nn.Linear(base_channels, time_emb_dim),
            nn.SiLU(),
            nn.Linear(time_emb_dim, time_emb_dim)
        )

        # 输入投影
        self.input_conv = nn.Conv2d(base_channels, base_channels, 3, padding=1)

        # 下采样路径
        self.down_blocks = nn.ModuleList()
        self.down_samples = nn.ModuleList()

        channels = [base_channels]
        for i, mult in enumerate(channel_multipliers):
            out_ch = base_channels * mult

            # ResNet块
            blocks = nn.ModuleList()
            for _ in range(num_res_blocks):
                blocks.append(ResNetBlock(
                    channels[-1], out_ch, time_emb_dim, dropout
                ))
                channels.append(out_ch)

            # 注意力机制
            if use_attention and (32 // (2 ** i)) in attention_resolutions:
                blocks.append(SpatialAttention(out_ch))

            self.down_blocks.append(blocks)

            # 下采样
            if i < len(channel_multipliers) - 1:
                self.down_samples.append(nn.Conv2d(out_ch, out_ch, 3, stride=2, padding=1))
            else:
                self.down_samples.append(nn.Identity())

        # 中间块
        mid_ch = base_channels * channel_multipliers[-1]
        self.mid_block1 = ResNetBlock(mid_ch, mid_ch, time_emb_dim, dropout)
        self.mid_attention = SpatialAttention(mid_ch) if use_attention else nn.Identity()
        self.mid_block2 = ResNetBlock(mid_ch, mid_ch, time_emb_dim, dropout)

        # 上采样路径
        self.up_samples = nn.ModuleList()
        self.up_blocks = nn.ModuleList()

        # 保存通道数用于上采样
        up_channels = channels.copy()

        for i, mult in enumerate(reversed(channel_multipliers)):
            out_ch = base_channels * mult

            # 上采样
            if i > 0:
                prev_ch = up_channels.pop()
                self.up_samples.append(nn.ConvTranspose2d(
                    prev_ch, out_ch, 4, stride=2, padding=1
                ))
            else:
                self.up_samples.append(nn.Identity())
                up_channels.pop()  # 移除最后一个通道数

            # ResNet块
            blocks = nn.ModuleList()
            for j in range(num_res_blocks + 1):
                if j == 0:
                    # 第一个块需要处理跳跃连接
                    skip_ch = up_channels.pop() if up_channels else out_ch
                    in_ch = out_ch + skip_ch
                else:
                    in_ch = out_ch

                blocks.append(ResNetBlock(in_ch, out_ch, time_emb_dim, dropout))

            # 注意力机制
            resolution = 32 // (2 ** (len(channel_multipliers) - 1 - i))
            if use_attention and resolution in attention_resolutions:
                blocks.append(SpatialAttention(out_ch))

            self.up_blocks.append(blocks)

        # 输出层
        self.output_conv = nn.Sequential(
            nn.GroupNorm(min(8, base_channels), base_channels),
            nn.SiLU(),
            nn.Conv2d(base_channels, out_channels, 3, padding=1)
        )

    def forward(self, x: torch.Tensor, timesteps: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入图像 (B, C, H, W)
            timesteps: 时间步 (B,)

        Returns:
            预测的噪声 (B, C, H, W)
        """
        # 通道特定处理
        h = self.channel_processor(x)

        # 输入投影
        h = self.input_conv(h)

        # 时间嵌入
        time_emb = self.get_time_embedding(timesteps)
        time_emb = self.time_embedding(time_emb)

        # 下采样路径
        skip_connections = []

        for blocks, downsample in zip(self.down_blocks, self.down_samples):
            for block in blocks:
                if isinstance(block, ResNetBlock):
                    h = block(h, time_emb)
                else:  # SpatialAttention
                    h = block(h)

            skip_connections.append(h)
            h = downsample(h)

        # 中间块
        h = self.mid_block1(h, time_emb)
        h = self.mid_attention(h)
        h = self.mid_block2(h, time_emb)

        # 上采样路径
        for blocks, upsample in zip(self.up_blocks, self.up_samples):
            h = upsample(h)

            # 跳跃连接
            skip = skip_connections.pop()
            h = torch.cat([h, skip], dim=1)

            for block in blocks:
                if isinstance(block, ResNetBlock):
                    h = block(h, time_emb)
                else:  # SpatialAttention
                    h = block(h)

        # 输出
        return self.output_conv(h)

    def get_time_embedding(self, timesteps: torch.Tensor) -> torch.Tensor:
        """生成时间嵌入"""
        half_dim = self.base_channels // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=timesteps.device) * -emb)
        emb = timesteps[:, None] * emb[None, :]
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=-1)
        return emb

def test_cnn_based_unet():
    """测试CNN-based U-Net"""
    print("🧪 测试CNN-based U-Net")

    # 创建模型
    model = CNNBasedUNet(
        in_channels=3,
        out_channels=3,
        base_channels=48,
        channel_multipliers=(1, 2, 3, 4),
        num_res_blocks=2,
        time_emb_dim=128,
        dropout=0.1,
        use_attention=True,
        attention_resolutions=(16, 8)
    )

    # 计算参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    print(f"参数量 (MB): {total_params * 4 / 1024 / 1024:.2f}")

    # 测试前向传播
    batch_size = 4
    x = torch.randn(batch_size, 3, 32, 32)
    timesteps = torch.randint(0, 1000, (batch_size,))

    with torch.no_grad():
        output = model(x, timesteps)

    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"时间步形状: {timesteps.shape}")

    # 验证输出形状
    assert output.shape == x.shape, f"输出形状不匹配: {output.shape} vs {x.shape}"

    print("✅ CNN-based U-Net测试通过")

    return model

if __name__ == "__main__":
    model = test_cnn_based_unet()
