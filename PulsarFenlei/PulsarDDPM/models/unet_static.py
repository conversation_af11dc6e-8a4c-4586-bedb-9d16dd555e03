"""
UNet模型的静态架构实现，解决维度不匹配问题。
"""

import math
from typing import List, Tuple, Optional, Dict, Union, Any

import torch
import torch.nn as nn
import torch.nn.functional as F

from .utils import get_norm


class Upsample(nn.Module):
    """
    上采样模块。
    """
    def __init__(self, in_channels: int, out_channels: Optional[int] = None):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels or in_channels
        self.conv = nn.Conv2d(self.in_channels, self.out_channels, 3, padding=1)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = F.interpolate(x, scale_factor=2, mode="nearest")
        return self.conv(x)


class Downsample(nn.Module):
    """
    下采样模块。
    """
    def __init__(self, in_channels: int, out_channels: Optional[int] = None):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels or in_channels
        self.conv = nn.Conv2d(self.in_channels, self.out_channels, 3, stride=2, padding=1)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.conv(x)


class AttentionBlock(nn.Module):
    """
    注意力模块。
    """
    def __init__(
        self,
        in_channels: int,
        norm: str = "gn",
        num_groups: int = 32,
        reduce_channels: bool = True,
        reduction_factor: float = 0.5,
    ):
        super().__init__()
        self.in_channels = in_channels
        self.norm = get_norm(norm, in_channels, num_groups)
        
        # 减少内部维度以提高效率
        self.reduced = reduce_channels
        if reduce_channels:
            self.attention_channels = max(int(in_channels * reduction_factor), 16)
            self.to_q = nn.Conv2d(in_channels, self.attention_channels, 1)
            self.to_k = nn.Conv2d(in_channels, self.attention_channels, 1)
            self.to_v = nn.Conv2d(in_channels, self.attention_channels, 1)
            self.to_out = nn.Conv2d(self.attention_channels, in_channels, 1)
        else:
            self.attention_channels = in_channels
            self.to_qkv = nn.Conv2d(in_channels, in_channels * 3, 1)
            self.to_out = nn.Conv2d(in_channels, in_channels, 1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        b, c, h, w = x.shape
        normalized = self.norm(x)
        
        if self.reduced:
            # 使用减少的维度计算q, k, v
            q = self.to_q(normalized).reshape(b, self.attention_channels, h * w).permute(0, 2, 1)
            k = self.to_k(normalized).reshape(b, self.attention_channels, h * w)
            v = self.to_v(normalized).reshape(b, self.attention_channels, h * w).permute(0, 2, 1)
        else:
            # 标准QKV计算
            q, k, v = torch.split(self.to_qkv(normalized), self.attention_channels, dim=1)
            q = q.permute(0, 2, 3, 1).reshape(b, h * w, self.attention_channels)
            k = k.reshape(b, self.attention_channels, h * w)
            v = v.permute(0, 2, 3, 1).reshape(b, h * w, self.attention_channels)
        
        # 使用缩放点积计算注意力
        dot_products = torch.bmm(q, k) * (self.attention_channels ** (-0.5))
        attention = torch.softmax(dot_products, dim=-1)
        out = torch.bmm(attention, v)
        out = out.reshape(b, h, w, self.attention_channels).permute(0, 3, 1, 2)
        
        # 投影回原始维度
        return self.to_out(out) + x


class ResidualBlock(nn.Module):
    """
    残差块，带有时间嵌入和类别条件。
    """
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        dropout: float = 0.0,
        time_emb_dim: Optional[int] = None,
        num_classes: Optional[int] = None,
        activation: nn.Module = nn.SiLU(),
        norm: str = "gn",
        num_groups: int = 32,
        use_attention: bool = False,
        use_cbam: bool = False,
        cbam_reduction_ratio: int = 16,
        cbam_kernel_size: int = 7,
        channel_id: Optional[int] = None,
        reduce_attention_channels: bool = True,
        attention_reduction_factor: float = 0.5,
    ):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.activation = activation
        
        # 标准化和卷积层
        self.norm_1 = get_norm(norm, in_channels, num_groups)
        self.conv_1 = nn.Conv2d(in_channels, out_channels, 3, padding=1)
        
        self.norm_2 = get_norm(norm, out_channels, num_groups)
        self.conv_2 = nn.Sequential(
            nn.Dropout(p=dropout),
            nn.Conv2d(out_channels, out_channels, 3, padding=1),
        )
        
        # 时间嵌入投影
        self.time_bias = nn.Linear(time_emb_dim, out_channels) if time_emb_dim else None
        
        # 类别条件
        self.class_bias = nn.Embedding(num_classes, out_channels) if num_classes else None
        
        # 注意力机制
        self.attention = AttentionBlock(
            out_channels, 
            norm=norm, 
            num_groups=num_groups,
            reduce_channels=reduce_attention_channels,
            reduction_factor=attention_reduction_factor
        ) if use_attention else nn.Identity()
        
        # 残差连接
        # 使用1x1卷积调整通道数，确保输入和输出通道数匹配
        self.residual_connection = nn.Conv2d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()
    
    def forward(self, x: torch.Tensor, time_emb: Optional[torch.Tensor] = None, y: Optional[torch.Tensor] = None) -> torch.Tensor:
        out = self.activation(self.norm_1(x))
        out = self.conv_1(out)
        
        if self.time_bias is not None:
            if time_emb is None:
                raise ValueError("time conditioning was specified but time_emb is not passed")
            out += self.time_bias(self.activation(time_emb))[:, :, None, None]
        
        if self.class_bias is not None:
            if y is None:
                raise ValueError("class conditioning was specified but y is not passed")
            out += self.class_bias(y)[:, :, None, None]
        
        out = self.activation(self.norm_2(out))
        out = self.conv_2(out) + self.residual_connection(x)
        out = self.attention(out)
        
        return out


class SkipConnectionBlock(nn.Module):
    """
    跳跃连接块，用于处理跳跃连接的通道数调整。
    """
    def __init__(self, in_channels: int, out_channels: int):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.projection = nn.Conv2d(in_channels, out_channels, 1)
    
    def forward(self, x: torch.Tensor, skip: torch.Tensor) -> torch.Tensor:
        # 调整skip的通道数
        projected_skip = self.projection(skip)
        # 拼接
        return torch.cat([x, projected_skip], dim=1)


class TimeEmbedding(nn.Module):
    """
    时间嵌入模块。
    """
    def __init__(self, dim: int, max_period: int = 10000):
        super().__init__()
        self.dim = dim
        self.max_period = max_period
        
        # 使用更深的MLP获取更好的时间表示
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 2),
            nn.SiLU(),
            nn.Linear(dim * 2, dim),
            nn.SiLU(),
            nn.Linear(dim, dim),
        )
    
    def forward(self, t: torch.Tensor) -> torch.Tensor:
        half = self.dim // 2
        freqs = torch.exp(-math.log(self.max_period) * torch.arange(start=0, end=half, dtype=torch.float32) / half).to(t.device)
        args = t[:, None].float() * freqs[None]
        embedding = torch.cat([torch.cos(args), torch.sin(args)], dim=-1)
        if self.dim % 2:
            embedding = torch.cat([embedding, torch.zeros_like(embedding[:, :1])], dim=-1)
        
        return self.mlp(embedding)
