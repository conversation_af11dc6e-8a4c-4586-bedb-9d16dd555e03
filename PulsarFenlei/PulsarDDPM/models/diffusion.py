import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
import math
from typing import Optional, Tuple, List, Dict, Any, Union, Callable

# Configure logging
logger = logging.getLogger(__name__)

from .ema import EMA
from .utils import extract, generate_cosine_schedule

# OPTIMIZATION 2.1: Import ChannelWeightedLoss for pulsar-specific loss computation
import sys
import os
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from alternative_channel_strategies import ChannelWeightedLoss
except ImportError:
    logger.warning("Could not import ChannelWeightedLoss from alternative_channel_strategies")
    ChannelWeightedLoss = None

# 特征匹配损失的通道权重配置 - 针对HTRU1数据集优化
HTRU1_FEATURE_WEIGHTS = {
    0: {  # Period-DM surface - 增强低层权重，关注峰值检测
        'low': 0.6,    # 60% (原70%，略微降低以平衡)
        'mid': 0.3,    # 30% (原20%，增加以捕获更多结构信息)
        'high': 0.1,   # 10% (保持，关注全局特征)
    },
    1: {  # Phase-Subband surface - 平衡调整，重点关注中层条纹模式
        'low': 0.3,    # 30% (原40%，降低以减少噪声)
        'mid': 0.6,    # 60% (原50%，增加以强化条纹检测)
        'high': 0.1,   # 10% (保持，关注整体结构)
    },
    2: {  # Phase-Subintegration surface - 增强高层权重，保持细节
        'low': 0.2,    # 20% (原30%，降低以减少低层噪声)
        'mid': 0.3,    # 30% (原40%，降低以平衡)
        'high': 0.5,   # 50% (原30%，增加以保持细节特征)
    }
}

# 定义每个通道的关键特征层，用于选择性特征提取
# 这样可以显著减少内存使用，同时保持特征匹配损失的有效性
CHANNEL_KEY_LAYERS = {
    0: {  # Period-DM surface
        'low': ['low_0'],           # 只使用第一个低层特征，保留峰值信息
        'mid': ['mid_2'],           # 使用中间的中层特征
        'high': ['high_4', 'out'],  # 使用最后的高层特征和输出
    },
    1: {  # Phase-Subband surface
        'low': ['low_0'],
        'mid': ['mid_2', 'mid_3'],  # 使用两个中层特征，保留垂直条纹
        'high': ['high_4'],
    },
    2: {  # Phase-Subintegration surface
        'low': ['low_0'],
        'mid': ['mid_2'],
        'high': ['high_4', 'out'],  # 使用高层特征和输出，保留细节
    }
}

# 极简关键特征层，用于更激进的内存优化
# 每个通道只保留1-2个最重要的特征层，显著减少内存使用
MINIMAL_KEY_LAYERS = {
    0: {  # Period-DM surface
        'low': ['low_0'],  # 保留低层特征，捕捉峰值信息
        'mid': [],         # 不使用中层特征
        'high': ['out'],   # 只使用输出特征
    },
    1: {  # Phase-Subband surface
        'low': [],         # 不使用低层特征
        'mid': ['mid_2'],  # 只使用一个中层特征，保留垂直条纹
        'high': [],        # 不使用高层特征
    },
    2: {  # Phase-Subintegration surface
        'low': [],         # 不使用低层特征
        'mid': [],         # 不使用中层特征
        'high': ['high_4'] # 只使用一个高层特征，保留细节
    }
}

# 平衡的特征层配置 - 在内存效率和特征丰富度之间取得平衡
# 相比MINIMAL_KEY_LAYERS增加33%特征层，相比CHANNEL_KEY_LAYERS减少25%内存使用
BALANCED_KEY_LAYERS = {
    0: {  # Period-DM surface - 重点关注低层特征（峰值检测）
        'low': ['low_0', 'low_1'],      # 2个低层特征
        'mid': ['mid_0'],               # 1个中层特征
        'high': ['high_2', 'out'],      # 2个高层特征
    },
    1: {  # Phase-Subband surface - 重点关注中层特征（条纹模式）
        'low': ['low_0'],               # 1个低层特征
        'mid': ['mid_0', 'mid_1'],      # 2个中层特征
        'high': ['high_3'],             # 1个高层特征
    },
    2: {  # Phase-Subintegration surface - 平衡各层特征（细节保持）
        'low': ['low_1'],               # 1个低层特征
        'mid': ['mid_1'],               # 1个中层特征
        'high': ['high_2', 'high_3'],   # 2个高层特征
    }
}


class GaussianDiffusion(nn.Module):
    """
    Gaussian Diffusion model for denoising diffusion probabilistic models.

    Args:
        model (nn.Module): Model which estimates diffusion noise
        img_size (tuple): Image size tuple (H, W)
        img_channels (int): Number of image channels
        num_classes (int, optional): Number of classes for class conditioning
        betas (np.ndarray): Noise schedule
        loss_type (str): Loss type, "l1" or "l2"
        ema_decay (float): EMA decay rate
        ema_start (int): Number of steps before starting EMA
        ema_update_rate (int): Number of steps between EMA updates
        device (str): Device to use
    """
    def __init__(
        self,
        model: nn.Module,
        img_size: Tuple[int, int],
        img_channels: int,
        num_classes: Optional[int] = None,
        betas: Optional[np.ndarray] = None,
        loss_type: str = "l2",
        ema_decay: float = 0.9999,
        ema_start: int = 5000,
        ema_update_rate: int = 1,
        device: str = "cuda",
        curriculum_learning: bool = False,
        curriculum_strategy: str = "noise_level",
        use_feature_matching: bool = False,
        feature_matching_weight: float = 0.1,
        # OPTIMIZATION 2.1: Pulsar-specific loss function parameters
        use_channel_weighted_loss: bool = True,
        channel_weights: List[float] = None,
        adaptive_channel_weights: bool = True,
    ):
        super().__init__()

        self.model = model
        # 延迟初始化 EMA 模型，减少训练初期的内存使用
        self.ema_model = None

        self.ema = EMA(ema_decay)
        self.ema_decay = ema_decay
        self.ema_start = ema_start
        self.ema_update_rate = ema_update_rate
        self.step = 0

        # 记录是否已经记录了 EMA 初始化信息
        self._ema_init_logged = False

        self.img_size = img_size
        self.img_channels = img_channels
        self.num_classes = num_classes
        self.device = device

        if loss_type not in ["l1", "l2"]:
            raise ValueError("loss_type must be 'l1' or 'l2'")
        self.loss_type = loss_type

        # Feature matching settings
        self.use_feature_matching = use_feature_matching
        self.feature_matching_weight = feature_matching_weight

        # OPTIMIZATION 2.1: Initialize ChannelWeightedLoss for pulsar-specific loss computation
        self.use_channel_weighted_loss = use_channel_weighted_loss
        self.channel_weighted_loss = None

        if self.use_channel_weighted_loss and ChannelWeightedLoss is not None:
            # Default channel weights for pulsar data: Period-DM, Phase-Subband, Phase-Subintegration
            default_weights = [0.4, 0.35, 0.25]
            weights = channel_weights if channel_weights is not None else default_weights

            self.channel_weighted_loss = ChannelWeightedLoss(
                channel_weights=weights,
                adaptive_weights=adaptive_channel_weights
            )
            logger.info(f"Initialized ChannelWeightedLoss with weights: {weights}, adaptive: {adaptive_channel_weights}")
        elif self.use_channel_weighted_loss:
            logger.warning("ChannelWeightedLoss requested but not available, falling back to standard loss")
            self.use_channel_weighted_loss = False

        # Default to cosine schedule if betas is None
        if betas is None:
            betas = generate_cosine_schedule(1000)
        self.num_timesteps = len(betas)

        # Curriculum learning settings
        self.curriculum_learning = curriculum_learning
        self.curriculum_strategy = curriculum_strategy
        self.curriculum_stage = 0  # Current curriculum stage (0-5)

        # Six-stage curriculum learning with finer granularity
        # Each stage gradually increases the noise level
        self.num_stages = 6
        stage_fractions = [1/6, 2/6, 3/6, 4/6, 5/6, 6/6]  # Fractions of total timesteps

        self.curriculum_timestep_ranges = []
        for i in range(self.num_stages):
            max_t = int(stage_fractions[i] * self.num_timesteps)
            # Ensure the final stage includes the maximum timestep
            if i == self.num_stages - 1:
                max_t = self.num_timesteps - 1
            self.curriculum_timestep_ranges.append((0, max_t))

        # Initialize transition epochs (will be set by set_curriculum_transitions)
        self.curriculum_transitions = [0] * self.num_stages

        # Noise distribution parameters for each stage
        # These control how timesteps are sampled within each range
        # Lower alpha means more concentration on lower timesteps (easier samples)
        # Higher alpha means more uniform distribution
        self.noise_distribution_alphas = [0.1, 0.3, 0.5, 0.7, 0.9, 1.0]

        # Transition smoothness factor (0-1)
        # 0 means abrupt transitions, 1 means very smooth transitions
        self.transition_smoothness = 0.8

        # Number of epochs to complete a transition
        self.transition_window = 10

        # Metrics tracking
        self.curriculum_metrics = {
            'stage': 0,
            'loss_history': [],
            'transition_epochs': [],
            'current_epoch': 0,
            'stage_progress': 0.0,  # Progress within current stage (0-1)
            'adaptive_noise_scale': 1.0  # Scale factor for adaptive noise scheduling
        }

        # Precompute diffusion parameters
        self._precompute_diffusion_parameters(betas)

    def _create_ema_model(self, model: nn.Module) -> nn.Module:
        """
        Create a copy of the model for EMA.

        BUGFIX: Ensure EMA model is on the same device as the original model.

        Args:
            model (nn.Module): Model to copy

        Returns:
            nn.Module: Copy of the model
        """
        import copy
        ema_model = copy.deepcopy(model)
        for param in ema_model.parameters():
            param.detach_()

        # BUGFIX: Ensure EMA model is on the same device as the original model
        ema_model = ema_model.to(next(model.parameters()).device)

        return ema_model

    def _precompute_diffusion_parameters(self, betas: Union[np.ndarray, torch.Tensor]) -> None:
        """
        Precompute diffusion parameters for efficiency.

        Args:
            betas (Union[np.ndarray, torch.Tensor]): Noise schedule
        """
        # Convert to torch tensors if needed
        if isinstance(betas, np.ndarray):
            betas = torch.from_numpy(betas).float().to(self.device)
        else:
            betas = betas.float().to(self.device)

        # Compute alphas and related parameters
        alphas = 1.0 - betas
        alphas_cumprod = torch.cumprod(alphas, dim=0)
        alphas_cumprod_prev = F.pad(alphas_cumprod[:-1], (1, 0), value=1.0)

        # Register buffers for parameters
        self.register_buffer("betas", betas)
        self.register_buffer("alphas", alphas)
        self.register_buffer("alphas_cumprod", alphas_cumprod)
        self.register_buffer("alphas_cumprod_prev", alphas_cumprod_prev)

        # Calculations for diffusion q(x_t | x_{t-1}) and others
        self.register_buffer("sqrt_alphas_cumprod", torch.sqrt(alphas_cumprod))
        self.register_buffer("sqrt_one_minus_alphas_cumprod", torch.sqrt(1.0 - alphas_cumprod))
        self.register_buffer("log_one_minus_alphas_cumprod", torch.log(1.0 - alphas_cumprod))
        self.register_buffer("sqrt_recip_alphas_cumprod", torch.sqrt(1.0 / alphas_cumprod))
        self.register_buffer("sqrt_recipm1_alphas_cumprod", torch.sqrt(1.0 / alphas_cumprod - 1))

        # Calculations for posterior q(x_{t-1} | x_t, x_0)
        posterior_variance = betas * (1.0 - alphas_cumprod_prev) / (1.0 - alphas_cumprod)
        self.register_buffer("posterior_variance", posterior_variance)
        self.register_buffer("posterior_log_variance_clipped", torch.log(posterior_variance.clamp(min=1e-20)))
        self.register_buffer("posterior_mean_coef1", betas * torch.sqrt(alphas_cumprod_prev) / (1.0 - alphas_cumprod))
        self.register_buffer("posterior_mean_coef2", (1.0 - alphas_cumprod_prev) * torch.sqrt(alphas) / (1.0 - alphas_cumprod))

    def update_ema(self) -> None:
        """
        Update EMA parameters.
        优化版本：延迟初始化 EMA 模型，减少训练初期的内存使用。
        只有在达到 ema_start 步数后才创建 EMA 模型。
        """
        self.step += 1

        # 记录内存使用情况（EMA 更新前）
        if torch.cuda.is_available() and self.step % 100 == 0:
            pre_ema_memory = torch.cuda.memory_allocated() / 1024**2
            logger.info(f"EMA 更新前内存: {pre_ema_memory:.2f} MB, 步数: {self.step}")

        if self.step % self.ema_update_rate == 0:
            if self.step >= self.ema_start:
                # 只有在达到 ema_start 步数后才创建和更新 EMA 模型
                if self.ema_model is None:
                    # 记录内存使用情况（EMA 初始化前）
                    if torch.cuda.is_available():
                        pre_init_memory = torch.cuda.memory_allocated() / 1024**2

                    # 初始化 EMA 模型
                    logger.info(f"初始化 EMA 模型，步数: {self.step}")
                    self.ema_model = self._create_ema_model(self.model)

                    # 记录内存使用情况（EMA 初始化后）
                    if torch.cuda.is_available():
                        post_init_memory = torch.cuda.memory_allocated() / 1024**2
                        logger.info(f"EMA 模型初始化内存增加: {post_init_memory - pre_init_memory:.2f} MB")
                else:
                    # 更新 EMA 模型
                    self.ema.update_model_average(self.ema_model, self.model)
            elif self.ema_model is not None:
                # 如果还没有达到 ema_start 步数但 EMA 模型已经存在，直接复制模型参数
                self.ema_model.load_state_dict(self.model.state_dict())

        # 记录内存使用情况（EMA 更新后）
        if torch.cuda.is_available() and self.step % 100 == 0:
            post_ema_memory = torch.cuda.memory_allocated() / 1024**2
            logger.info(f"EMA 更新后内存: {post_ema_memory:.2f} MB")
            if 'pre_ema_memory' in locals():
                logger.info(f"EMA 更新内存变化: {post_ema_memory - pre_ema_memory:.2f} MB")

    def set_curriculum_transitions(self, transitions: List[int]) -> None:
        """
        Set epoch thresholds for curriculum transitions.

        Args:
            transitions (List[int]): List of epoch thresholds for transitions between stages
                                    For 6-stage curriculum: [stage0_to_stage1, stage1_to_stage2,
                                    stage2_to_stage3, stage3_to_stage4, stage4_to_stage5]
        """
        # For backward compatibility, handle both 2-element and 5-element transition lists
        if len(transitions) == 2:
            # Convert 3-stage transitions to 6-stage transitions
            # Distribute transitions evenly between the original points
            old_transitions = [0] + transitions
            new_transitions = [0]  # Stage 0 always starts at epoch 0

            # Calculate intermediate transition points
            for i in range(1, len(old_transitions)):
                start = old_transitions[i-1]
                end = old_transitions[i]
                step = (end - start) / ((self.num_stages - 1) / (len(old_transitions) - 1))

                for j in range(1, int((self.num_stages - 1) / (len(old_transitions) - 1)) + 1):
                    if i < len(old_transitions) - 1 or j < int((self.num_stages - 1) / (len(old_transitions) - 1)):
                        new_transitions.append(int(start + j * step))

            # Ensure we have the right number of transitions
            while len(new_transitions) < self.num_stages:
                new_transitions.append(new_transitions[-1] + int(step))

            self.curriculum_transitions = new_transitions
            logger.info(f"Converted 3-stage transitions {[0] + transitions} to 6-stage transitions {self.curriculum_transitions}")
        elif len(transitions) == self.num_stages - 1:
            # Direct 6-stage transitions
            self.curriculum_transitions = [0] + transitions
            logger.info(f"Set curriculum transitions to {self.curriculum_transitions}")
        else:
            raise ValueError(f"transitions must be a list of {self.num_stages - 1} integers for {self.num_stages}-stage curriculum")

        # Update transition window based on transition spacing
        min_spacing = float('inf')
        for i in range(1, len(self.curriculum_transitions)):
            spacing = self.curriculum_transitions[i] - self.curriculum_transitions[i-1]
            min_spacing = min(min_spacing, spacing)

        # Set transition window to 1/3 of minimum spacing, but at least 5 epochs
        self.transition_window = max(5, min(min_spacing // 3, 10))
        logger.info(f"Set transition window to {self.transition_window} epochs")

    def update_curriculum_stage(self, epoch: int) -> bool:
        """
        Update curriculum stage based on current epoch with smooth transitions.

        Args:
            epoch (int): Current epoch

        Returns:
            bool: Whether the stage was updated
        """
        if not self.curriculum_learning:
            return False

        # Update current epoch
        self.curriculum_metrics['current_epoch'] = epoch

        # Check if we need to transition to the next stage
        old_stage = self.curriculum_stage
        old_progress = self.curriculum_metrics['stage_progress']

        # Determine base stage based on epoch
        base_stage = 0
        for i, threshold in enumerate(self.curriculum_transitions):
            if epoch >= threshold:
                base_stage = i

        # Calculate progress within transition window for smooth transitions
        next_threshold = float('inf')
        if base_stage < len(self.curriculum_transitions) - 1:
            next_threshold = self.curriculum_transitions[base_stage + 1]

        # Calculate progress within current stage (0-1)
        if base_stage < len(self.curriculum_transitions) - 1:
            current_threshold = self.curriculum_transitions[base_stage]
            stage_duration = next_threshold - current_threshold
            stage_progress = min(1.0, (epoch - current_threshold) / stage_duration)
        else:
            # Final stage
            stage_progress = 1.0

        # Calculate smooth transition progress
        # During transition window, we blend between stages
        transition_progress = 0.0
        in_transition = False

        if base_stage < len(self.curriculum_transitions) - 1:
            # Calculate distance to next transition
            distance_to_next = next_threshold - epoch

            if distance_to_next <= self.transition_window:
                # We're in the transition window approaching the next stage
                transition_progress = 1.0 - (distance_to_next / self.transition_window)
                in_transition = True

        # Apply smoothness factor
        if in_transition:
            # Apply sigmoid smoothing for more natural transitions
            # This transforms linear transition_progress to a sigmoid curve
            transition_progress = 1.0 / (1.0 + math.exp(-10 * (transition_progress - 0.5)))

            # Apply user-defined smoothness factor
            transition_progress = transition_progress * self.transition_smoothness

        # Store progress metrics
        self.curriculum_metrics['stage'] = base_stage
        self.curriculum_metrics['stage_progress'] = stage_progress
        self.curriculum_metrics['in_transition'] = in_transition
        self.curriculum_metrics['transition_progress'] = transition_progress

        # Determine if we've changed stages
        stage_changed = (base_stage != old_stage)

        # If stage changed, log the transition
        if stage_changed:
            logger.info(f"Curriculum learning: Transitioning from stage {old_stage} to stage {base_stage}")
            logger.info(f"Curriculum learning: New base timestep range = {self.curriculum_timestep_ranges[base_stage]}")
            self.curriculum_metrics['transition_epochs'].append(epoch)

            # Adaptive noise scheduling based on loss history
            self._update_adaptive_noise_scale()

            return True

        return False

    def _update_adaptive_noise_scale(self) -> None:
        """
        Update adaptive noise scale based on recent loss history.

        This adjusts the noise distribution based on training progress:
        - If loss is decreasing steadily, we can increase difficulty slightly
        - If loss is unstable or increasing, we reduce difficulty
        """
        # Need at least 50 loss values to make a decision
        if len(self.curriculum_metrics['loss_history']) < 50:
            self.curriculum_metrics['adaptive_noise_scale'] = 1.0
            return

        # Get recent loss values (last 50)
        recent_losses = self.curriculum_metrics['loss_history'][-50:]

        # Calculate trend using simple linear regression
        x = np.arange(len(recent_losses))
        y = np.array(recent_losses)
        A = np.vstack([x, np.ones(len(x))]).T

        # Avoid numerical issues
        try:
            slope, _ = np.linalg.lstsq(A, y, rcond=None)[0]

            # Normalize slope to make it comparable across different loss scales
            normalized_slope = slope / np.mean(recent_losses) * 100

            # Calculate loss volatility (standard deviation relative to mean)
            volatility = np.std(recent_losses) / np.mean(recent_losses)

            # Adjust noise scale based on trend and volatility
            if normalized_slope < -0.5 and volatility < 0.1:
                # Loss is decreasing steadily with low volatility
                # Increase difficulty slightly
                new_scale = min(1.2, self.curriculum_metrics['adaptive_noise_scale'] + 0.05)
                logger.info(f"Loss decreasing steadily, increasing noise scale to {new_scale:.2f}")
            elif normalized_slope > 0.5 or volatility > 0.2:
                # Loss is increasing or highly volatile
                # Reduce difficulty
                new_scale = max(0.8, self.curriculum_metrics['adaptive_noise_scale'] - 0.1)
                logger.info(f"Loss unstable or increasing, reducing noise scale to {new_scale:.2f}")
            else:
                # Maintain current scale
                new_scale = self.curriculum_metrics['adaptive_noise_scale']

            self.curriculum_metrics['adaptive_noise_scale'] = new_scale

        except Exception as e:
            logger.warning(f"Error updating adaptive noise scale: {e}")
            # Keep current scale
            pass

    def get_curriculum_timestep_range(self) -> Tuple[int, int]:
        """
        Get current timestep range based on curriculum stage with smooth transitions.

        Implements:
        1. Smooth transitions between stages
        2. Non-uniform timestep sampling based on stage difficulty
        3. Adaptive noise scaling based on training progress

        Returns:
            Tuple[int, int]: (min_timestep, max_timestep)
        """
        if not self.curriculum_learning:
            return (0, self.num_timesteps - 1)

        # Get base stage and next stage (if in transition)
        base_stage = self.curriculum_metrics['stage']
        base_range = self.curriculum_timestep_ranges[base_stage]

        # Check if we're in a transition between stages
        if self.curriculum_metrics.get('in_transition', False):
            # Get transition progress (0-1)
            progress = self.curriculum_metrics['transition_progress']

            # If we're in the last stage, there's no next stage
            if base_stage < len(self.curriculum_timestep_ranges) - 1:
                next_stage = base_stage + 1
                next_range = self.curriculum_timestep_ranges[next_stage]

                # Interpolate between current and next stage's max timestep
                max_t = int(base_range[1] * (1 - progress) + next_range[1] * progress)

                # Always keep min_t at 0 for our curriculum design
                min_t = 0

                return (min_t, max_t)

        # If not in transition, return the base range
        return base_range

    def get_curriculum_metrics(self) -> Dict[str, Any]:
        """
        Get curriculum learning metrics.

        Returns:
            Dict[str, Any]: Curriculum learning metrics
        """
        if not self.curriculum_learning:
            return {}

        # Calculate average loss for recent iterations
        recent_loss = 0.0
        if len(self.curriculum_metrics['loss_history']) > 0:
            # Get last 100 losses or all if less than 100
            recent_losses = self.curriculum_metrics['loss_history'][-100:]
            recent_loss = sum(recent_losses) / len(recent_losses)

        metrics = {
            'stage': self.curriculum_stage,
            'current_epoch': self.curriculum_metrics['current_epoch'],
            'transition_epochs': self.curriculum_metrics['transition_epochs'],
            'recent_loss': recent_loss,
            'timestep_range': self.curriculum_timestep_ranges[self.curriculum_stage],
        }

        return metrics

    def q_sample(self, x_0: torch.Tensor, t: torch.Tensor, noise: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Sample from q(x_t | x_0) (forward diffusion process).

        Args:
            x_0 (torch.Tensor): Initial sample (clean image)
            t (torch.Tensor): Timesteps
            noise (torch.Tensor, optional): Noise to add. If None, random noise is used.

        Returns:
            torch.Tensor: Noisy sample at timestep t
        """
        # Log tensor shapes for debugging
        logger.debug(f"[GaussianDiffusion.q_sample] x_0: shape={x_0.shape}")
        logger.debug(f"[GaussianDiffusion.q_sample] t: shape={t.shape}")

        # When t=0, return x_0 directly
        if (t == 0).all():
            return x_0

        if noise is None:
            noise = torch.randn_like(x_0)

        # Extract parameters for the given timesteps
        sqrt_alphas_cumprod_t = extract(self.sqrt_alphas_cumprod, t, x_0.shape)
        sqrt_one_minus_alphas_cumprod_t = extract(self.sqrt_one_minus_alphas_cumprod, t, x_0.shape)

        # Sample from q(x_t | x_0)
        return sqrt_alphas_cumprod_t * x_0 + sqrt_one_minus_alphas_cumprod_t * noise

    def q_posterior_mean_variance(self, x_0: torch.Tensor, x_t: torch.Tensor, t: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Compute the mean and variance of the diffusion posterior q(x_{t-1} | x_t, x_0).

        Args:
            x_0 (torch.Tensor): Initial sample (clean image)
            x_t (torch.Tensor): Noisy sample at timestep t
            t (torch.Tensor): Timesteps

        Returns:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor]: Posterior mean, variance, and log variance
        """
        posterior_mean_coef1_t = extract(self.posterior_mean_coef1, t, x_0.shape)
        posterior_mean_coef2_t = extract(self.posterior_mean_coef2, t, x_0.shape)

        # Compute posterior mean
        posterior_mean = posterior_mean_coef1_t * x_0 + posterior_mean_coef2_t * x_t

        # Extract posterior variance and log variance
        posterior_variance_t = extract(self.posterior_variance, t, x_0.shape)
        posterior_log_variance_clipped_t = extract(self.posterior_log_variance_clipped, t, x_0.shape)

        return posterior_mean, posterior_variance_t, posterior_log_variance_clipped_t

    def p_mean_variance(self, x_t: torch.Tensor, t: torch.Tensor, y: Optional[torch.Tensor] = None, use_ema: bool = True) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Compute the mean and variance of the diffusion posterior p(x_{t-1} | x_t) with enhanced numerical stability.

        Args:
            x_t (torch.Tensor): Noisy sample at timestep t
            t (torch.Tensor): Timesteps
            y (torch.Tensor, optional): Class labels
            use_ema (bool): Whether to use EMA model

        Returns:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor]: Predicted mean, variance, and log variance
        """
        # Check for NaN or Inf values in input
        if torch.isnan(x_t).any() or torch.isinf(x_t).any():
            logger.warning(f"NaN or Inf detected in input tensor at timestep {t[0].item()}")
            # Replace NaN and Inf values with zeros
            x_t = torch.nan_to_num(x_t, nan=0.0, posinf=0.0, neginf=0.0)

        # Select the appropriate model (EMA or regular)
        original_state = None
        if use_ema:
            # 检查各种可能的EMA模型存储方式
            if hasattr(self, 'ema_model') and self.ema_model is not None:
                if isinstance(self.ema_model, nn.Module):
                    # 如果ema_model是一个完整的模型实例
                    logger.debug("Using EMA model instance")
                    model = self.ema_model
                elif isinstance(self.ema_model, dict):
                    # 如果ema_model是状态字典
                    logger.debug("Temporarily loading EMA state dict")
                    original_state = {k: v.clone() for k, v in self.model.state_dict().items()}
                    self.model.load_state_dict(self.ema_model)
                    model = self.model
                    # 我们会在预测后恢复原始权重
                else:
                    logger.warning(f"Unknown EMA model type: {type(self.ema_model)}, using regular model")
                    model = self.model
            # 检查是否有_ema_state_dict属性（由test_sampling.py设置）
            elif hasattr(self, '_ema_state_dict') and self._ema_state_dict is not None:
                logger.debug("Using _ema_state_dict")
                original_state = {k: v.clone() for k, v in self.model.state_dict().items()}
                self.model.load_state_dict(self._ema_state_dict)
                model = self.model
                # 我们会在预测后恢复原始权重
            else:
                logger.warning("EMA model requested but not available, using regular model")
                model = self.model
        else:
            model = self.model

        # Log tensor shapes for debugging
        logger.debug(f"[GaussianDiffusion.p_mean_variance] x_t: shape={x_t.shape}")
        logger.debug(f"[GaussianDiffusion.p_mean_variance] t: shape={t.shape}")
        if y is not None:
            logger.debug(f"[GaussianDiffusion.p_mean_variance] y: shape={y.shape}")

        # Predict the noise with error handling
        try:
            pred_noise = model(x_t, t, y)

            # Check for NaN or Inf values in predicted noise
            if torch.isnan(pred_noise).any() or torch.isinf(pred_noise).any():
                logger.warning(f"NaN or Inf detected in predicted noise at timestep {t[0].item()}")
                # Replace with zeros or small random noise
                pred_noise = torch.randn_like(x_t) * 0.001
        except Exception as e:
            logger.error(f"Error predicting noise at timestep {t[0].item()}: {e}")
            # Fallback to random noise
            pred_noise = torch.randn_like(x_t) * 0.01

        # 恢复原始权重（如果我们临时加载了EMA权重）
        if original_state is not None:
            logger.debug("Restoring original model weights")
            self.model.load_state_dict(original_state)

        logger.debug(f"[GaussianDiffusion.p_mean_variance] pred_noise: shape={pred_noise.shape}")

        # Extract parameters for the given timesteps with error handling
        try:
            sqrt_recip_alphas_cumprod_t = extract(self.sqrt_recip_alphas_cumprod, t, x_t.shape)
            sqrt_recipm1_alphas_cumprod_t = extract(self.sqrt_recipm1_alphas_cumprod, t, x_t.shape)

            # Check for NaN or Inf values
            if torch.isnan(sqrt_recip_alphas_cumprod_t).any() or torch.isinf(sqrt_recip_alphas_cumprod_t).any():
                logger.warning("NaN or Inf in sqrt_recip_alphas_cumprod_t")
                sqrt_recip_alphas_cumprod_t = torch.ones_like(sqrt_recip_alphas_cumprod_t)

            if torch.isnan(sqrt_recipm1_alphas_cumprod_t).any() or torch.isinf(sqrt_recipm1_alphas_cumprod_t).any():
                logger.warning("NaN or Inf in sqrt_recipm1_alphas_cumprod_t")
                sqrt_recipm1_alphas_cumprod_t = torch.zeros_like(sqrt_recipm1_alphas_cumprod_t)
        except Exception as e:
            logger.error(f"Error extracting diffusion parameters: {e}")
            # Fallback values
            sqrt_recip_alphas_cumprod_t = torch.ones_like(x_t)
            sqrt_recipm1_alphas_cumprod_t = torch.zeros_like(x_t)

        # Compute predicted x_0 with clipping for stability
        try:
            pred_x_0 = sqrt_recip_alphas_cumprod_t * x_t - sqrt_recipm1_alphas_cumprod_t * pred_noise

            # Clip predicted x_0 to reasonable range
            if pred_x_0.abs().max().item() > 100:
                # Use debug level instead of warning to reduce console output
                logger.debug(f"Extreme values in pred_x_0: {pred_x_0.abs().max().item()}")
                pred_x_0 = torch.clamp(pred_x_0, -100, 100)
        except Exception as e:
            logger.error(f"Error computing pred_x_0: {e}")
            # Fallback to input with small noise
            pred_x_0 = x_t + torch.randn_like(x_t) * 0.01

        # Compute posterior mean and variance with error handling
        try:
            posterior_mean_coef1_t = extract(self.posterior_mean_coef1, t, x_t.shape)
            posterior_mean_coef2_t = extract(self.posterior_mean_coef2, t, x_t.shape)

            # Check coefficients
            if torch.isnan(posterior_mean_coef1_t).any() or torch.isinf(posterior_mean_coef1_t).any():
                logger.warning("NaN or Inf in posterior_mean_coef1_t")
                posterior_mean_coef1_t = torch.zeros_like(posterior_mean_coef1_t)

            if torch.isnan(posterior_mean_coef2_t).any() or torch.isinf(posterior_mean_coef2_t).any():
                logger.warning("NaN or Inf in posterior_mean_coef2_t")
                posterior_mean_coef2_t = torch.ones_like(posterior_mean_coef2_t)

            # Compute mean
            pred_mean = posterior_mean_coef1_t * pred_x_0 + posterior_mean_coef2_t * x_t

            # Check mean
            if torch.isnan(pred_mean).any() or torch.isinf(pred_mean).any():
                logger.warning("NaN or Inf in pred_mean")
                pred_mean = x_t  # Fallback to current state
        except Exception as e:
            logger.error(f"Error computing posterior mean: {e}")
            # Fallback to current state
            pred_mean = x_t

        # Extract posterior variance and log variance with error handling
        try:
            posterior_variance_t = extract(self.posterior_variance, t, x_t.shape)
            posterior_log_variance_clipped_t = extract(self.posterior_log_variance_clipped, t, x_t.shape)

            # Check variance
            if torch.isnan(posterior_variance_t).any() or torch.isinf(posterior_variance_t).any() or (posterior_variance_t <= 0).any():
                logger.warning("Invalid values in posterior_variance_t")
                # Replace with small positive values
                posterior_variance_t = torch.ones_like(posterior_variance_t) * 1e-4
                posterior_log_variance_clipped_t = torch.log(posterior_variance_t)

            # Check log variance
            if torch.isnan(posterior_log_variance_clipped_t).any() or torch.isinf(posterior_log_variance_clipped_t).any():
                logger.warning("NaN or Inf in posterior_log_variance_clipped_t")
                # Recompute from variance
                posterior_log_variance_clipped_t = torch.log(posterior_variance_t.clamp(min=1e-8))

            # Expand dimensions to match x_t shape
            if posterior_variance_t.shape != x_t.shape:
                posterior_variance_t = posterior_variance_t.expand(x_t.shape)
            if posterior_log_variance_clipped_t.shape != x_t.shape:
                posterior_log_variance_clipped_t = posterior_log_variance_clipped_t.expand(x_t.shape)
        except Exception as e:
            logger.error(f"Error computing posterior variance: {e}")
            # Fallback to small constant variance
            posterior_variance_t = torch.ones_like(x_t) * 1e-4
            posterior_log_variance_clipped_t = torch.log(posterior_variance_t)

        return pred_mean, posterior_variance_t, posterior_log_variance_clipped_t

    def p_sample(self, x_t: torch.Tensor, t: torch.Tensor, y: Optional[torch.Tensor] = None, use_ema: bool = True, clip_denoised: bool = True) -> torch.Tensor:
        """
        Sample from p(x_{t-1} | x_t) (reverse diffusion process) with enhanced numerical stability.

        Args:
            x_t (torch.Tensor): Noisy sample at timestep t
            t (torch.Tensor): Timesteps
            y (torch.Tensor, optional): Class labels
            use_ema (bool): Whether to use EMA model
            clip_denoised (bool): Whether to clip the denoised signal to [-1, 1]

        Returns:
            torch.Tensor: Sample from p(x_{t-1} | x_t)
        """
        # Check for NaN or Inf values in input
        if torch.isnan(x_t).any() or torch.isinf(x_t).any():
            logger.debug(f"NaN or Inf detected in input tensor at timestep {t[0].item()}")
            # Replace NaN and Inf values with zeros
            x_t = torch.nan_to_num(x_t, nan=0.0, posinf=0.0, neginf=0.0)

        # Compute mean and variance with error handling
        try:
            mean, variance, log_variance = self.p_mean_variance(x_t, t, y, use_ema)

            # Check for NaN or Inf values in mean and variance
            if torch.isnan(mean).any() or torch.isinf(mean).any():
                logger.debug(f"NaN or Inf detected in mean at timestep {t[0].item()}")
                mean = torch.nan_to_num(mean, nan=0.0, posinf=0.0, neginf=0.0)

            if torch.isnan(variance).any() or torch.isinf(variance).any() or (variance < 0).any():
                logger.debug(f"Invalid variance at timestep {t[0].item()}")
                # Replace with small positive values
                variance = torch.ones_like(variance) * 1e-4
                log_variance = torch.log(variance)

            # Ensure log_variance is finite
            if torch.isnan(log_variance).any() or torch.isinf(log_variance).any():
                logger.debug(f"NaN or Inf detected in log_variance at timestep {t[0].item()}")
                # Recompute from variance
                log_variance = torch.log(variance.clamp(min=1e-8))
        except Exception as e:
            logger.error(f"Error in p_mean_variance at timestep {t[0].item()}: {e}")
            # Fallback to simple denoising
            mean = x_t
            variance = torch.ones_like(x_t) * 1e-4
            log_variance = torch.log(variance)

        # Generate noise
        noise = torch.randn_like(x_t)

        # No noise when t == 0
        nonzero_mask = (t != 0).float().view(-1, *([1] * (len(x_t.shape) - 1)))

        # Sample from the posterior with numerical stability
        try:
            # Compute standard deviation with clipping to avoid numerical issues
            std_dev = torch.exp(0.5 * log_variance).clamp(min=1e-8)

            # Generate sample
            sample = mean + nonzero_mask * std_dev * noise

            # Check for NaN or Inf values in sample
            if torch.isnan(sample).any() or torch.isinf(sample).any():
                logger.debug(f"NaN or Inf detected in sample at timestep {t[0].item()}")
                # Fall back to mean
                sample = mean
        except Exception as e:
            logger.error(f"Error in sampling at timestep {t[0].item()}: {e}")
            # Fallback to mean
            sample = mean

        # Apply smooth normalization for extreme values
        if clip_denoised:
            # Check for extreme values
            max_val = sample.abs().max().item()
            if max_val > 10.0:
                logger.debug(f"Extreme values detected in sample: max_abs={max_val:.4f}")
                # Apply tanh normalization for smooth clipping
                sample = torch.tanh(sample * 0.1) * 10.0

            # Final hard clipping to [-1, 1]
            sample = torch.clamp(sample, -1.0, 1.0)

        return sample

    @torch.no_grad()
    def p_sample_loop(
        self,
        shape: Tuple[int, ...],
        y: Optional[torch.Tensor] = None,
        use_ema: bool = True,
        clip_denoised: bool = True,
        progress_callback: Optional[Callable[[int, int, torch.Tensor], None]] = None
    ) -> torch.Tensor:
        """
        Generate samples from the model using the reverse diffusion process with enhanced stability.

        Args:
            shape (Tuple[int, ...]): Shape of the samples to generate
            y (torch.Tensor, optional): Class labels
            use_ema (bool): Whether to use EMA model
            clip_denoised (bool): Whether to clip the denoised signal to [-1, 1]
            progress_callback (Callable, optional): Callback function for progress updates

        Returns:
            torch.Tensor: Generated samples
        """
        device = next(self.model.parameters()).device

        # 记录内存使用情况（采样开始）
        if torch.cuda.is_available():
            start_memory = torch.cuda.memory_allocated() / 1024**2
            logger.info(f"采样开始内存: {start_memory:.2f} MB")

        # 检查 EMA 模型是否可用
        if use_ema and self.ema_model is None:
            logger.warning("EMA 模型尚未初始化，使用原始模型进行采样")
            use_ema = False

        # Start from pure noise
        x = torch.randn(shape, device=device)

        # Check for NaN or Inf values in initial noise
        if torch.isnan(x).any() or torch.isinf(x).any():
            logger.warning("NaN or Inf detected in initial noise, replacing with new noise")
            x = torch.randn(shape, device=device)

        # Track statistics for monitoring
        min_vals = []
        max_vals = []
        has_nan_inf = []

        # Iterate through all timesteps
        for t in reversed(range(self.num_timesteps)):
            # Create a batch of the same timestep
            t_batch = torch.full((shape[0],), t, device=device, dtype=torch.long)

            # Log progress more frequently for debugging
            log_interval = 100 if t >= 100 else 10
            if t % log_interval == 0 or t == self.num_timesteps - 1:
                # Log current tensor statistics
                with torch.no_grad():
                    x_min = x.min().item()
                    x_max = x.max().item()
                    x_mean = x.mean().item()
                    x_std = x.std().item()
                    has_nan = torch.isnan(x).any().item()
                    has_inf = torch.isinf(x).any().item()

                min_vals.append(x_min)
                max_vals.append(x_max)
                has_nan_inf.append(has_nan or has_inf)

                # Use debug level for detailed sampling progress to reduce console output
                logger.debug(
                    f"[GaussianDiffusion.p_sample_loop] Sampling step {t}/{self.num_timesteps}, "
                    f"x: shape={x.shape}, min={x_min:.4f}, max={x_max:.4f}, "
                    f"mean={x_mean:.4f}, std={x_std:.4f}, "
                    f"has_nan={has_nan}, has_inf={has_inf}"
                )

            # Sample from p(x_{t-1} | x_t) with enhanced stability
            try:
                x = self.p_sample(x, t_batch, y, use_ema, clip_denoised)

                # Apply additional safeguards for numerical stability
                if t % 100 == 0:  # Periodically check and correct
                    # Check for extreme values
                    if x.abs().max().item() > 100:
                        logger.debug(f"Extreme values detected at step {t}, applying tanh normalization")
                        # Apply smooth normalization
                        x = torch.tanh(x * 0.01) * 100

                    # Ensure tensor is on the correct device
                    if x.device != device:
                        logger.debug(f"Tensor moved to {x.device}, moving back to {device}")
                        x = x.to(device)
            except Exception as e:
                logger.error(f"Error in sampling step {t}: {e}")
                # If an error occurs, try to recover
                if t > 0:
                    # Generate new noise for this step
                    logger.info(f"Attempting recovery at step {t}")
                    recovery_noise = torch.randn(shape, device=device) * (t / self.num_timesteps)
                    x = x * 0.5 + recovery_noise * 0.5  # Blend with recovery noise
                else:
                    # For the final steps, just clamp values
                    logger.info("Applying emergency clamping for final steps")
                    x = torch.clamp(x, -1.0, 1.0)

            # Call progress callback if provided
            if progress_callback is not None:
                try:
                    progress_callback(t, self.num_timesteps, x)
                except Exception as e:
                    logger.warning(f"Error in progress callback: {e}")

        # Final processing
        # Ensure output is in [-1, 1] range
        x = torch.clamp(x, -1.0, 1.0)

        # Log sampling statistics
        logger.info(
            f"Sampling complete. Value ranges during sampling: "
            f"min={min(min_vals):.4f} to {max(max_vals):.4f}, "
            f"NaN/Inf occurred: {any(has_nan_inf)}"
        )

        # 记录内存使用情况（采样结束）
        if torch.cuda.is_available():
            end_memory = torch.cuda.memory_allocated() / 1024**2
            peak_memory = torch.cuda.max_memory_allocated() / 1024**2
            logger.info(f"采样结束内存: {end_memory:.2f} MB, 峰值: {peak_memory:.2f} MB")
            if 'start_memory' in locals():
                logger.info(f"采样内存变化: {end_memory - start_memory:.2f} MB")

            # 清理内存
            torch.cuda.empty_cache()

        return x

    @torch.no_grad()
    def sample(
        self,
        batch_size: int,
        y: Optional[torch.Tensor] = None,
        use_ema: bool = True,
        clip_denoised: bool = True,
        progress_callback: Optional[Callable[[int, int, torch.Tensor], None]] = None
    ) -> torch.Tensor:
        """
        Generate samples from the model with enhanced EMA handling and stability.

        Args:
            batch_size (int): Number of samples to generate
            y (torch.Tensor, optional): Class labels
            use_ema (bool): Whether to use EMA model
            clip_denoised (bool): Whether to clip the denoised signal to [-1, 1]
            progress_callback (Callable, optional): Callback function for progress updates

        Returns:
            torch.Tensor: Generated samples
        """
        logger.info(f"Generating {batch_size} samples with {'EMA' if use_ema else 'regular'} model")

        # 验证EMA模型是否可用
        if use_ema:
            # 检查各种可能的EMA模型存储方式
            if hasattr(self, 'ema_model') and self.ema_model is not None:
                # 记录EMA模型状态
                if isinstance(self.ema_model, nn.Module):
                    logger.info("Using EMA model (Module instance)")
                elif isinstance(self.ema_model, dict):
                    logger.info("Using EMA model (state dict)")
                else:
                    logger.info(f"Using EMA model (type: {type(self.ema_model)})")
            # 检查是否有_ema_state_dict属性
            elif hasattr(self, '_ema_state_dict') and self._ema_state_dict is not None:
                logger.info("Using EMA model (_ema_state_dict)")
            else:
                logger.warning("EMA model requested but not available, falling back to regular model")
                use_ema = False

        # Define sample shape
        shape = (batch_size, self.img_channels, self.img_size[0], self.img_size[1])

        # Generate samples with enhanced stability
        try:
            samples = self.p_sample_loop(
                shape=shape,
                y=y,
                use_ema=use_ema,
                clip_denoised=clip_denoised,
                progress_callback=progress_callback
            )

            # Final validation
            if torch.isnan(samples).any() or torch.isinf(samples).any():
                logger.warning("NaN or Inf values in final samples, applying nan_to_num")
                samples = torch.nan_to_num(samples, nan=0.0, posinf=1.0, neginf=-1.0)

            # Ensure samples are in [-1, 1] range
            min_val = samples.min().item()
            max_val = samples.max().item()
            if min_val < -1 or max_val > 1:
                logger.warning(f"Final samples out of range: [{min_val:.4f}, {max_val:.4f}], clamping to [-1, 1]")
                samples = torch.clamp(samples, -1.0, 1.0)

            logger.info(f"Successfully generated {batch_size} samples with range [{samples.min().item():.4f}, {samples.max().item():.4f}]")
            return samples

        except Exception as e:
            logger.error(f"Error generating samples: {e}")
            # Emergency fallback - return random noise
            logger.warning("Returning random noise as emergency fallback")
            noise = torch.randn(shape, device=next(self.model.parameters()).device)
            return torch.clamp(noise, -1.0, 1.0)

    def compute_feature_matching_loss(self, real_features: Dict[str, torch.Tensor],
                                 fake_features: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        计算特征匹配损失，根据不同通道应用不同的权重。
        平衡内存优化版本：使用BALANCED_KEY_LAYERS配置，渐进式特征释放，混合精度计算。

        Args:
            real_features (Dict[str, torch.Tensor]): 真实样本的特征
            fake_features (Dict[str, torch.Tensor]): 生成样本的特征

        Returns:
            torch.Tensor: 特征匹配损失值
        """
        total_loss = 0.0
        device = next(self.model.parameters()).device

        # 记录内存使用情况（开始）
        if torch.cuda.is_available():
            start_memory = torch.cuda.memory_allocated() / 1024**2
            logger.info(f"特征匹配损失计算开始内存: {start_memory:.2f} MB")

        # 使用平衡的特征层配置进行特征匹配
        # 为每个通道分别计算损失，然后加权平均
        logger.info("使用平衡的特征层配置进行特征匹配")

        for channel_id in range(3):
            channel_loss = 0.0

            # 获取该通道的权重配置
            weights = HTRU1_FEATURE_WEIGHTS[channel_id]

            # 获取该通道的特征层配置
            channel_layers = BALANCED_KEY_LAYERS[channel_id]

            # 记录当前通道的特征数量
            channel_features = []
            for layer_type in ['low', 'mid', 'high']:
                for key in channel_layers[layer_type]:
                    if key in real_features and key in fake_features:
                        channel_features.append(key)

            logger.info(f"通道 {channel_id} 特征数量: {len(channel_features)}")

            # 处理每种层级类型
            for layer_type in ['low', 'mid', 'high']:
                layer_loss = 0.0
                layer_count = 0

                # 处理平衡关键特征层
                for key in channel_layers[layer_type]:
                    if key in real_features and key in fake_features:
                        # 记录特征大小
                        feat_shape = real_features[key].shape
                        logger.info(f"处理特征 {key}, 形状: {feat_shape}")

                        # 记录内存使用情况（特征处理前）
                        if torch.cuda.is_available():
                            pre_feat_memory = torch.cuda.memory_allocated() / 1024**2

                        # 将特征移回 GPU 并恢复精度，一次只处理一个特征
                        real_feat = real_features[key].to(device=device, dtype=torch.float32)

                        # 计算均值以减少内存使用
                        real_mean = real_feat.mean(0)

                        # 立即释放不再需要的张量
                        del real_feat
                        torch.cuda.empty_cache()

                        # 处理生成特征
                        fake_feat = fake_features[key].to(device=device, dtype=torch.float32)
                        fake_mean = fake_feat.mean(0)

                        # 立即释放不再需要的张量
                        del fake_feat
                        torch.cuda.empty_cache()

                        # 计算损失
                        feat_loss = F.mse_loss(real_mean, fake_mean)
                        layer_loss += feat_loss
                        layer_count += 1

                        # 立即释放不再需要的张量
                        del real_mean, fake_mean
                        torch.cuda.empty_cache()

                        # 记录内存使用情况（特征处理后）
                        if torch.cuda.is_available():
                            post_feat_memory = torch.cuda.memory_allocated() / 1024**2
                            logger.info(f"特征 {key} 处理内存变化: {post_feat_memory - pre_feat_memory:.2f} MB")

                # 添加加权层级损失
                if layer_count > 0:
                    weighted_loss = weights[layer_type] * (layer_loss / layer_count)
                    channel_loss += weighted_loss
                    logger.info(f"通道 {channel_id} {layer_type} 层损失: {weighted_loss.item():.6f}")

            # 添加到总损失
            total_loss += channel_loss
            logger.info(f"通道 {channel_id} 总损失: {channel_loss.item():.6f}")

        # 平均每个通道的损失
        total_loss /= 3.0

        # 最终清理
        torch.cuda.empty_cache()

        # 记录内存使用情况（结束）
        if torch.cuda.is_available():
            end_memory = torch.cuda.memory_allocated() / 1024**2
            logger.info(f"特征匹配损失计算结束内存: {end_memory:.2f} MB")
            logger.info(f"特征匹配损失计算内存变化: {end_memory - start_memory:.2f} MB")

        return total_loss

    def get_loss(self, x_0: torch.Tensor, t: torch.Tensor, y: Optional[torch.Tensor] = None,
                noise: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Compute the loss for training.

        Args:
            x_0 (torch.Tensor): Clean images
            t (torch.Tensor): Timesteps
            y (torch.Tensor, optional): Class labels
            noise (torch.Tensor, optional): Noise to add. If None, random noise is used.

        Returns:
            torch.Tensor: Loss value
        """
        # Log tensor shapes for debugging
        logger.debug(f"[GaussianDiffusion.get_loss] x_0: shape={x_0.shape}")
        logger.debug(f"[GaussianDiffusion.get_loss] t: shape={t.shape}")
        if y is not None:
            logger.debug(f"[GaussianDiffusion.get_loss] y: shape={y.shape}")

        # 记录内存使用情况（开始）
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()
            start_memory = torch.cuda.memory_allocated() / 1024**2
            logger.info(f"内存使用（特征提取前）: {start_memory:.2f} MB")

        # Generate random noise if not provided
        if noise is None:
            noise = torch.randn_like(x_0)

        # Add noise to the input
        x_t = self.q_sample(x_0, t, noise)

        # 基本损失计算
        if self.use_feature_matching:
            # 使用平衡的特征层配置，在内存效率和特征丰富度之间取得平衡
            # 构建平衡特征层列表
            balanced_key_layers = []
            for channel_id in range(3):
                for layer_type in ['low', 'mid', 'high']:
                    balanced_key_layers.extend(BALANCED_KEY_LAYERS[channel_id][layer_type])
            # 去重
            balanced_key_layers = list(set(balanced_key_layers))

            logger.info(f"使用平衡特征层: {balanced_key_layers}")

            # 记录内存使用情况（特征提取前）
            if torch.cuda.is_available():
                pre_features_memory = torch.cuda.memory_allocated() / 1024**2
                logger.info(f"内存使用（特征提取前）: {pre_features_memory:.2f} MB")

            # 使用特征匹配损失
            # 获取真实样本的特征，只提取平衡关键层
            _, real_features = self.model(x_0, t, y, return_features=True, feature_layers=balanced_key_layers)

            # 记录内存使用情况（真实特征提取后）
            if torch.cuda.is_available():
                post_real_memory = torch.cuda.memory_allocated() / 1024**2
                logger.info(f"内存使用（真实特征提取后）: {post_real_memory:.2f} MB")
                logger.info(f"真实特征提取内存增加: {post_real_memory - pre_features_memory:.2f} MB")

            # 预测噪声并获取生成样本的特征，只提取平衡关键层
            pred_noise, fake_features = self.model(x_t, t, y, return_features=True, feature_layers=balanced_key_layers)

            # 记录内存使用情况（生成特征提取后）
            if torch.cuda.is_available():
                post_fake_memory = torch.cuda.memory_allocated() / 1024**2
                logger.info(f"内存使用（生成特征提取后）: {post_fake_memory:.2f} MB")
                logger.info(f"生成特征提取内存增加: {post_fake_memory - post_real_memory:.2f} MB")

            # OPTIMIZATION 2.1: Compute base loss with channel weighting if enabled
            if self.use_channel_weighted_loss and self.channel_weighted_loss is not None:
                # Use channel-weighted loss for pulsar-specific optimization
                base_loss = self.channel_weighted_loss(pred_noise, noise)
                logger.debug(f"Using ChannelWeightedLoss (with feature matching): {base_loss.item():.6f}")
            else:
                # Standard base loss computation
                if self.loss_type == "l1":
                    base_loss = F.l1_loss(pred_noise, noise)
                elif self.loss_type == "l2":
                    base_loss = F.mse_loss(pred_noise, noise)
                else:
                    raise ValueError(f"Unknown loss type: {self.loss_type}")

            # 计算特征匹配损失
            feature_loss = self.compute_feature_matching_loss(real_features, fake_features)

            # 记录内存使用情况（特征匹配损失计算后）
            if torch.cuda.is_available():
                post_loss_memory = torch.cuda.memory_allocated() / 1024**2
                logger.info(f"内存使用（特征匹配损失计算后）: {post_loss_memory:.2f} MB")
                logger.info(f"特征匹配损失计算内存变化: {post_loss_memory - post_fake_memory:.2f} MB")

            # 显式释放特征
            del real_features, fake_features
            torch.cuda.empty_cache()

            # 记录内存使用情况（特征释放后）
            if torch.cuda.is_available():
                post_cleanup_memory = torch.cuda.memory_allocated() / 1024**2
                logger.info(f"内存使用（特征释放后）: {post_cleanup_memory:.2f} MB")
                logger.info(f"特征释放内存减少: {post_cleanup_memory - post_loss_memory:.2f} MB")

            # 组合损失
            loss = base_loss + self.feature_matching_weight * feature_loss

            # 记录日志
            logger.debug(f"Base loss: {base_loss.item():.4f}, Feature matching loss: {feature_loss.item():.4f}")
        else:
            # 标准损失计算
            # Predict the noise using the model
            pred_noise = self.model(x_t, t, y)

            # OPTIMIZATION 2.1: Apply ChannelWeightedLoss if enabled
            if self.use_channel_weighted_loss and self.channel_weighted_loss is not None:
                # Use channel-weighted loss for pulsar-specific optimization
                loss = self.channel_weighted_loss(pred_noise, noise)
                logger.debug(f"Using ChannelWeightedLoss: {loss.item():.6f}")
            else:
                # Standard loss computation
                if self.loss_type == "l1":
                    loss = F.l1_loss(pred_noise, noise)
                elif self.loss_type == "l2":
                    loss = F.mse_loss(pred_noise, noise)
                else:
                    raise ValueError(f"Unknown loss type: {self.loss_type}")

        # 记录内存使用情况（结束）
        if torch.cuda.is_available() and self.use_feature_matching:
            end_memory = torch.cuda.memory_allocated() / 1024**2
            peak_memory = torch.cuda.max_memory_allocated() / 1024**2
            logger.info(f"内存使用（结束）: {end_memory:.2f} MB, 峰值: {peak_memory:.2f} MB")
            logger.info(f"总内存变化: {end_memory - start_memory:.2f} MB")

        return loss

    def forward(self, x_0: torch.Tensor, y: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass for training.

        Args:
            x_0 (torch.Tensor): Clean images
            y (torch.Tensor, optional): Class labels

        Returns:
            torch.Tensor: Loss value
        """
        # Check input shape
        b, _, height, width = x_0.shape  # 使用 b 作为批次大小变量名，保持与后续代码一致
        if height != self.img_size[0] or width != self.img_size[1]:
            raise ValueError(f"Input image size ({height}x{width}) doesn't match expected size ({self.img_size[0]}x{self.img_size[1]})")

        # Sample random timesteps based on curriculum learning stage
        device = x_0.device

        if self.curriculum_learning:
            # Get current timestep range based on curriculum stage with smooth transitions
            min_t, max_t = self.get_curriculum_timestep_range()

            # Get current stage for alpha parameter selection
            current_stage = self.curriculum_metrics['stage']
            stage_progress = self.curriculum_metrics['stage_progress']

            # Get alpha parameter for current stage (controls distribution shape)
            # Lower alpha concentrates sampling on lower timesteps (easier)
            # Higher alpha makes sampling more uniform
            if current_stage < len(self.noise_distribution_alphas):
                base_alpha = self.noise_distribution_alphas[current_stage]

                # If in transition and not in final stage, blend with next stage's alpha
                if self.curriculum_metrics.get('in_transition', False) and current_stage < len(self.noise_distribution_alphas) - 1:
                    next_alpha = self.noise_distribution_alphas[current_stage + 1]
                    transition_progress = self.curriculum_metrics['transition_progress']
                    alpha = base_alpha * (1 - transition_progress) + next_alpha * transition_progress
                else:
                    alpha = base_alpha
            else:
                # Default to uniform distribution if stage is out of range
                alpha = 1.0

            # Apply adaptive noise scaling
            adaptive_scale = self.curriculum_metrics.get('adaptive_noise_scale', 1.0)
            alpha = alpha * adaptive_scale

            # Clamp alpha to reasonable range
            alpha = max(0.05, min(alpha, 2.0))

            logger.debug(f"Curriculum learning: stage={current_stage}, progress={stage_progress:.2f}, "
                        f"timestep range=[{min_t}, {max_t}], alpha={alpha:.2f}")

            # Sample timesteps using non-uniform distribution
            if alpha < 0.99:  # Use non-uniform sampling only if alpha is significantly different from 1
                # Generate uniform samples
                u = torch.rand(b, device=device)

                # Transform to power distribution with parameter alpha
                # This concentrates samples toward lower values when alpha < 1
                # and toward higher values when alpha > 1
                p = u ** (1.0 / alpha)

                # Scale to timestep range and convert to integers
                t = (min_t + p * (max_t - min_t + 1)).long().clamp(min_t, max_t)
            else:
                # Use uniform sampling when alpha is close to 1
                t = torch.randint(min_t, max_t + 1, (b,), device=device, dtype=torch.long)
        else:
            # Standard timestep sampling
            t = torch.randint(0, self.num_timesteps, (b,), device=device, dtype=torch.long)

        # Compute loss
        loss = self.get_loss(x_0, t, y)

        # Track loss for curriculum learning
        if self.curriculum_learning:
            self.curriculum_metrics['loss_history'].append(loss.item())

        return loss
