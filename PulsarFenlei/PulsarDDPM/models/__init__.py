"""
Model components for PulsarWGAN-VAE.
WGAN-GP+VAE混合架构组件

注意：为避免循环导入，请直接导入具体模块：
- from models.vae import PulsarVAE
- from models.discriminator import PulsarWGANDiscriminator
- from models.wgan_vae import PulsarWGANVAE
"""

# 延迟导入以避免循环依赖
def get_vae_components():
    from .vae import PulsarVAEEncoder, PulsarVAEDecoder, PulsarVAE, compute_vae_loss
    return PulsarVAEEncoder, PulsarVAEDecoder, PulsarVAE, compute_vae_loss

def get_discriminator_components():
    from .discriminator import (
        PulsarWGANDiscriminator, compute_gradient_penalty,
        compute_wgan_gp_loss, WGANGPTrainer
    )
    return PulsarWGANDiscriminator, compute_gradient_penalty, compute_wgan_gp_loss, WGANGPTrainer

def get_wgan_vae_components():
    from .wgan_vae import PulsarWGANVAE, PulsarWGANVAETrainer, create_model_and_trainer
    return PulsarWGANVAE, PulsarWGANVAETrainer, create_model_and_trainer

def get_utils():
    from .ema import EMA
    from .utils import get_norm
    return EMA, get_norm

__all__ = [
    'get_vae_components', 'get_discriminator_components',
    'get_wgan_vae_components', 'get_utils'
]
