"""
Model components for PulsarWGAN-VAE.
WGAN-GP+VAE混合架构组件
"""

from .vae import Pulsar<PERSON><PERSON><PERSON>der, PulsarVAEDecoder, PulsarVAE, compute_vae_loss
from .discriminator import (
    PulsarWGANDiscriminator, compute_gradient_penalty,
    compute_wgan_gp_loss, WGANGPTrainer
)
from .wgan_vae import Pulsar<PERSON><PERSON>NVA<PERSON>, PulsarWGANVAETrainer, create_model_and_trainer
from .ema import EMA
from .utils import get_norm

__all__ = [
    'PulsarVAEEncoder', 'PulsarVAEDecoder', 'PulsarVAE', 'compute_vae_loss',
    'PulsarWGANDiscriminator', 'compute_gradient_penalty', 'compute_wgan_gp_loss', 'WGANGPTrainer',
    'PulsarWGANVAE', 'PulsarWGANVAETrainer', 'create_model_and_trainer',
    'EMA', 'get_norm'
]
