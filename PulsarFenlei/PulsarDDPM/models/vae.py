#!/usr/bin/env python3
"""
VAE编码器和解码器模块
PulsarWGAN-VAE混合架构的核心VAE组件

基于WGAN-GP+VAE实施计划的技术规格：
- 编码器: 400K参数，5层CNN
- 解码器: 500K参数，6层转置卷积
- 潜在维度: 64维
- 输入/输出: 32x32x3 (Period-DM, Phase-Subband, Phase-Subintegration)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Dict

class PulsarVAEEncoder(nn.Module):
    """
    脉冲星VAE编码器 (轻量化设计)

    架构规格:
    - 输入: 32x32x3 → 输出: μ,σ ∈ R^64
    - 参数量: ~400K
    - 特殊设计: 轻量化CNN，保持物理特征
    """

    def __init__(self, latent_dim: int = 64, input_channels: int = 3):
        super().__init__()
        self.latent_dim = latent_dim
        self.input_channels = input_channels

        # 轻量化编码器 - 4层CNN
        self.encoder = nn.Sequential(
            # Layer 1: 32x32x3 → 16x16x16
            nn.Conv2d(input_channels, 16, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(16),
            nn.LeakyReLU(0.2, inplace=True),

            # Layer 2: 16x16x16 → 8x8x32
            nn.Conv2d(16, 32, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(32),
            nn.LeakyReLU(0.2, inplace=True),

            # Layer 3: 8x8x32 → 4x4x64
            nn.Conv2d(32, 64, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2, inplace=True),

            # Layer 4: 4x4x64 → 2x2x128
            nn.Conv2d(64, 128, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),

            # 全局平均池化
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten()
        )

        # 轻量化全连接层
        self.fc = nn.Sequential(
            nn.Linear(128, 256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3)
        )

        # μ和σ分支
        self.fc_mu = nn.Linear(256, latent_dim)
        self.fc_logvar = nn.Linear(256, latent_dim)

        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='leaky_relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='leaky_relu')
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, 3, 32, 32]
            
        Returns:
            mu: 均值 [batch_size, latent_dim]
            logvar: 对数方差 [batch_size, latent_dim]
        """
        # 编码器前向传播
        h = self.encoder(x)
        h = self.fc(h)
        
        # 计算μ和logvar
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        
        return mu, logvar

class PulsarVAEDecoder(nn.Module):
    """
    脉冲星VAE解码器 (轻量化设计)

    架构规格:
    - 输入: z ∈ R^64 → 输出: 32x32x3
    - 参数量: ~500K
    - 特殊设计: 精确尺寸控制，三通道独立处理能力
    """

    def __init__(self, latent_dim: int = 64, output_channels: int = 3):
        super().__init__()
        self.latent_dim = latent_dim
        self.output_channels = output_channels

        # 轻量化全连接层
        self.fc = nn.Sequential(
            nn.Linear(latent_dim, 256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),

            nn.Linear(256, 2 * 2 * 128),
            nn.LeakyReLU(0.2, inplace=True)
        )

        # 精确尺寸控制的解码器 - 4层转置卷积
        self.decoder = nn.Sequential(
            # Layer 1: 2x2x128 → 4x4x64
            nn.ConvTranspose2d(128, 64, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2, inplace=True),

            # Layer 2: 4x4x64 → 8x8x32
            nn.ConvTranspose2d(64, 32, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(32),
            nn.LeakyReLU(0.2, inplace=True),

            # Layer 3: 8x8x32 → 16x16x16
            nn.ConvTranspose2d(32, 16, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(16),
            nn.LeakyReLU(0.2, inplace=True),

            # Layer 4: 16x16x16 → 32x32x3
            nn.ConvTranspose2d(16, output_channels, kernel_size=4, stride=2, padding=1),
            nn.Tanh()  # 输出范围[-1,1]匹配HTRU1归一化
        )

        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, (nn.ConvTranspose2d, nn.Conv2d)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='leaky_relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='leaky_relu')
                nn.init.constant_(m.bias, 0)
    
    def forward(self, z: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            z: 潜在向量 [batch_size, latent_dim]

        Returns:
            x_recon: 重建图像 [batch_size, 3, 32, 32]
        """
        # 全连接层
        h = self.fc(z)

        # 重塑为4D张量 (2x2x128)
        h = h.view(h.size(0), 128, 2, 2)

        # 解码器前向传播
        x_recon = self.decoder(h)

        return x_recon

class PulsarVAE(nn.Module):
    """
    完整的脉冲星VAE模型
    
    集成编码器和解码器，实现重参数化技巧
    """
    
    def __init__(self, latent_dim: int = 64, input_channels: int = 3):
        super().__init__()
        self.latent_dim = latent_dim
        self.input_channels = input_channels
        
        # 编码器和解码器
        self.encoder = PulsarVAEEncoder(latent_dim, input_channels)
        self.decoder = PulsarVAEDecoder(latent_dim, input_channels)
        
        # 统计参数量
        self.total_params = sum(p.numel() for p in self.parameters())
    
    def reparameterize(self, mu: torch.Tensor, logvar: torch.Tensor) -> torch.Tensor:
        """
        重参数化技巧
        
        Args:
            mu: 均值 [batch_size, latent_dim]
            logvar: 对数方差 [batch_size, latent_dim]
            
        Returns:
            z: 采样的潜在向量 [batch_size, latent_dim]
        """
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        z = mu + eps * std
        return z
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入图像 [batch_size, 3, 32, 32]
            
        Returns:
            x_recon: 重建图像 [batch_size, 3, 32, 32]
            mu: 均值 [batch_size, latent_dim]
            logvar: 对数方差 [batch_size, latent_dim]
        """
        # 编码
        mu, logvar = self.encoder(x)
        
        # 重参数化
        z = self.reparameterize(mu, logvar)
        
        # 解码
        x_recon = self.decoder(z)
        
        return x_recon, mu, logvar
    
    def sample(self, num_samples: int, device: torch.device) -> torch.Tensor:
        """
        从先验分布采样生成新样本
        
        Args:
            num_samples: 生成样本数量
            device: 设备
            
        Returns:
            samples: 生成的样本 [num_samples, 3, 32, 32]
        """
        self.eval()
        with torch.no_grad():
            # 从标准正态分布采样
            z = torch.randn(num_samples, self.latent_dim, device=device)
            
            # 解码生成样本
            samples = self.decoder(z)
        
        return samples

def compute_vae_loss(x_recon: torch.Tensor, x: torch.Tensor, 
                     mu: torch.Tensor, logvar: torch.Tensor, 
                     beta: float = 1.0) -> Dict[str, torch.Tensor]:
    """
    计算VAE损失函数
    
    Args:
        x_recon: 重建图像 [batch_size, 3, 32, 32]
        x: 原始图像 [batch_size, 3, 32, 32]
        mu: 均值 [batch_size, latent_dim]
        logvar: 对数方差 [batch_size, latent_dim]
        beta: KL散度权重
        
    Returns:
        loss_dict: 包含各项损失的字典
    """
    batch_size = x.size(0)
    
    # 重建损失 (MSE)
    recon_loss = F.mse_loss(x_recon, x, reduction='sum') / batch_size
    
    # KL散度损失
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp()) / batch_size
    
    # 总损失
    total_loss = recon_loss + beta * kl_loss
    
    return {
        'total_loss': total_loss,
        'recon_loss': recon_loss,
        'kl_loss': kl_loss
    }
