#!/usr/bin/env python3
"""
PulsarAdaptiveSampler - 专为PulsarTransformerDDPM设计的数值稳定采样器
完全重构的架构，确保与PulsarTransformerDDPM的完美兼容性和数值稳定性
"""

import torch
import torch.nn as nn
import logging
from typing import Optional, Union, Tuple
from tqdm import tqdm

logger = logging.getLogger(__name__)


class PulsarAdaptiveSampler:
    """
    专为PulsarTransformerDDPM设计的自适应采样器

    核心设计原则：
    1. 数值稳定性优先：避免所有可能导致NaN/Inf的数学操作
    2. 架构适配：专门适配PulsarTransformerDDPM的输出特性
    3. 性能保持：在稳定性基础上保持高效采样
    4. 脉冲星优化：针对脉冲星数据的特殊处理
    """

    def __init__(
        self,
        model: nn.Module,
        num_timesteps: int = 1000,
        beta_schedule: str = "cosine",
        beta_start: float = 0.0001,
        beta_end: float = 0.02,
        device: str = "cuda",
        # 数值稳定性参数
        numerical_stability: bool = True,
        safe_division_threshold: float = 1e-6,
        range_clamp_strength: float = 0.95,
        # 自适应参数
        adaptive_step_size: bool = True,
        min_step_ratio: float = 0.5,
        max_step_ratio: float = 2.0,
        # 脉冲星特定参数
        pulsar_optimization: bool = True,
        channel_aware: bool = True
    ):
        """
        初始化PulsarAdaptiveSampler

        Args:
            model: PulsarTransformerDDPM模型
            num_timesteps: 总时间步数
            numerical_stability: 是否启用数值稳定性保护
            safe_division_threshold: 安全除法阈值
            range_clamp_strength: 范围限制强度
            adaptive_step_size: 是否使用自适应步长
            pulsar_optimization: 是否启用脉冲星特定优化
            channel_aware: 是否启用通道感知处理
        """
        self.model = model
        self.num_timesteps = num_timesteps
        self.device = device
        self.numerical_stability = numerical_stability
        self.safe_division_threshold = safe_division_threshold
        self.range_clamp_strength = range_clamp_strength
        self.adaptive_step_size = adaptive_step_size
        self.min_step_ratio = min_step_ratio
        self.max_step_ratio = max_step_ratio
        self.pulsar_optimization = pulsar_optimization
        self.channel_aware = channel_aware

        # 初始化噪声调度
        self._init_noise_schedule(beta_schedule, beta_start, beta_end)

        # 初始化脉冲星特定参数
        if self.pulsar_optimization:
            self._init_pulsar_parameters()

        # 确保设备一致性
        self._ensure_device_consistency()

        logger.info(f"PulsarAdaptiveSampler初始化完成: timesteps={num_timesteps}, device={device}")

    def _init_noise_schedule(self, schedule: str, beta_start: float, beta_end: float):
        """初始化数值稳定的噪声调度"""
        if schedule == "linear":
            betas = torch.linspace(beta_start, beta_end, self.num_timesteps)
        elif schedule == "cosine":
            # 改进的余弦调度，避免极端值
            timesteps = torch.arange(self.num_timesteps + 1, dtype=torch.float32) / self.num_timesteps
            alphas_cumprod = torch.cos((timesteps + 0.008) / 1.008 * torch.pi / 2) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - alphas_cumprod[1:] / alphas_cumprod[:-1]
            betas = torch.clamp(betas, 0.0001, 0.9999)
        else:
            raise ValueError(f"Unknown beta schedule: {schedule}")

        # 计算相关参数
        alphas = 1.0 - betas
        alphas_cumprod = torch.cumprod(alphas, dim=0)

        # 数值稳定性处理：确保所有值在安全范围内
        if self.numerical_stability:
            # 防止alpha_cumprod过小导致数值问题
            alphas_cumprod = torch.clamp(alphas_cumprod, min=1e-5, max=1.0-1e-5)
            # 重新计算betas以保持一致性
            alphas = torch.clamp(alphas, min=1e-5, max=1.0-1e-5)
            betas = torch.clamp(betas, min=1e-5, max=1.0-1e-5)

        # 转移到设备
        self.betas = betas.to(self.device)
        self.alphas = alphas.to(self.device)
        self.alphas_cumprod = alphas_cumprod.to(self.device)

        # 预计算常用项以提高效率
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1 - self.alphas_cumprod)

        logger.info(f"噪声调度初始化: alpha_cumprod范围[{self.alphas_cumprod.min():.6f}, {self.alphas_cumprod.max():.6f}]")

    def _init_pulsar_parameters(self):
        """初始化脉冲星特定参数"""
        # 通道特定的处理权重
        if self.channel_aware:
            # Period-DM, Phase-Subband, Phase-Subintegration的权重
            self.channel_weights = torch.tensor([1.0, 0.95, 0.90]).to(self.device)

        # 脉冲星信号保护参数
        self.signal_preservation_strength = 0.8
        self.noise_reduction_factor = 0.9

    def _ensure_device_consistency(self):
        """确保设备一致性"""
        # 获取模型设备
        if hasattr(self.model, 'device'):
            model_device = self.model.device
        else:
            model_device = next(self.model.parameters()).device

        # 如果设备不一致，调整采样器
        if model_device != self.device:
            logger.info(f"调整设备一致性: {self.device} -> {model_device}")
            self.device = model_device

            # 移动所有张量
            if hasattr(self, 'betas'):
                self.betas = self.betas.to(self.device)
                self.alphas = self.alphas.to(self.device)
                self.alphas_cumprod = self.alphas_cumprod.to(self.device)
                self.sqrt_alphas_cumprod = self.sqrt_alphas_cumprod.to(self.device)
                self.sqrt_one_minus_alphas_cumprod = self.sqrt_one_minus_alphas_cumprod.to(self.device)

            if hasattr(self, 'channel_weights'):
                self.channel_weights = self.channel_weights.to(self.device)

    def safe_division(self, numerator: torch.Tensor, denominator: torch.Tensor) -> torch.Tensor:
        """数值稳定的除法操作"""
        # 确保分母不会太小
        safe_denominator = torch.clamp(denominator, min=self.safe_division_threshold)
        return numerator / safe_denominator

    def safe_sqrt(self, x: torch.Tensor) -> torch.Tensor:
        """数值稳定的平方根操作"""
        # 确保输入非负且不会太小
        safe_x = torch.clamp(x, min=1e-8)
        return torch.sqrt(safe_x)

    def predict_x0_from_eps(
        self,
        x_t: torch.Tensor,
        eps: torch.Tensor,
        timestep: int
    ) -> torch.Tensor:
        """
        从噪声预测x_0（数值稳定版本）

        Args:
            x_t: 当前噪声样本
            eps: 预测的噪声
            timestep: 时间步
        Returns:
            预测的x_0
        """
        sqrt_alpha_cumprod = self.sqrt_alphas_cumprod[timestep]
        sqrt_one_minus_alpha_cumprod = self.sqrt_one_minus_alphas_cumprod[timestep]

        # 数值稳定的x_0预测
        # x_0 = (x_t - sqrt(1-alpha) * eps) / sqrt(alpha)
        numerator = x_t - sqrt_one_minus_alpha_cumprod * eps
        pred_x0 = self.safe_division(numerator, sqrt_alpha_cumprod)

        return pred_x0

    def adaptive_step_update(
        self,
        x_t: torch.Tensor,
        eps: torch.Tensor,
        timestep: int,
        prev_timestep: int
    ) -> torch.Tensor:
        """
        自适应步长更新（完全数值稳定）

        Args:
            x_t: 当前样本
            eps: 模型预测的噪声
            timestep: 当前时间步
            prev_timestep: 前一时间步
        Returns:
            更新后的样本
        """
        # 获取当前和目标时间步的参数
        alpha_t = self.alphas_cumprod[timestep]
        alpha_s = self.alphas_cumprod[prev_timestep]

        sqrt_alpha_t = self.safe_sqrt(alpha_t)
        sqrt_alpha_s = self.safe_sqrt(alpha_s)
        sqrt_one_minus_alpha_t = self.safe_sqrt(1 - alpha_t)
        sqrt_one_minus_alpha_s = self.safe_sqrt(1 - alpha_s)

        # 预测x_0
        pred_x0 = self.predict_x0_from_eps(x_t, eps, timestep)

        # 应用脉冲星特定的x_0后处理
        if self.pulsar_optimization:
            pred_x0 = self._apply_pulsar_x0_processing(pred_x0)

        # 数值稳定的DDIM更新
        # x_s = sqrt(alpha_s) * x_0 + sqrt(1-alpha_s) * eps
        x_s = sqrt_alpha_s * pred_x0 + sqrt_one_minus_alpha_s * eps

        return x_s

    def _apply_pulsar_x0_processing(self, pred_x0: torch.Tensor) -> torch.Tensor:
        """应用脉冲星特定的x_0后处理"""
        if not self.pulsar_optimization:
            return pred_x0

        # 通道特定处理
        if self.channel_aware and hasattr(self, 'channel_weights'):
            for c in range(min(3, pred_x0.shape[1])):
                pred_x0[:, c] *= self.channel_weights[c]

        # 信号保护：避免过度平滑
        # 保持重要的脉冲星特征
        pred_x0 = pred_x0 * self.signal_preservation_strength + \
                  pred_x0.detach() * (1 - self.signal_preservation_strength)

        return pred_x0

    def sample(
        self,
        batch_size: int,
        shape: Tuple[int, int, int] = (3, 32, 32),
        num_inference_steps: int = 20,
        generator: Optional[torch.Generator] = None,
        return_dict: bool = False,
        verbose: bool = True
    ) -> Union[torch.Tensor, dict]:
        """
        执行数值稳定的采样

        Args:
            batch_size: 批次大小
            shape: 样本形状 (C, H, W)
            num_inference_steps: 推理步数
            generator: 随机数生成器
            return_dict: 是否返回字典
            verbose: 是否显示进度
        Returns:
            生成的样本
        """
        # 设置推理时间步
        self.set_timesteps(num_inference_steps)

        # 初始化随机噪声
        sample = torch.randn(
            (batch_size,) + shape,
            generator=generator,
            device=self.device,
            dtype=torch.float32
        )

        # 确保初始样本有合理的范围
        sample = torch.clamp(sample, -3.0, 3.0)

        logger.info(f"开始采样: batch_size={batch_size}, steps={num_inference_steps}")
        logger.info(f"初始样本: mean={sample.mean():.4f}, std={sample.std():.4f}")

        # 采样循环
        timesteps_list = self.timesteps.tolist()
        if verbose:
            timesteps_iter = tqdm(timesteps_list, desc="PulsarAdaptive Sampling")
        else:
            timesteps_iter = timesteps_list

        for i, t in enumerate(timesteps_iter):
            # 确定前一时间步
            prev_timestep = timesteps_list[i + 1] if i < len(timesteps_list) - 1 else 0

            # 模型预测 - 确保时间步格式正确
            t_tensor = torch.full((batch_size,), t, device=self.device, dtype=torch.long)

            with torch.no_grad():
                model_output = self.model(sample, t_tensor)

            # 数值稳定性检查
            if torch.isnan(model_output).any() or torch.isinf(model_output).any():
                logger.warning(f"模型输出包含NaN/Inf在时间步{t}，应用修复")
                model_output = torch.where(
                    torch.isnan(model_output) | torch.isinf(model_output),
                    torch.zeros_like(model_output),
                    model_output
                )

            # 自适应步长更新
            sample = self.adaptive_step_update(sample, model_output, t, prev_timestep)

            # 应用数值稳定的范围控制
            sample = self._apply_stable_range_control(sample)

            # 监控采样质量
            if i % max(1, len(timesteps_list) // 4) == 0:
                current_std = sample.std()
                logger.debug(f"步骤 {i+1}/{len(timesteps_list)}: std={current_std:.6f}, "
                           f"range=[{sample.min():.4f}, {sample.max():.4f}]")

        # 最终后处理
        sample = self._final_postprocessing(sample)

        # 最终质量检查
        final_std = sample.std()
        logger.info(f"采样完成: mean={sample.mean():.4f}, std={final_std:.4f}, "
                   f"range=[{sample.min():.4f}, {sample.max():.4f}]")

        # 验证无NaN/Inf
        if torch.isnan(sample).any() or torch.isinf(sample).any():
            logger.error("最终样本包含NaN/Inf值！")
            raise RuntimeError("采样失败：产生了NaN/Inf值")

        if return_dict:
            return {"sample": sample}

        return sample

    def set_timesteps(self, num_inference_steps: int):
        """设置推理时间步"""
        # 使用线性分布的时间步，避免复杂的调度导致数值问题
        timesteps = torch.linspace(
            self.num_timesteps - 1, 0, num_inference_steps, dtype=torch.long
        )

        self.timesteps = timesteps.to(self.device)
        self.num_inference_steps = num_inference_steps

    def _apply_stable_range_control(self, sample: torch.Tensor) -> torch.Tensor:
        """应用数值稳定的范围控制"""
        # 检查异常值
        nan_mask = torch.isnan(sample)
        inf_mask = torch.isinf(sample)

        if nan_mask.any() or inf_mask.any():
            logger.warning(f"检测到异常值: NaN={nan_mask.sum().item()}, Inf={inf_mask.sum().item()}")
            # 使用中位数替换异常值
            valid_sample = sample[~(nan_mask | inf_mask)]
            if valid_sample.numel() > 0:
                median_val = valid_sample.median()
                sample = torch.where(nan_mask | inf_mask, median_val, sample)
            else:
                # 如果全部异常，重新初始化
                sample = torch.randn_like(sample) * 0.1

        # 软性范围控制
        max_val = sample.abs().max()
        if max_val > 5.0:
            # 使用tanh进行软性限制
            scale_factor = 3.0 / max_val
            sample = sample * scale_factor

        return sample

    def _final_postprocessing(self, sample: torch.Tensor) -> torch.Tensor:
        """最终后处理"""
        # 脉冲星特定的最终调整
        if self.pulsar_optimization:
            # 轻微的噪声减少
            sample = sample * self.noise_reduction_factor

        # 确保最终范围在[-1, 1]内
        # 使用tanh确保软性限制
        sample = torch.tanh(sample * self.range_clamp_strength)

        return sample