import torch
import torch.nn as nn
import math


class ECALayer(nn.Module):
    """
    Enhanced ECA (Efficient Channel Attention) module optimized for PulsarDDPM.

    Improvements over standard ECA:
    - Adaptive kernel sizing based on channel count
    - Pulsar-specific channel weighting for 3-channel input
    - Frequency-domain awareness for higher-level features
    - Memory-efficient implementation for A100 training

    Args:
        channel (int): Number of channels of the input feature map
        gamma (int): Parameter for adaptive kernel size calculation
        b (int): Parameter for adaptive kernel size calculation
        min_k (int): Minimum kernel size
        use_channel_weights (bool): Enable channel-specific weighting for pulsar data
        freq_aware (bool): Enable frequency-domain weighting for higher-level features
    """
    def __init__(self, channel, gamma=2, b=1, min_k=3, use_channel_weights=False, freq_aware=False):
        super(ECALayer, self).__init__()

        # Adaptive kernel size based on channel count (ECA-Net paper formula)
        t = int(abs((math.log(channel, 2) + b) / gamma))
        k_size = max(t if t % 2 else t + 1, min_k)

        self.channel = channel
        self.k_size = k_size

        # Core ECA components
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

        # OPTIMIZATION 1.2: Enhanced pulsar-specific channel weighting
        if use_channel_weights and channel == 3:
            # Initialize with pulsar-specific weights based on physical importance:
            # Period-DM (0.4), Phase-Subband (0.35), Phase-Subintegration (0.25)
            initial_weights = torch.tensor([0.4, 0.35, 0.25])
            self.channel_weights = nn.Parameter(initial_weights)
            print(f"ECA: Enabled pulsar-specific channel weighting: Period-DM={initial_weights[0]:.2f}, "
                  f"Phase-Subband={initial_weights[1]:.2f}, Phase-Subintegration={initial_weights[2]:.2f}")
        else:
            self.channel_weights = None

        # Frequency-domain awareness for higher-level features
        if freq_aware and channel >= 48:
            self.freq_weight = nn.Parameter(torch.ones(1))
            print(f"ECA: Enabled frequency weighting for {channel} channels")
        else:
            self.freq_weight = None

        print(f"ECA: Initialized with {channel} channels, k_size={k_size}")

    def forward(self, x):
        """
        Forward pass with pulsar-specific optimizations.

        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, channel, H, W)

        Returns:
            torch.Tensor: Output tensor with channel attention applied
        """
        # OPTIMIZATION 1.2: Apply pulsar-specific channel weighting
        if self.channel_weights is not None:
            # Apply normalized weights to maintain signal magnitude
            # Softmax ensures weights sum to 1, preserving overall signal strength
            normalized_weights = torch.softmax(self.channel_weights, dim=0) * 3  # Scale back to original magnitude
            x = x * normalized_weights.view(1, 3, 1, 1)

        # Global average pooling to get channel-wise statistics
        y = self.avg_pool(x)  # Shape: (B, C, 1, 1)

        # 1D convolution for channel interaction
        # Reshape for 1D conv: (B, C, 1, 1) -> (B, 1, C) -> conv -> (B, 1, C) -> (B, C, 1, 1)
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)

        # Apply sigmoid activation to get attention weights
        y = self.sigmoid(y)

        # Apply frequency weighting for higher-level features
        if self.freq_weight is not None:
            # Soft frequency enhancement: tanh provides bounded scaling
            freq_scale = 1 + 0.1 * torch.tanh(self.freq_weight)
            y = y * freq_scale

        # Apply channel attention to input
        return x * y.expand_as(x)

    def extra_repr(self):
        """String representation for debugging"""
        return f'channel={self.channel}, k_size={self.k_size}, ' \
               f'channel_weights={self.channel_weights is not None}, ' \
               f'freq_aware={self.freq_weight is not None}'


# Backward compatibility alias
eca_layer = ECALayer


def create_eca_for_layer(channel, layer_type='default'):
    """
    Factory function to create ECA layers with optimal configurations for different layer types.

    OPTIMIZATION 1.2: Pulsar-specific ECA configurations based on channel characteristics:
    - Period-DM surface: Larger kernels (7x7) for peak feature detection
    - Phase-Subband surface: Medium kernels (5x5) for stripe structure
    - Phase-Subintegration surface: Smaller kernels (3x3) for fine details

    Args:
        channel (int): Number of channels
        layer_type (str): Type of layer ('input', 'low', 'mid', 'high', 'period_dm', 'phase_subband', 'phase_subintegration', 'default')

    Returns:
        ECALayer: Configured ECA layer
    """
    configs = {
        'input': {  # For 3-channel input layer - enhanced for pulsar correlation
            'gamma': 2,
            'b': 1,
            'min_k': 5,  # Increased from 3 for better channel interaction
            'use_channel_weights': True,
            'freq_aware': False
        },
        'low': {  # For low-resolution blocks (48, 96 channels) - Period-DM optimized
            'gamma': 2,
            'b': 1,
            'min_k': 7,  # Larger kernel for peak detection
            'use_channel_weights': False,
            'freq_aware': False
        },
        'mid': {  # For mid-resolution blocks (192 channels) - Phase-Subband optimized
            'gamma': 2,
            'b': 1,
            'min_k': 5,  # Medium kernel for stripe structures
            'use_channel_weights': False,
            'freq_aware': True
        },
        'high': {  # For high-resolution blocks (384 channels) - Phase-Subintegration optimized
            'gamma': 2,
            'b': 1,
            'min_k': 3,  # Smaller kernel for fine details
            'use_channel_weights': False,
            'freq_aware': True
        },
        # Pulsar-specific configurations for different channel characteristics
        'period_dm': {  # Optimized for Period-DM surface (peak preservation)
            'gamma': 2,
            'b': 1,
            'min_k': 7,  # Large kernel for peak feature detection
            'use_channel_weights': False,
            'freq_aware': False  # Focus on spatial features, not frequency
        },
        'phase_subband': {  # Optimized for Phase-Subband surface (stripe structures)
            'gamma': 2,
            'b': 1,
            'min_k': 5,  # Medium kernel for vertical stripe detection
            'use_channel_weights': False,
            'freq_aware': True  # Frequency awareness for periodic patterns
        },
        'phase_subintegration': {  # Optimized for Phase-Subintegration surface (fine details)
            'gamma': 2,
            'b': 1,
            'min_k': 3,  # Small kernel for fine detail preservation
            'use_channel_weights': False,
            'freq_aware': True  # High-frequency detail enhancement
        },
        'default': {  # Default configuration
            'gamma': 2,
            'b': 1,
            'min_k': 3,
            'use_channel_weights': False,
            'freq_aware': False
        }
    }

    config = configs.get(layer_type, configs['default'])
    return ECALayer(channel, **config)