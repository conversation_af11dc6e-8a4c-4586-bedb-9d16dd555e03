import torch
import torch.nn as nn
from typing import Optional, Dict, Any


class EMA:
    """
    Exponential Moving Average for model parameters.
    
    Args:
        decay (float): Decay rate for EMA (typically close to 1.0, e.g., 0.9999)
        use_num_updates (bool): Whether to use number of updates to adjust decay rate
    """
    def __init__(self, decay: float = 0.9999, use_num_updates: bool = True):
        self.decay = decay
        self.use_num_updates = use_num_updates
        self.num_updates = 0
    
    def update_model_average(self, ema_model: nn.Module, current_model: nn.Module) -> None:
        """
        Update EMA model parameters using current model parameters.
        
        Args:
            ema_model (nn.Module): EMA model to update
            current_model (nn.Module): Current model with updated parameters
        """
        # Adjust decay rate based on number of updates if enabled
        decay = self.decay
        if self.use_num_updates:
            self.num_updates += 1
            decay = min(self.decay, (1 + self.num_updates) / (10 + self.num_updates))
        
        # Update each parameter in the EMA model
        for ema_param, current_param in zip(ema_model.parameters(), current_model.parameters()):
            if ema_param.requires_grad:
                ema_param.data.mul_(decay).add_(current_param.data, alpha=1 - decay)
    
    def state_dict(self) -> Dict[str, Any]:
        """
        Returns the state of the EMA as a dict.
        
        Returns:
            Dict[str, Any]: State dictionary containing decay and num_updates
        """
        return {
            'decay': self.decay,
            'num_updates': self.num_updates,
            'use_num_updates': self.use_num_updates
        }
    
    def load_state_dict(self, state_dict: Dict[str, Any]) -> None:
        """
        Loads the EMA state.
        
        Args:
            state_dict (Dict[str, Any]): EMA state dictionary
        """
        self.decay = state_dict['decay']
        self.num_updates = state_dict['num_updates']
        self.use_num_updates = state_dict.get('use_num_updates', True)
