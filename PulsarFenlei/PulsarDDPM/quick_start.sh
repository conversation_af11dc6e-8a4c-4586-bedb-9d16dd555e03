#!/bin/bash

# =============================================================================
# WGAN-GP+VAE脉冲星生成系统 - 快速启动脚本
# 
# 用法：
#   ./quick_start.sh                    # 使用默认参数
#   ./quick_start.sh --epochs 100       # 自定义训练轮数
#   ./quick_start.sh --batch_size 32    # 自定义批次大小
# =============================================================================

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 默认参数
EPOCHS=150
BATCH_SIZE=16
LEARNING_RATE=5e-5

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --epochs)
            EPOCHS="$2"
            shift 2
            ;;
        --batch_size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --lr)
            LEARNING_RATE="$2"
            shift 2
            ;;
        --help|-h)
            echo "WGAN-GP+VAE脉冲星生成系统快速启动"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --epochs EPOCHS        训练轮数 (默认: 150)"
            echo "  --batch_size SIZE      批次大小 (默认: 16)"
            echo "  --lr RATE             学习率 (默认: 5e-5)"
            echo "  --help, -h            显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                     # 使用默认参数"
            echo "  $0 --epochs 100       # 训练100轮"
            echo "  $0 --batch_size 32    # 使用32的批次大小"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}🚀 WGAN-GP+VAE脉冲星生成系统快速启动${NC}"
echo -e "${BLUE}===============================================${NC}"
echo -e "${GREEN}训练参数:${NC}"
echo -e "  训练轮数: ${EPOCHS}"
echo -e "  批次大小: ${BATCH_SIZE}"
echo -e "  学习率: ${LEARNING_RATE}"
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR" || {
    echo -e "${YELLOW}❌ 无法切换到脚本目录${NC}"
    exit 1
}

# 检查是否在正确的目录
if [[ ! -f "train_wgan_vae_complete.sh" ]]; then
    echo -e "${YELLOW}⚠️  找不到训练脚本 train_wgan_vae_complete.sh${NC}"
    echo -e "${YELLOW}当前目录: $(pwd)${NC}"
    exit 1
fi

# 确保必要的目录存在
mkdir -p logs checkpoints results

# 设置环境变量
export TOTAL_EPOCHS=$EPOCHS
export BATCH_SIZE=$BATCH_SIZE
export LEARNING_RATE=$LEARNING_RATE

echo -e "${GREEN}🔧 启动完整训练流程...${NC}"
echo ""

# 执行完整训练脚本
exec ./train_wgan_vae_complete.sh
