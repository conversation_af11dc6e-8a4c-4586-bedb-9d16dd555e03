#!/usr/bin/env python3
"""
WGAN-GP判别器模块单元测试
验证PulsarWGANDiscriminator和梯度惩罚机制的功能正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
import unittest
from models.discriminator import (
    PulsarWGANDiscriminator, compute_gradient_penalty, 
    compute_wgan_gp_loss, WGANGPTrainer
)

class TestPulsarWGANDiscriminator(unittest.TestCase):
    """PulsarWGANDiscriminator模块测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.batch_size = 4
        self.input_channels = 3
        self.image_size = 32
        
        # 创建测试数据
        self.test_real = torch.randn(
            self.batch_size, self.input_channels, self.image_size, self.image_size,
            device=self.device
        )
        self.test_fake = torch.randn(
            self.batch_size, self.input_channels, self.image_size, self.image_size,
            device=self.device
        )
        
        print(f"测试设备: {self.device}")
        print(f"测试输入形状: {self.test_real.shape}")
    
    def test_discriminator_architecture(self):
        """测试判别器架构"""
        print("\n🔍 测试判别器架构...")
        
        discriminator = PulsarWGANDiscriminator(self.input_channels).to(self.device)
        
        # 测试前向传播
        with torch.no_grad():
            score_real = discriminator(self.test_real)
            score_fake = discriminator(self.test_fake)
        
        # 验证输出形状
        expected_shape = (self.batch_size, 1)
        self.assertEqual(score_real.shape, expected_shape, 
                        f"真实样本评分形状错误: {score_real.shape} vs {expected_shape}")
        self.assertEqual(score_fake.shape, expected_shape,
                        f"生成样本评分形状错误: {score_fake.shape} vs {expected_shape}")
        
        # 验证参数量 (目标: ~900K)
        total_params = discriminator.total_params
        self.assertLess(total_params, 1200000, f"判别器参数量过大: {total_params}")
        self.assertGreater(total_params, 700000, f"判别器参数量过小: {total_params}")
        
        # 验证输出是标量 (无激活函数)
        self.assertTrue(torch.is_tensor(score_real), "输出应为张量")
        self.assertTrue(torch.is_tensor(score_fake), "输出应为张量")
        
        print(f"✅ 判别器参数量: {total_params/1e6:.2f}M")
        print(f"✅ 真实样本评分形状: {score_real.shape}")
        print(f"✅ 生成样本评分形状: {score_fake.shape}")
        print(f"✅ 真实样本评分范围: [{score_real.min().item():.3f}, {score_real.max().item():.3f}]")
        print(f"✅ 生成样本评分范围: [{score_fake.min().item():.3f}, {score_fake.max().item():.3f}]")
    
    def test_multiscale_features(self):
        """测试多尺度特征提取"""
        print("\n🔍 测试多尺度特征提取...")
        
        discriminator = PulsarWGANDiscriminator(self.input_channels).to(self.device)
        
        # 测试不同分辨率输入
        test_inputs = [
            torch.randn(2, 3, 32, 32, device=self.device),  # 标准输入
            torch.randn(1, 3, 32, 32, device=self.device),  # 单样本
            torch.randn(8, 3, 32, 32, device=self.device),  # 大批次
        ]
        
        for i, test_input in enumerate(test_inputs):
            with torch.no_grad():
                score = discriminator(test_input)
            
            expected_shape = (test_input.size(0), 1)
            self.assertEqual(score.shape, expected_shape,
                           f"输入{i+1}评分形状错误: {score.shape} vs {expected_shape}")
            
            print(f"✅ 输入{i+1} {test_input.shape} → 输出 {score.shape}")
    
    def test_gradient_penalty_computation(self):
        """测试梯度惩罚计算"""
        print("\n🔍 测试梯度惩罚计算...")
        
        discriminator = PulsarWGANDiscriminator(self.input_channels).to(self.device)
        
        # 测试梯度惩罚计算
        gp_loss = compute_gradient_penalty(
            discriminator, self.test_real, self.test_fake, self.device, lambda_gp=10.0
        )
        
        # 验证梯度惩罚
        self.assertTrue(torch.is_tensor(gp_loss), "梯度惩罚应为张量")
        self.assertEqual(gp_loss.dim(), 0, "梯度惩罚应为标量")
        self.assertGreater(gp_loss.item(), 0, "梯度惩罚应为正数")
        
        # 测试不同λ值
        lambda_values = [1.0, 5.0, 10.0, 20.0]
        gp_values = []
        
        for lambda_val in lambda_values:
            gp = compute_gradient_penalty(
                discriminator, self.test_real, self.test_fake, self.device, lambda_val
            )
            gp_values.append(gp.item())
        
        # 验证λ值影响 (更大的λ应该产生更大的惩罚)
        for i in range(1, len(gp_values)):
            self.assertGreater(gp_values[i], gp_values[i-1], 
                             f"λ={lambda_values[i]}的惩罚应大于λ={lambda_values[i-1]}")
        
        print(f"✅ 梯度惩罚计算正常: {gp_loss.item():.4f}")
        print(f"✅ λ值影响验证: {dict(zip(lambda_values, gp_values))}")
    
    def test_wgan_gp_loss_computation(self):
        """测试完整WGAN-GP损失计算"""
        print("\n🔍 测试WGAN-GP损失计算...")
        
        discriminator = PulsarWGANDiscriminator(self.input_channels).to(self.device)
        
        # 计算完整损失
        loss_dict = compute_wgan_gp_loss(
            discriminator, self.test_real, self.test_fake, self.device, lambda_gp=10.0
        )
        
        # 验证损失项
        required_keys = ['d_loss', 'g_loss', 'wasserstein_distance', 
                        'gradient_penalty', 'd_real_score', 'd_fake_score']
        
        for key in required_keys:
            self.assertIn(key, loss_dict, f"缺少损失项: {key}")
            self.assertTrue(torch.is_tensor(loss_dict[key]), f"{key}应为张量")
        
        # 验证损失关系
        d_loss = loss_dict['d_loss']
        wasserstein_distance = loss_dict['wasserstein_distance']
        gradient_penalty = loss_dict['gradient_penalty']
        
        # d_loss = wasserstein_distance + gradient_penalty
        expected_d_loss = wasserstein_distance + gradient_penalty
        self.assertAlmostEqual(d_loss.item(), expected_d_loss.item(), places=5,
                              msg="判别器损失计算错误")
        
        # 验证生成器损失
        g_loss = loss_dict['g_loss']
        d_fake_score = loss_dict['d_fake_score']
        expected_g_loss = -d_fake_score
        self.assertAlmostEqual(g_loss.item(), expected_g_loss.item(), places=5,
                              msg="生成器损失计算错误")
        
        print(f"✅ 判别器损失: {d_loss.item():.4f}")
        print(f"✅ 生成器损失: {g_loss.item():.4f}")
        print(f"✅ Wasserstein距离: {wasserstein_distance.item():.4f}")
        print(f"✅ 梯度惩罚: {gradient_penalty.item():.4f}")
        print(f"✅ 真实样本评分: {loss_dict['d_real_score'].item():.4f}")
        print(f"✅ 生成样本评分: {loss_dict['d_fake_score'].item():.4f}")
    
    def test_discriminator_gradient_flow(self):
        """测试判别器梯度流"""
        print("\n🔍 测试判别器梯度流...")
        
        discriminator = PulsarWGANDiscriminator(self.input_channels).to(self.device)
        discriminator.train()
        
        # 计算损失
        loss_dict = compute_wgan_gp_loss(
            discriminator, self.test_real, self.test_fake, self.device
        )
        d_loss = loss_dict['d_loss']
        
        # 反向传播
        d_loss.backward()
        
        # 检查梯度
        has_grad = 0
        total_grad_norm = 0
        
        for name, param in discriminator.named_parameters():
            if param.grad is not None:
                has_grad += 1
                grad_norm = param.grad.norm().item()
                total_grad_norm += grad_norm
        
        self.assertGreater(has_grad, 0, "没有参数有梯度")
        self.assertGreater(total_grad_norm, 0, "梯度范数为0")
        self.assertLess(total_grad_norm, 50000000, f"梯度范数过大: {total_grad_norm}")  # WGAN-GP梯度范数较大是正常的
        
        print(f"✅ 有梯度的参数数量: {has_grad}")
        print(f"✅ 总梯度范数: {total_grad_norm:.4f}")
    
    def test_wgan_gp_trainer(self):
        """测试WGAN-GP训练器"""
        print("\n🔍 测试WGAN-GP训练器...")
        
        # 创建简单的生成器 (用于测试)
        class SimpleGenerator(nn.Module):
            def __init__(self):
                super().__init__()
                self.fc = nn.Linear(64, 3*32*32)
                self.tanh = nn.Tanh()
            
            def forward(self, z):
                x = self.fc(z)
                x = x.view(-1, 3, 32, 32)
                return self.tanh(x)
        
        discriminator = PulsarWGANDiscriminator(self.input_channels).to(self.device)
        generator = SimpleGenerator().to(self.device)
        
        # 创建优化器
        d_optimizer = torch.optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        g_optimizer = torch.optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        
        # 创建训练器
        trainer = WGANGPTrainer(
            discriminator, generator, d_optimizer, g_optimizer, 
            self.device, lambda_gp=10.0, n_critic=5
        )
        
        # 测试判别器训练
        z = torch.randn(self.batch_size, 64, device=self.device)
        fake_samples = generator(z)
        
        d_metrics = trainer.train_discriminator(self.test_real, fake_samples)
        
        # 验证返回的指标
        required_metrics = ['d_loss', 'g_loss', 'wasserstein_distance', 'gradient_penalty']
        for metric in required_metrics:
            self.assertIn(metric, d_metrics, f"缺少判别器训练指标: {metric}")
            self.assertIsInstance(d_metrics[metric], (int, float), f"{metric}应为数值")
        
        # 测试生成器训练
        fake_samples = generator(z)
        g_metrics = trainer.train_generator(fake_samples)
        
        self.assertIn('g_loss', g_metrics, "缺少生成器损失")
        self.assertIsInstance(g_metrics['g_loss'], (int, float), "生成器损失应为数值")
        
        print(f"✅ 判别器训练指标: {d_metrics}")
        print(f"✅ 生成器训练指标: {g_metrics}")
    
    def test_instance_norm_usage(self):
        """测试InstanceNorm的使用"""
        print("\n🔍 测试InstanceNorm使用...")
        
        discriminator = PulsarWGANDiscriminator(self.input_channels).to(self.device)
        
        # 检查是否使用了InstanceNorm
        instance_norm_count = 0
        batch_norm_count = 0
        
        for module in discriminator.modules():
            if isinstance(module, nn.InstanceNorm2d):
                instance_norm_count += 1
            elif isinstance(module, nn.BatchNorm2d):
                batch_norm_count += 1
        
        self.assertGreater(instance_norm_count, 0, "应该使用InstanceNorm2d")
        print(f"✅ InstanceNorm2d层数: {instance_norm_count}")
        print(f"✅ BatchNorm2d层数: {batch_norm_count}")
        
        # 测试InstanceNorm在不同批次大小下的行为
        for batch_size in [1, 2, 4, 8]:
            test_input = torch.randn(batch_size, 3, 32, 32, device=self.device)
            with torch.no_grad():
                output = discriminator(test_input)
            self.assertEqual(output.shape, (batch_size, 1), 
                           f"批次大小{batch_size}输出形状错误")
        
        print("✅ InstanceNorm在不同批次大小下工作正常")

def run_discriminator_tests():
    """运行所有判别器测试"""
    print("🚀 开始WGAN-GP判别器模块测试")
    print("="*60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPulsarWGANDiscriminator)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 测试结果总结
    print("\n" + "="*60)
    print("📊 WGAN-GP判别器测试结果总结:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n🎉 所有WGAN-GP判别器测试通过！")
    else:
        print("\n⚠️ 存在测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    success = run_discriminator_tests()
    exit(0 if success else 1)
