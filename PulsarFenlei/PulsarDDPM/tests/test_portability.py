#!/usr/bin/env python3
"""
便携性测试脚本
验证修复后的路径配置和依赖检查
"""

import sys
import os
sys.path.insert(0, os.getcwd())

import torch
import numpy as np
import pickle
import matplotlib
import scipy

def test_environment():
    """测试环境依赖"""
    print("🔍 测试环境依赖")
    print("=" * 50)
    
    try:
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ NumPy: {np.__version__}")
        print(f"✅ Pickle: 内置模块")
        print(f"✅ Matplotlib: {matplotlib.__version__}")
        print(f"✅ SciPy: {scipy.__version__}")
        print(f"✅ HTRU1数据格式: Pickle (.pkl)")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA: {torch.version.cuda}")
            print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️ CUDA不可用，将使用CPU")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境检查失败: {e}")
        return False

def test_path_detection():
    """测试路径检测"""
    print("\n🔍 测试路径检测")
    print("=" * 50)
    
    try:
        from utils.data_loader import find_htru1_data_paths
        
        # 测试环境变量
        env_path = os.environ.get('HTRU1_DATA_PATH', '未设置')
        print(f"环境变量 HTRU1_DATA_PATH: {env_path}")
        
        # 查找数据路径
        data_paths = find_htru1_data_paths()
        print(f"找到数据文件: {len(data_paths)}个")
        
        if data_paths:
            print("✅ 找到真实HTRU1数据文件:")
            for i, path in enumerate(data_paths[:3]):
                print(f"  {i+1}. {path}")
                # 验证文件存在
                if os.path.exists(path):
                    print(f"     ✅ 文件存在")
                else:
                    print(f"     ❌ 文件不存在")
            
            if len(data_paths) > 3:
                print(f"  ... 还有 {len(data_paths)-3} 个文件")
        else:
            print("⚠️ 未找到真实数据，将使用模拟数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 路径检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n🔍 测试数据加载")
    print("=" * 50)
    
    try:
        from utils.data_loader import create_htru1_dataloader
        
        # 创建数据加载器
        dataloader = create_htru1_dataloader(
            batch_size=4, 
            augment=False, 
            num_workers=0
        )
        
        # 测试获取批次
        batch = next(iter(dataloader))
        
        print(f"✅ 数据加载成功:")
        print(f"  - 批次形状: {batch.shape}")
        print(f"  - 数据范围: [{batch.min().item():.3f}, {batch.max().item():.3f}]")
        print(f"  - 数据类型: {batch.dtype}")
        print(f"  - 数据集大小: {len(dataloader.dataset)}")
        print(f"  - 批次数量: {len(dataloader)}")
        
        # 验证数据格式
        if batch.shape[1:] == (3, 32, 32):
            print("✅ 数据形状正确 (3通道, 32x32)")
        else:
            print(f"❌ 数据形状错误: {batch.shape}")
            return False
        
        # 验证数据范围
        if -2.0 <= batch.min().item() <= 2.0 and -2.0 <= batch.max().item() <= 2.0:
            print("✅ 数据范围合理")
        else:
            print(f"⚠️ 数据范围可能异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_imports():
    """测试模型导入"""
    print("\n🔍 测试模型导入")
    print("=" * 50)
    
    try:
        from models.vae import PulsarVAE
        print("✅ VAE模型导入成功")
        
        from models.discriminator import PulsarWGANDiscriminator
        print("✅ 判别器模型导入成功")
        
        from models.wgan_vae import PulsarWGANVAE
        print("✅ WGAN-VAE模型导入成功")
        
        from losses.combined_loss import PulsarCombinedLoss
        print("✅ 损失函数导入成功")
        
        from training.full_trainer import TrainingConfig
        print("✅ 训练配置导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_working_directories():
    """测试不同工作目录"""
    print("\n🔍 测试工作目录兼容性")
    print("=" * 50)
    
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 测试可能的数据路径
    test_paths = [
        "/Pulsar/PulsarFenlei/data/htru1-batches-py",
        "/yanyb/jmy/Pulsar/PulsarFenlei/data/htru1-batches-py",
        "../data/htru1-batches-py",
        "./data/htru1-batches-py",
        "../../data/htru1-batches-py",
        "../../../data/htru1-batches-py",
        "data/htru1-batches-py"
    ]
    
    found_paths = []
    for path in test_paths:
        if os.path.exists(path):
            found_paths.append(path)
            print(f"✅ 找到路径: {path}")
    
    if not found_paths:
        print("⚠️ 未找到任何数据路径，将使用模拟数据")
    
    return True

def main():
    """主函数"""
    print("🚀 WGAN-GP+VAE便携性测试")
    print(f"测试时间: {os.popen('date').read().strip()}")
    print("=" * 60)
    
    tests = [
        ("环境依赖", test_environment),
        ("路径检测", test_path_detection),
        ("数据加载", test_data_loading),
        ("模型导入", test_model_imports),
        ("工作目录兼容性", test_working_directories)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: 通过")
                passed += 1
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"💥 {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有便携性测试通过！脚本可以在不同环境中运行")
        return 0
    else:
        print("⚠️ 部分测试失败，可能存在兼容性问题")
        return 1

if __name__ == "__main__":
    exit(main())
