#!/usr/bin/env python3
"""
WGAN-GP+VAE混合架构集成测试
验证混合架构、损失函数、训练器和数据加载器的功能正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
import unittest
from models.wgan_vae import PulsarWGANVAE, PulsarWGANVAETrainer, create_model_and_trainer
from losses.combined_loss import PulsarCombinedLoss, ProgressiveLossScheduler, create_loss_function
from losses.physics_loss import PulsarPhysicsLoss
from utils.data_loader import create_mock_dataloader, validate_dataloader

class TestPulsarWGANVAEIntegration(unittest.TestCase):
    """WGAN-GP+VAE混合架构集成测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.batch_size = 4
        self.latent_dim = 64
        self.input_channels = 3
        self.image_size = 32
        
        # 创建测试数据
        self.test_batch = torch.randn(
            self.batch_size, self.input_channels, self.image_size, self.image_size,
            device=self.device
        )
        
        print(f"测试设备: {self.device}")
        print(f"测试批次形状: {self.test_batch.shape}")
    
    def test_mixed_architecture_creation(self):
        """测试混合架构创建"""
        print("\n🔍 测试混合架构创建...")
        
        model = PulsarWGANVAE(self.latent_dim, self.input_channels).to(self.device)
        
        # 验证组件存在
        self.assertTrue(hasattr(model, 'vae'), "缺少VAE组件")
        self.assertTrue(hasattr(model, 'discriminator'), "缺少判别器组件")
        
        # 验证总参数量 (目标: 1.49M)
        total_params = model.total_params
        self.assertLess(total_params, 1800000, f"总参数量过大: {total_params}")
        self.assertGreater(total_params, 1200000, f"总参数量过小: {total_params}")
        
        # 验证参数/样本比 (995样本)
        param_per_sample = total_params / 995
        self.assertLess(param_per_sample, 2000, f"参数/样本比过大: {param_per_sample:.0f}")
        
        print(f"✅ 总参数量: {total_params/1e6:.2f}M")
        print(f"✅ 参数/样本比: {param_per_sample:.0f}:1")
    
    def test_mixed_architecture_forward(self):
        """测试混合架构前向传播"""
        print("\n🔍 测试混合架构前向传播...")
        
        model = PulsarWGANVAE(self.latent_dim, self.input_channels).to(self.device)
        
        # 测试完整前向传播
        with torch.no_grad():
            output_dict = model(self.test_batch)
        
        # 验证输出
        required_keys = ['x_recon', 'mu', 'logvar', 'd_real_score', 'd_fake_score']
        for key in required_keys:
            self.assertIn(key, output_dict, f"缺少输出: {key}")
        
        # 验证形状
        x_recon = output_dict['x_recon']
        mu = output_dict['mu']
        logvar = output_dict['logvar']
        d_real_score = output_dict['d_real_score']
        d_fake_score = output_dict['d_fake_score']
        
        self.assertEqual(x_recon.shape, self.test_batch.shape, "重建图像形状错误")
        self.assertEqual(mu.shape, (self.batch_size, self.latent_dim), "μ形状错误")
        self.assertEqual(logvar.shape, (self.batch_size, self.latent_dim), "logvar形状错误")
        self.assertEqual(d_real_score.shape, (self.batch_size, 1), "真实评分形状错误")
        self.assertEqual(d_fake_score.shape, (self.batch_size, 1), "生成评分形状错误")
        
        print(f"✅ 重建图像形状: {x_recon.shape}")
        print(f"✅ 潜在向量形状: μ={mu.shape}, logvar={logvar.shape}")
        print(f"✅ 判别器评分形状: real={d_real_score.shape}, fake={d_fake_score.shape}")
    
    def test_physics_loss_computation(self):
        """测试物理约束损失计算"""
        print("\n🔍 测试物理约束损失计算...")
        
        physics_loss = PulsarPhysicsLoss().to(self.device)
        
        # 创建测试数据 (原始和重建)
        original = self.test_batch
        reconstructed = self.test_batch + 0.1 * torch.randn_like(self.test_batch)
        
        # 计算物理损失
        loss_dict = physics_loss(original, reconstructed)
        
        # 验证损失项
        required_keys = ['total_physics_loss', 'periodicity_loss', 
                        'phase_consistency_loss', 'amplitude_loss']
        for key in required_keys:
            self.assertIn(key, loss_dict, f"缺少物理损失项: {key}")
            self.assertTrue(torch.is_tensor(loss_dict[key]), f"{key}应为张量")
            self.assertGreater(loss_dict[key].item(), 0, f"{key}应为正数")
        
        # 验证权重组合
        total_loss = loss_dict['total_physics_loss']
        component_losses = [
            0.4 * loss_dict['periodicity_loss'],
            0.35 * loss_dict['phase_consistency_loss'],
            0.25 * loss_dict['amplitude_loss']
        ]
        expected_total = sum(component_losses)
        
        self.assertAlmostEqual(total_loss.item(), expected_total.item(), places=5,
                              msg="物理损失权重组合错误")
        
        print(f"✅ 总物理损失: {total_loss.item():.4f}")
        print(f"✅ 周期性损失: {loss_dict['periodicity_loss'].item():.4f}")
        print(f"✅ 相位一致性损失: {loss_dict['phase_consistency_loss'].item():.4f}")
        print(f"✅ 幅度保持损失: {loss_dict['amplitude_loss'].item():.4f}")
    
    def test_combined_loss_function(self):
        """测试组合损失函数"""
        print("\n🔍 测试组合损失函数...")
        
        # 创建模型和损失函数
        model = PulsarWGANVAE(self.latent_dim, self.input_channels).to(self.device)
        loss_function, scheduler = create_loss_function(stage=3)
        
        # 前向传播
        output_dict = model(self.test_batch)
        x_recon = output_dict['x_recon']
        mu = output_dict['mu']
        logvar = output_dict['logvar']
        
        # 测试生成器损失
        g_loss_dict = loss_function.compute_generator_loss(
            self.test_batch, x_recon, mu, logvar, model.discriminator, self.device
        )
        
        # 验证生成器损失项
        required_g_keys = ['generator_total_loss', 'weighted_vae_loss', 
                          'weighted_g_adversarial_loss', 'weighted_physics_loss']
        for key in required_g_keys:
            self.assertIn(key, g_loss_dict, f"缺少生成器损失项: {key}")
            self.assertTrue(torch.is_tensor(g_loss_dict[key]), f"{key}应为张量")
        
        # 测试判别器损失
        fake_samples = x_recon.detach()
        d_loss_dict = loss_function.compute_discriminator_loss(
            model.discriminator, self.test_batch, fake_samples, self.device
        )
        
        # 验证判别器损失项
        required_d_keys = ['discriminator_total_loss', 'weighted_d_loss']
        for key in required_d_keys:
            self.assertIn(key, d_loss_dict, f"缺少判别器损失项: {key}")
            self.assertTrue(torch.is_tensor(d_loss_dict[key]), f"{key}应为张量")
        
        print(f"✅ 生成器总损失: {g_loss_dict['generator_total_loss'].item():.4f}")
        print(f"✅ 判别器总损失: {d_loss_dict['discriminator_total_loss'].item():.4f}")
    
    def test_progressive_loss_scheduler(self):
        """测试渐进式损失调度器"""
        print("\n🔍 测试渐进式损失调度器...")
        
        loss_function, scheduler = create_loss_function(stage=1)
        
        # 测试阶段切换
        for stage in [1, 2, 3]:
            scheduler.set_stage(stage)
            weights = scheduler.get_current_weights()
            
            # 验证权重和为1
            total_weight = sum(weights.values())
            self.assertAlmostEqual(total_weight, 1.0, places=6, 
                                 msg=f"阶段{stage}权重和不为1: {total_weight}")
            
            # 更新损失函数
            scheduler.update_loss_function(loss_function)
            
            # 验证损失函数权重更新
            self.assertAlmostEqual(loss_function.vae_weight, weights['vae'], places=6)
            self.assertAlmostEqual(loss_function.wgan_weight, weights['wgan'], places=6)
            self.assertAlmostEqual(loss_function.physics_weight, weights['physics'], places=6)
            
            print(f"✅ 阶段{stage}权重: {weights}")
    
    def test_trainer_creation_and_setup(self):
        """测试训练器创建和设置"""
        print("\n🔍 测试训练器创建和设置...")
        
        model, trainer = create_model_and_trainer(
            latent_dim=self.latent_dim, device=self.device
        )
        
        # 验证训练器组件
        self.assertIsInstance(trainer.model, PulsarWGANVAE, "模型类型错误")
        self.assertIsInstance(trainer.loss_function, PulsarCombinedLoss, "损失函数类型错误")
        self.assertIsInstance(trainer.loss_scheduler, ProgressiveLossScheduler, "调度器类型错误")
        
        # 验证优化器
        self.assertTrue(hasattr(trainer, 'vae_optimizer'), "缺少VAE优化器")
        self.assertTrue(hasattr(trainer, 'discriminator_optimizer'), "缺少判别器优化器")
        
        # 测试阶段设置
        for stage in [1, 2, 3]:
            trainer.set_stage(stage)
            self.assertEqual(trainer.current_stage, stage, f"阶段设置错误: {stage}")
        
        print(f"✅ 训练器创建成功")
        print(f"✅ 当前阶段: {trainer.current_stage}")
    
    def test_training_step_execution(self):
        """测试训练步骤执行"""
        print("\n🔍 测试训练步骤执行...")
        
        model, trainer = create_model_and_trainer(
            latent_dim=self.latent_dim, device=self.device
        )
        
        # 测试各阶段训练
        for stage in [1, 2, 3]:
            trainer.set_stage(stage)
            
            # 执行训练步骤
            metrics = trainer.train_step(self.test_batch)
            
            # 验证返回指标
            self.assertIsInstance(metrics, dict, "训练指标应为字典")
            self.assertGreater(len(metrics), 0, "训练指标不应为空")
            
            # 验证指标值
            for key, value in metrics.items():
                self.assertIsInstance(value, (int, float), f"{key}应为数值")
                self.assertFalse(np.isnan(value), f"{key}不应为NaN")
                self.assertFalse(np.isinf(value), f"{key}不应为Inf")
            
            print(f"✅ 阶段{stage}训练步骤执行成功，指标数量: {len(metrics)}")
    
    def test_data_loader_integration(self):
        """测试数据加载器集成"""
        print("\n🔍 测试数据加载器集成...")
        
        # 创建模拟数据加载器
        dataloader = create_mock_dataloader(batch_size=self.batch_size)
        
        # 验证数据加载器
        stats = validate_dataloader(dataloader)
        
        # 验证数据格式
        self.assertEqual(stats['batch_shape'], [self.batch_size, 3, 32, 32], "批次形状错误")
        self.assertGreaterEqual(stats['data_min'], -1.1, "数据最小值异常")
        self.assertLessEqual(stats['data_max'], 1.1, "数据最大值异常")
        
        # 测试与训练器集成
        model, trainer = create_model_and_trainer(device=self.device)
        
        # 获取一个批次并训练
        batch = next(iter(dataloader))
        batch = batch.to(self.device)
        
        trainer.set_stage(1)  # VAE预训练阶段
        metrics = trainer.train_step(batch)
        
        self.assertIsInstance(metrics, dict, "训练指标应为字典")
        self.assertGreater(len(metrics), 0, "应有训练指标")
        
        print(f"✅ 数据加载器集成成功")
        print(f"✅ 数据统计: {stats}")
    
    def test_memory_usage(self):
        """测试内存使用"""
        print("\n🔍 测试内存使用...")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过内存测试")
            return
        
        # 清空GPU缓存
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        
        # 创建模型和训练器
        model, trainer = create_model_and_trainer(device=self.device)
        
        # 执行训练步骤
        trainer.set_stage(3)  # 最复杂的联合优化阶段
        
        for _ in range(5):  # 多次训练步骤
            metrics = trainer.train_step(self.test_batch)
        
        # 检查内存使用
        peak_memory = torch.cuda.max_memory_allocated()
        memory_usage_gb = (peak_memory - initial_memory) / 1024**3
        
        # 验证内存使用 (目标: <12GB)
        self.assertLess(memory_usage_gb, 12.0, f"内存使用过大: {memory_usage_gb:.2f}GB")
        
        print(f"✅ 内存使用: {memory_usage_gb:.2f}GB")
        
        # 清理
        torch.cuda.empty_cache()

def run_integration_tests():
    """运行所有集成测试"""
    print("🚀 开始WGAN-GP+VAE混合架构集成测试")
    print("="*60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPulsarWGANVAEIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 测试结果总结
    print("\n" + "="*60)
    print("📊 WGAN-GP+VAE集成测试结果总结:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n🎉 所有WGAN-GP+VAE集成测试通过！")
    else:
        print("\n⚠️ 存在测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    success = run_integration_tests()
    exit(0 if success else 1)
