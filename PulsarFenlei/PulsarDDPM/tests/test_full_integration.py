#!/usr/bin/env python3
"""
完整集成测试
验证WGAN-GP+VAE的端到端训练流程和评估功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
import unittest
import tempfile
import shutil
from training.full_trainer import TrainingConfig, create_training_pipeline
from evaluation.fid_is_evaluator import FIDISEvaluator, evaluate_generation_quality
from evaluation.visualization import visualize_samples, plot_training_curves
from utils.random_seed import set_random_seed

class TestFullIntegration(unittest.TestCase):
    """完整集成测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.temp_dir = tempfile.mkdtemp()
        
        # 小规模测试配置
        self.config = TrainingConfig(
            latent_dim=32,  # 减小潜在维度
            batch_size=4,   # 小批次
            device=str(self.device),
            random_seed=42,
            vae_lr=2e-4,
            discriminator_lr=4e-4,
            stage1_epochs=2,  # 极小轮数用于测试
            stage2_epochs=2,
            stage3_epochs=2,
            beta_vae=1.0,
            lambda_gp=10.0,
            n_critic=2,  # 减少判别器训练频率
            data_augment=True,
            augment_factor=1,  # 不增强数据
            num_workers=0,  # 避免多进程问题
            save_dir=self.temp_dir,
            save_interval=1,
            log_interval=1,
            max_memory_gb=12.0
        )
        
        print(f"测试设备: {self.device}")
        print(f"临时目录: {self.temp_dir}")
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_training_pipeline_creation(self):
        """测试训练管道创建"""
        print("\n🔍 测试训练管道创建...")
        
        # 创建训练管道
        pipeline = create_training_pipeline(self.config)
        
        # 验证管道组件
        self.assertIsNotNone(pipeline, "训练管道创建失败")
        self.assertEqual(pipeline.device, self.device, "设备配置错误")
        self.assertEqual(pipeline.config.latent_dim, 32, "潜在维度配置错误")
        
        print(f"✅ 训练管道创建成功")
    
    def test_data_setup(self):
        """测试数据设置"""
        print("\n🔍 测试数据设置...")
        
        pipeline = create_training_pipeline(self.config)
        
        # 设置数据
        dataloader = pipeline.setup_data()
        
        # 验证数据加载器
        self.assertIsNotNone(dataloader, "数据加载器创建失败")
        self.assertGreater(len(dataloader), 0, "数据加载器为空")
        
        # 验证批次数据
        batch = next(iter(dataloader))
        self.assertEqual(batch.shape[0], self.config.batch_size, "批次大小错误")
        self.assertEqual(batch.shape[1:], (3, 32, 32), "数据形状错误")
        self.assertTrue(torch.all(batch >= -1.1) and torch.all(batch <= 1.1), "数据范围错误")
        
        print(f"✅ 数据设置成功，批次形状: {batch.shape}")
    
    def test_model_setup(self):
        """测试模型设置"""
        print("\n🔍 测试模型设置...")
        
        pipeline = create_training_pipeline(self.config)
        
        # 设置模型
        model, trainer = pipeline.setup_model()
        
        # 验证模型
        self.assertIsNotNone(model, "模型创建失败")
        self.assertIsNotNone(trainer, "训练器创建失败")
        
        # 验证参数量
        total_params = model.total_params
        self.assertLess(total_params, 2000000, f"参数量过大: {total_params}")
        self.assertGreater(total_params, 500000, f"参数量过小: {total_params}")
        
        # 验证前向传播
        test_input = torch.randn(2, 3, 32, 32, device=self.device)
        with torch.no_grad():
            output = model(test_input)
        
        self.assertIn('x_recon', output, "缺少重建输出")
        self.assertIn('mu', output, "缺少均值输出")
        self.assertIn('logvar', output, "缺少对数方差输出")
        
        print(f"✅ 模型设置成功，参数量: {total_params/1e6:.2f}M")
    
    def test_single_stage_training(self):
        """测试单阶段训练"""
        print("\n🔍 测试单阶段训练...")
        
        pipeline = create_training_pipeline(self.config)
        pipeline.setup_data()
        pipeline.setup_model()
        
        # 测试阶段1训练
        stage_history = pipeline.train_stage(1, 1)  # 只训练1轮
        
        # 验证训练历史
        self.assertEqual(len(stage_history), 1, "训练历史长度错误")
        self.assertIn('epoch', stage_history[0], "缺少epoch信息")
        self.assertIn('stage', stage_history[0], "缺少stage信息")
        
        # 验证损失值
        metrics = stage_history[0]
        for key, value in metrics.items():
            if key not in ['epoch', 'stage', 'epoch_time']:
                self.assertFalse(np.isnan(value), f"{key}为NaN")
                self.assertFalse(np.isinf(value), f"{key}为Inf")
        
        print(f"✅ 单阶段训练成功，指标数量: {len(metrics)}")
    
    def test_three_stage_training(self):
        """测试三阶段训练"""
        print("\n🔍 测试三阶段训练...")
        
        # 使用更小的配置
        small_config = TrainingConfig(
            latent_dim=16,
            batch_size=2,
            device=str(self.device),
            random_seed=42,
            stage1_epochs=1,
            stage2_epochs=1,
            stage3_epochs=1,
            save_dir=self.temp_dir,
            num_workers=0
        )
        
        pipeline = create_training_pipeline(small_config)
        
        # 执行完整训练
        training_history = pipeline.run_full_training()
        
        # 验证训练历史
        self.assertIn('stage1', training_history, "缺少阶段1历史")
        self.assertIn('stage2', training_history, "缺少阶段2历史")
        self.assertIn('stage3', training_history, "缺少阶段3历史")
        
        # 验证各阶段都有数据
        for stage in ['stage1', 'stage2', 'stage3']:
            self.assertGreater(len(training_history[stage]), 0, f"{stage}历史为空")
        
        print(f"✅ 三阶段训练成功")
    
    def test_fid_is_evaluator(self):
        """测试FID/IS评估器"""
        print("\n🔍 测试FID/IS评估器...")
        
        # 创建评估器
        evaluator = FIDISEvaluator(self.device)
        
        # 创建测试数据
        real_images = torch.randn(8, 3, 32, 32, device=self.device)
        fake_images = torch.randn(8, 3, 32, 32, device=self.device)
        
        # 测试特征提取
        real_features = evaluator.extract_features(real_images, 'pool3')
        fake_features = evaluator.extract_features(fake_images, 'pool3')
        
        self.assertEqual(real_features.shape, (8, 2048), "FID特征形状错误")
        self.assertEqual(fake_features.shape, (8, 2048), "FID特征形状错误")
        
        # 测试FID计算
        fid_score = evaluator.compute_fid(real_images, fake_images)
        self.assertIsInstance(fid_score, float, "FID分数类型错误")
        self.assertGreater(fid_score, 0, "FID分数应为正数")
        
        # 测试IS计算
        is_mean, is_std = evaluator.compute_is(fake_images)
        self.assertIsInstance(is_mean, float, "IS均值类型错误")
        self.assertIsInstance(is_std, float, "IS标准差类型错误")
        self.assertGreater(is_mean, 0, "IS均值应为正数")
        
        print(f"✅ FID/IS评估器测试成功，FID: {fid_score:.2f}, IS: {is_mean:.2f}±{is_std:.2f}")
    
    def test_generation_quality_evaluation(self):
        """测试生成质量评估"""
        print("\n🔍 测试生成质量评估...")
        
        # 创建简单模型用于测试
        pipeline = create_training_pipeline(self.config)
        pipeline.setup_data()
        pipeline.setup_model()
        
        # 评估生成质量
        metrics = evaluate_generation_quality(
            pipeline.model,
            pipeline.dataloader,
            self.device,
            num_samples=16  # 小样本数用于测试
        )
        
        # 验证评估指标
        self.assertIn('fid', metrics, "缺少FID指标")
        self.assertIn('is_mean', metrics, "缺少IS均值")
        self.assertIn('is_std', metrics, "缺少IS标准差")
        self.assertIn('num_samples', metrics, "缺少样本数")
        
        # 验证指标值
        self.assertIsInstance(metrics['fid'], float, "FID类型错误")
        self.assertIsInstance(metrics['is_mean'], float, "IS均值类型错误")
        self.assertGreater(metrics['fid'], 0, "FID应为正数")
        self.assertGreater(metrics['is_mean'], 0, "IS均值应为正数")
        
        print(f"✅ 生成质量评估成功: FID={metrics['fid']:.2f}, IS={metrics['is_mean']:.2f}")
    
    def test_visualization_functions(self):
        """测试可视化功能"""
        print("\n🔍 测试可视化功能...")
        
        # 创建测试样本
        samples = torch.randn(4, 3, 32, 32)
        
        # 测试样本可视化 (不保存文件)
        try:
            visualize_samples(samples, title="Test Samples")
            print("✅ 样本可视化功能正常")
        except Exception as e:
            self.fail(f"样本可视化失败: {e}")
        
        # 测试训练曲线绘制
        mock_history = {
            'stage1': [
                {'epoch': 0, 'weighted_vae_loss': 1.0, 'epoch_time': 10.0}
            ],
            'stage2': [
                {'epoch': 0, 'weighted_vae_loss': 0.8, 'weighted_g_adversarial_loss': 0.5, 'epoch_time': 12.0}
            ],
            'stage3': [
                {'epoch': 0, 'weighted_vae_loss': 0.6, 'weighted_g_adversarial_loss': 0.3, 'weighted_physics_loss': 0.2, 'epoch_time': 15.0}
            ]
        }
        
        try:
            plot_training_curves(mock_history)
            print("✅ 训练曲线绘制功能正常")
        except Exception as e:
            self.fail(f"训练曲线绘制失败: {e}")
    
    def test_memory_usage(self):
        """测试内存使用"""
        print("\n🔍 测试内存使用...")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过内存测试")
            return
        
        # 清空GPU缓存
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        
        # 创建和训练模型
        pipeline = create_training_pipeline(self.config)
        pipeline.setup_data()
        pipeline.setup_model()
        
        # 执行一些训练步骤
        for _ in range(3):
            batch = next(iter(pipeline.dataloader))
            metrics = pipeline.trainer.train_step(batch)
        
        # 检查内存使用
        peak_memory = torch.cuda.max_memory_allocated()
        memory_usage_gb = (peak_memory - initial_memory) / 1024**3
        
        # 验证内存使用在合理范围内 (严格限制)
        self.assertLess(memory_usage_gb, 1.0, f"内存使用过大: {memory_usage_gb:.2f}GB (应<1.0GB)")
        
        print(f"✅ 内存使用测试通过: {memory_usage_gb:.2f}GB")
        
        # 清理
        torch.cuda.empty_cache()

def run_full_integration_tests():
    """运行完整集成测试"""
    print("🚀 开始完整集成测试")
    print("="*60)
    
    # 设置随机种子
    set_random_seed(42)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestFullIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 测试结果总结
    print("\n" + "="*60)
    print("📊 完整集成测试结果总结:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n🎉 所有完整集成测试通过！")
    else:
        print("\n⚠️ 存在测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    success = run_full_integration_tests()
    exit(0 if success else 1)
