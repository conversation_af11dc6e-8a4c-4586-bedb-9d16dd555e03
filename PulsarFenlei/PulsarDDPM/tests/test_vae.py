#!/usr/bin/env python3
"""
VAE模块单元测试
验证PulsarVAE编码器、解码器和完整模型的功能正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
import unittest
from models.vae import PulsarVAEEncoder, PulsarVAEDecoder, PulsarVAE, compute_vae_loss

class TestPulsarVAE(unittest.TestCase):
    """PulsarVAE模块测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.batch_size = 4
        self.latent_dim = 64
        self.input_channels = 3
        self.image_size = 32
        
        # 创建测试数据
        self.test_input = torch.randn(
            self.batch_size, self.input_channels, self.image_size, self.image_size,
            device=self.device
        )
        
        print(f"测试设备: {self.device}")
        print(f"测试输入形状: {self.test_input.shape}")
    
    def test_encoder_architecture(self):
        """测试编码器架构"""
        print("\n🔍 测试编码器架构...")
        
        encoder = PulsarVAEEncoder(self.latent_dim, self.input_channels).to(self.device)
        
        # 测试前向传播
        with torch.no_grad():
            mu, logvar = encoder(self.test_input)
        
        # 验证输出形状
        expected_shape = (self.batch_size, self.latent_dim)
        self.assertEqual(mu.shape, expected_shape, f"μ形状错误: {mu.shape} vs {expected_shape}")
        self.assertEqual(logvar.shape, expected_shape, f"logvar形状错误: {logvar.shape} vs {expected_shape}")
        
        # 验证参数量 (轻量化目标: ~240K)
        total_params = sum(p.numel() for p in encoder.parameters())
        self.assertLess(total_params, 300000, f"编码器参数量过大: {total_params}")
        self.assertGreater(total_params, 200000, f"编码器参数量过小: {total_params}")
        
        print(f"✅ 编码器参数量: {total_params/1e6:.2f}M")
        print(f"✅ μ形状: {mu.shape}")
        print(f"✅ logvar形状: {logvar.shape}")
    
    def test_decoder_architecture(self):
        """测试解码器架构"""
        print("\n🔍 测试解码器架构...")
        
        decoder = PulsarVAEDecoder(self.latent_dim, self.input_channels).to(self.device)
        
        # 创建测试潜在向量
        test_z = torch.randn(self.batch_size, self.latent_dim, device=self.device)
        
        # 测试前向传播
        with torch.no_grad():
            x_recon = decoder(test_z)
        
        # 验证输出形状
        expected_shape = (self.batch_size, self.input_channels, self.image_size, self.image_size)
        self.assertEqual(x_recon.shape, expected_shape, f"重建图像形状错误: {x_recon.shape} vs {expected_shape}")
        
        # 验证输出范围 (应该在[-1, 1]之间)
        self.assertGreaterEqual(x_recon.min().item(), -1.1, "输出值过小")
        self.assertLessEqual(x_recon.max().item(), 1.1, "输出值过大")
        
        # 验证参数量 (轻量化目标: ~320K)
        total_params = sum(p.numel() for p in decoder.parameters())
        self.assertLess(total_params, 400000, f"解码器参数量过大: {total_params}")
        self.assertGreater(total_params, 250000, f"解码器参数量过小: {total_params}")
        
        print(f"✅ 解码器参数量: {total_params/1e6:.2f}M")
        print(f"✅ 重建图像形状: {x_recon.shape}")
        print(f"✅ 输出范围: [{x_recon.min().item():.3f}, {x_recon.max().item():.3f}]")
    
    def test_complete_vae_model(self):
        """测试完整VAE模型"""
        print("\n🔍 测试完整VAE模型...")
        
        vae = PulsarVAE(self.latent_dim, self.input_channels).to(self.device)
        
        # 测试前向传播
        with torch.no_grad():
            x_recon, mu, logvar = vae(self.test_input)
        
        # 验证输出形状
        self.assertEqual(x_recon.shape, self.test_input.shape, "重建图像形状不匹配")
        self.assertEqual(mu.shape, (self.batch_size, self.latent_dim), "μ形状错误")
        self.assertEqual(logvar.shape, (self.batch_size, self.latent_dim), "logvar形状错误")
        
        # 验证总参数量 (严格目标: 560K±10%)
        total_params = vae.total_params
        target_params = 560000
        tolerance = 0.1  # 10%
        min_params = int(target_params * (1 - tolerance))  # 504K
        max_params = int(target_params * (1 + tolerance))  # 616K

        self.assertLess(total_params, max_params,
                       f"总参数量过大: {total_params} > {max_params} (560K+10%)")
        self.assertGreater(total_params, min_params,
                          f"总参数量过小: {total_params} < {min_params} (560K-10%)")

        # 验证参数/样本比 (995样本，轻量化目标563:1)
        param_per_sample = total_params / 995
        self.assertLess(param_per_sample, 1000, f"参数/样本比过大: {param_per_sample:.0f}")
        
        print(f"✅ VAE总参数量: {total_params/1e6:.2f}M")
        print(f"✅ 参数/样本比: {param_per_sample:.0f}:1")
    
    def test_reparameterization_trick(self):
        """测试重参数化技巧"""
        print("\n🔍 测试重参数化技巧...")
        
        vae = PulsarVAE(self.latent_dim, self.input_channels).to(self.device)
        
        # 创建测试μ和logvar
        mu = torch.randn(self.batch_size, self.latent_dim, device=self.device)
        logvar = torch.randn(self.batch_size, self.latent_dim, device=self.device)
        
        # 测试重参数化
        z1 = vae.reparameterize(mu, logvar)
        z2 = vae.reparameterize(mu, logvar)
        
        # 验证形状
        self.assertEqual(z1.shape, (self.batch_size, self.latent_dim), "z形状错误")
        
        # 验证随机性 (两次采样应该不同)
        self.assertFalse(torch.allclose(z1, z2), "重参数化缺乏随机性")
        
        # 验证梯度流 (z应该对μ和logvar有梯度)
        mu.requires_grad_(True)
        logvar.requires_grad_(True)
        z = vae.reparameterize(mu, logvar)
        loss = z.sum()
        loss.backward()
        
        self.assertIsNotNone(mu.grad, "μ没有梯度")
        self.assertIsNotNone(logvar.grad, "logvar没有梯度")
        
        print("✅ 重参数化技巧正常工作")
    
    def test_vae_loss_computation(self):
        """测试VAE损失函数计算"""
        print("\n🔍 测试VAE损失函数...")
        
        vae = PulsarVAE(self.latent_dim, self.input_channels).to(self.device)
        
        # 前向传播
        x_recon, mu, logvar = vae(self.test_input)
        
        # 计算损失
        loss_dict = compute_vae_loss(x_recon, self.test_input, mu, logvar, beta=1.0)
        
        # 验证损失项
        self.assertIn('total_loss', loss_dict, "缺少总损失")
        self.assertIn('recon_loss', loss_dict, "缺少重建损失")
        self.assertIn('kl_loss', loss_dict, "缺少KL损失")
        
        # 验证损失值合理性
        total_loss = loss_dict['total_loss']
        recon_loss = loss_dict['recon_loss']
        kl_loss = loss_dict['kl_loss']
        
        self.assertGreater(total_loss.item(), 0, "总损失应为正数")
        self.assertGreater(recon_loss.item(), 0, "重建损失应为正数")
        self.assertGreater(kl_loss.item(), 0, "KL损失应为正数")
        
        # 验证损失组合
        expected_total = recon_loss + kl_loss
        self.assertAlmostEqual(total_loss.item(), expected_total.item(), places=5, 
                              msg="总损失计算错误")
        
        print(f"✅ 总损失: {total_loss.item():.4f}")
        print(f"✅ 重建损失: {recon_loss.item():.4f}")
        print(f"✅ KL损失: {kl_loss.item():.4f}")
    
    def test_sampling_capability(self):
        """测试采样生成能力"""
        print("\n🔍 测试采样生成能力...")
        
        vae = PulsarVAE(self.latent_dim, self.input_channels).to(self.device)
        
        # 测试采样
        num_samples = 8
        samples = vae.sample(num_samples, self.device)
        
        # 验证采样形状
        expected_shape = (num_samples, self.input_channels, self.image_size, self.image_size)
        self.assertEqual(samples.shape, expected_shape, f"采样形状错误: {samples.shape}")
        
        # 验证采样范围
        self.assertGreaterEqual(samples.min().item(), -1.1, "采样值过小")
        self.assertLessEqual(samples.max().item(), 1.1, "采样值过大")
        
        print(f"✅ 采样形状: {samples.shape}")
        print(f"✅ 采样范围: [{samples.min().item():.3f}, {samples.max().item():.3f}]")
    
    def test_gradient_flow(self):
        """测试梯度流"""
        print("\n🔍 测试梯度流...")
        
        vae = PulsarVAE(self.latent_dim, self.input_channels).to(self.device)
        vae.train()
        
        # 前向传播
        x_recon, mu, logvar = vae(self.test_input)
        
        # 计算损失
        loss_dict = compute_vae_loss(x_recon, self.test_input, mu, logvar)
        total_loss = loss_dict['total_loss']
        
        # 反向传播
        total_loss.backward()
        
        # 检查梯度
        has_grad = 0
        total_grad_norm = 0
        
        for name, param in vae.named_parameters():
            if param.grad is not None:
                has_grad += 1
                grad_norm = param.grad.norm().item()
                total_grad_norm += grad_norm
        
        self.assertGreater(has_grad, 0, "没有参数有梯度")
        self.assertGreater(total_grad_norm, 0, "梯度范数为0")
        self.assertLess(total_grad_norm, 1000, f"梯度范数过大: {total_grad_norm} (应<1000)")
        
        print(f"✅ 有梯度的参数数量: {has_grad}")
        print(f"✅ 总梯度范数: {total_grad_norm:.4f}")

def run_vae_tests():
    """运行所有VAE测试"""
    print("🚀 开始VAE模块测试")
    print("="*60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPulsarVAE)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 测试结果总结
    print("\n" + "="*60)
    print("📊 VAE测试结果总结:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n🎉 所有VAE测试通过！")
    else:
        print("\n⚠️ 存在测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    success = run_vae_tests()
    exit(0 if success else 1)
