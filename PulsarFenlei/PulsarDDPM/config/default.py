"""
Default configuration for PulsarDDPM.
"""

# Data configuration
DATA_CONFIG = {
    # Path to HTRU1 dataset
    "data_dir": "/Pulsar/PulsarFenlei/data/htru1-batches-py",

    # Batch size for training and evaluation
    "batch_size": 32,

    # Number of workers for dataloader
    "num_workers": 4,

    # Whether to apply data augmentation
    "augment": True,

    # Whether to shuffle the dataset
    "shuffle": True,

    # Whether to drop the last incomplete batch
    "drop_last": True,
}

# Model configuration
MODEL_CONFIG = {
    # Image size
    "img_size": 32,

    # Number of image channels
    "img_channels": 3,

    # Number of base channels in UNet
    "base_channels": 64,  # Increased from 48 to 64 for better expressivity with small samples

    # Channel multipliers for UNet
    "channel_mults": (1, 2, 4, 8),  # Wider channel multipliers for better hierarchical feature extraction

    # Number of residual blocks per resolution
    "num_res_blocks": 2,  # Increased from 1 to 2 for better feature extraction

    # Dropout rate
    "dropout": 0.3,  # Maintained higher dropout for regularization

    # Time embedding dimension
    "time_emb_dim": 128,  # Increased from 64 to 128 for better temporal representation

    # Number of diffusion timesteps
    "num_timesteps": 1000,

    # Loss type (l1 or l2)
    "loss_type": "l2",

    # EMA decay rate
    "ema_decay": 0.9999,

    # Number of steps before starting EMA
    "ema_start": 5000,

    # Number of steps between EMA updates
    "ema_update_rate": 1,

    # Small sample mode optimizations
    "small_sample_mode": False,

    # Number of groups for group normalization
    "num_groups": 8,  # Must be a divisor of base_channels (64 % 8 = 0)

    # Whether to reduce attention channels for efficiency
    "reduce_attention_channels": True,

    # Factor to reduce attention channels by
    "attention_reduction_factor": 0.5,

    # Number of middle blocks
    "num_middle_blocks": 2,  # Increased from 1 to 2 for better bottleneck representation

    # Whether to use CBAM attention
    "use_cbam": True,

    # Reduction ratio for CBAM channel attention
    "cbam_reduction_ratio": 16,

    # Whether to use channel-specific processing
    "channel_specific": True,

    # Dimension of channel embeddings
    "channel_emb_dim": 64,

    # Whether to use channel-specific convolution kernels
    "use_channel_conv_kernels": True,

    # Resolutions at which to apply attention
    "attention_resolutions": (1, 2),  # Apply attention at more resolutions for better feature integration
}

# Training configuration
TRAIN_CONFIG = {
    # Learning rate
    "lr": 5e-5,  # Maintained at 5e-5 for stability

    # Weight decay
    "weight_decay": 1e-5,  # Increased from 0.0 to 1e-5 for better regularization

    # Type of learning rate scheduler
    "scheduler_type": "warmup_cosine",  # Using cosine annealing for smooth LR decay

    # Number of warmup epochs
    "warmup_epochs": 5,  # Maintained at 5 epochs for stable initial training

    # Maximum number of epochs
    "max_epochs": 200,

    # Gradient clipping value
    "gradient_clip_val": 0.5,  # Maintained at 0.5 to prevent gradient explosion

    # Whether to use progressive training
    "progressive_training": True,

    # Resolutions for progressive training
    "progressive_resolutions": [16, 32],

    # Epochs for each resolution in progressive training
    "progressive_epochs": [100, 100],

    # Device to use
    "device": "cuda",

    # Whether to use debug mode
    "debug": False,
}

# Output configuration
OUTPUT_CONFIG = {
    # Output directory
    "output_dir": "outputs",

    # Log directory
    "log_dir": "logs",

    # Checkpoint directory
    "checkpoint_dir": "checkpoints",

    # Save checkpoint every n epochs
    "save_every": 10,

    # Evaluate every n epochs
    "eval_every": 10,
}

# Evaluation configuration
EVAL_CONFIG = {
    # Path to Inception-v3 model
    "inception_model_path": "/Pulsar/PulsarFenlei/PulsarDDPM/evaluation/inception_v3_google-0cc3c7bd.pth",

    # Number of samples to generate for evaluation (changed from 199 to match training set positive samples)
    "num_samples": 995,

    # Batch size for evaluation
    "batch_size": 32,
}

# 已移除小样本配置 (SMALL_SAMPLE_MODEL_CONFIG 和 SMALL_SAMPLE_TRAIN_CONFIG)

# Default configuration
DEFAULT_CONFIG = {
    "data": DATA_CONFIG,
    "model": MODEL_CONFIG,
    "train": TRAIN_CONFIG,
    "output": OUTPUT_CONFIG,
    "eval": EVAL_CONFIG,
}


def get_config():
    """
    Get configuration.

    Returns:
        dict: Configuration
    """
    return DEFAULT_CONFIG


def update_config(config, updates):
    """
    Update configuration with new values.

    Args:
        config (dict): Configuration to update
        updates (dict): New values

    Returns:
        dict: Updated configuration
    """
    for k, v in updates.items():
        if isinstance(v, dict) and k in config and isinstance(config[k], dict):
            config[k] = update_config(config[k], v)
        else:
            config[k] = v
    return config


def load_config(config_path):
    """
    Load configuration from file.

    Args:
        config_path (str): Path to configuration file

    Returns:
        dict: Loaded configuration
    """
    import json
    with open(config_path, "r") as f:
        config = json.load(f)
    return config


def save_config(config, config_path):
    """
    Save configuration to file.

    Args:
        config (dict): Configuration to save
        config_path (str): Path to save configuration
    """
    import json

    # Convert tuples to lists for JSON serialization
    def convert_tuples(obj):
        if isinstance(obj, tuple):
            return list(obj)
        elif isinstance(obj, dict):
            return {k: convert_tuples(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_tuples(item) for item in obj]
        else:
            return obj

    # Convert config for JSON serialization
    json_config = convert_tuples(config)

    with open(config_path, "w") as f:
        json.dump(json_config, f, indent=4)


def print_config(config):
    """
    Print configuration.

    Args:
        config (dict): Configuration to print
    """
    import json
    print(json.dumps(config, indent=4))
