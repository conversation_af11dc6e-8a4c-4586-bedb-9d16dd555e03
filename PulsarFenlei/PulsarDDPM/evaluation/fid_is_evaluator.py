#!/usr/bin/env python3
"""
FID和IS评估器
基于Inception-v3模型计算生成质量指标

基于WGAN-GP+VAE实施计划的评估规格：
- FID目标: <40 (基于DCGAN论文基准)
- IS目标: >5 (基于DCGAN论文基准)
- 使用预下载的Inception-v3模型
- 995个HTRU1正样本作为真实基线
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from torchvision.models import inception_v3
import numpy as np
import os
from typing import Tuple, Optional, Dict
from scipy import linalg
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FIDISEvaluator:
    """
    FID和IS评估器
    
    实现基于Inception-v3的生成质量评估：
    - FID: 使用2048维pool3特征计算分布距离
    - IS: 使用1000维分类层特征计算质量和多样性
    """
    
    def __init__(self, device: torch.device, inception_path: Optional[str] = None):
        self.device = device
        
        # 加载Inception-v3模型
        self.inception_model = self._load_inception_model(inception_path)
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((299, 299)),  # Inception-v3输入尺寸
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        logger.info(f"FIDISEvaluator初始化完成:")
        logger.info(f"  设备: {device}")
        logger.info(f"  Inception-v3模型已加载")
    
    def _load_inception_model(self, inception_path: Optional[str] = None) -> nn.Module:
        """加载Inception-v3模型"""
        # 默认路径
        if inception_path is None:
            inception_path = './evaluation/inception_v3_google-0cc3c7bd.pth'
        
        # 创建模型
        model = inception_v3(pretrained=False, transform_input=False)
        
        # 加载预训练权重
        if os.path.exists(inception_path):
            logger.info(f"加载预训练Inception-v3: {inception_path}")
            state_dict = torch.load(inception_path, map_location='cpu')
            model.load_state_dict(state_dict)
        else:
            logger.warning(f"Inception-v3文件不存在: {inception_path}，使用随机权重")
        
        # 设置为评估模式
        model.eval()
        model.to(self.device)
        
        return model
    
    def _preprocess_images(self, images: torch.Tensor) -> torch.Tensor:
        """预处理图像用于Inception-v3"""
        # 输入: [batch_size, 3, 32, 32], 范围[-1, 1]
        # 输出: [batch_size, 3, 299, 299], 范围[0, 1]并标准化
        
        # 转换到[0, 1]范围
        images = (images + 1.0) / 2.0
        
        # 确保3通道
        if images.size(1) != 3:
            images = images.repeat(1, 3, 1, 1)
        
        # 调整尺寸并标准化
        images = self.transform(images)
        
        return images
    
    def extract_features(self, images: torch.Tensor, feature_type: str = 'pool3') -> torch.Tensor:
        """
        提取Inception-v3特征
        
        Args:
            images: 输入图像 [batch_size, 3, 32, 32]
            feature_type: 特征类型 ('pool3' for FID, 'logits' for IS)
            
        Returns:
            features: 提取的特征
        """
        # 预处理图像
        processed_images = self._preprocess_images(images)
        
        with torch.no_grad():
            if feature_type == 'pool3':
                # FID使用pool3特征 (2048维)
                # 简化的特征提取 - 使用完整前向传播
                x = processed_images
                # 基础卷积层
                x = self.inception_model.Conv2d_1a_3x3(x)
                x = self.inception_model.Conv2d_2a_3x3(x)
                x = self.inception_model.Conv2d_2b_3x3(x)
                x = self.inception_model.maxpool1(x)
                x = self.inception_model.Conv2d_3b_1x1(x)
                x = self.inception_model.Conv2d_4a_3x3(x)
                x = self.inception_model.maxpool2(x)

                # Inception模块
                x = self.inception_model.Mixed_5b(x)
                x = self.inception_model.Mixed_5c(x)
                x = self.inception_model.Mixed_5d(x)
                x = self.inception_model.Mixed_6a(x)
                x = self.inception_model.Mixed_6b(x)
                x = self.inception_model.Mixed_6c(x)
                x = self.inception_model.Mixed_6d(x)
                x = self.inception_model.Mixed_6e(x)
                x = self.inception_model.Mixed_7a(x)
                x = self.inception_model.Mixed_7b(x)
                x = self.inception_model.Mixed_7c(x)

                # 全局平均池化
                features = self.inception_model.avgpool(x)
                features = features.view(features.size(0), -1)  # [batch_size, 2048]
                
            elif feature_type == 'logits':
                # IS使用分类logits (1000维)
                features = self.inception_model(processed_images)
                
            else:
                raise ValueError(f"不支持的特征类型: {feature_type}")
        
        return features
    
    def compute_fid(self, real_images: torch.Tensor, fake_images: torch.Tensor) -> float:
        """
        计算FID (Fréchet Inception Distance)
        
        Args:
            real_images: 真实图像 [N, 3, 32, 32]
            fake_images: 生成图像 [N, 3, 32, 32]
            
        Returns:
            fid_score: FID分数 (越低越好)
        """
        logger.info("计算FID分数...")
        
        # 提取特征
        real_features = self.extract_features(real_images, 'pool3')
        fake_features = self.extract_features(fake_images, 'pool3')
        
        # 转换为numpy
        real_features = real_features.cpu().numpy()
        fake_features = fake_features.cpu().numpy()
        
        # 计算均值和协方差
        mu_real = np.mean(real_features, axis=0)
        mu_fake = np.mean(fake_features, axis=0)
        
        sigma_real = np.cov(real_features, rowvar=False)
        sigma_fake = np.cov(fake_features, rowvar=False)
        
        # 计算FID (添加数值稳定性)
        diff = mu_real - mu_fake

        # 添加正则化项防止奇异矩阵
        eps = 1e-6
        sigma_real += eps * np.eye(sigma_real.shape[0])
        sigma_fake += eps * np.eye(sigma_fake.shape[0])

        try:
            covmean, _ = linalg.sqrtm(sigma_real.dot(sigma_fake), disp=False)

            if np.iscomplexobj(covmean):
                covmean = covmean.real

            fid_score = diff.dot(diff) + np.trace(sigma_real + sigma_fake - 2 * covmean)

            # 检查结果有效性
            if np.isnan(fid_score) or np.isinf(fid_score) or fid_score < 0:
                # 回退到简化计算
                fid_score = np.linalg.norm(diff) ** 2 + np.trace(sigma_real) + np.trace(sigma_fake)

        except Exception as e:
            logger.warning(f"FID计算出错，使用简化方法: {e}")
            # 简化的FID计算
            fid_score = np.linalg.norm(diff) ** 2 + np.trace(sigma_real) + np.trace(sigma_fake)
        
        logger.info(f"FID分数: {fid_score:.4f}")
        return float(fid_score)
    
    def compute_is(self, fake_images: torch.Tensor, splits: int = 10) -> Tuple[float, float]:
        """
        计算IS (Inception Score)
        
        Args:
            fake_images: 生成图像 [N, 3, 32, 32]
            splits: 分割数量用于计算标准差
            
        Returns:
            is_mean: IS均值 (越高越好)
            is_std: IS标准差
        """
        logger.info("计算IS分数...")
        
        # 提取logits
        logits = self.extract_features(fake_images, 'logits')
        
        # 转换为概率
        probs = F.softmax(logits, dim=1).cpu().numpy()
        
        # 分割计算
        N = probs.shape[0]
        split_size = N // splits
        
        scores = []
        for i in range(splits):
            start_idx = i * split_size
            end_idx = (i + 1) * split_size if i < splits - 1 else N
            
            split_probs = probs[start_idx:end_idx]
            
            # 计算KL散度
            py = np.mean(split_probs, axis=0)
            kl_divs = []
            
            for j in range(split_probs.shape[0]):
                px = split_probs[j]
                kl_div = np.sum(px * np.log(px / py + 1e-8))
                kl_divs.append(kl_div)
            
            score = np.exp(np.mean(kl_divs))
            scores.append(score)
        
        is_mean = np.mean(scores)
        is_std = np.std(scores)
        
        logger.info(f"IS分数: {is_mean:.4f} ± {is_std:.4f}")
        return float(is_mean), float(is_std)

def load_inception_model(device: torch.device, inception_path: Optional[str] = None) -> nn.Module:
    """加载Inception-v3模型的便捷函数"""
    evaluator = FIDISEvaluator(device, inception_path)
    return evaluator.inception_model

def compute_fid(real_images: torch.Tensor, fake_images: torch.Tensor, 
                device: torch.device, inception_path: Optional[str] = None) -> float:
    """计算FID的便捷函数"""
    evaluator = FIDISEvaluator(device, inception_path)
    return evaluator.compute_fid(real_images, fake_images)

def compute_is(fake_images: torch.Tensor, device: torch.device,
               inception_path: Optional[str] = None, splits: int = 10) -> Tuple[float, float]:
    """计算IS的便捷函数"""
    evaluator = FIDISEvaluator(device, inception_path)
    return evaluator.compute_is(fake_images, splits)

def evaluate_generation_quality(model, real_dataloader: torch.utils.data.DataLoader,
                               device: torch.device, num_samples: int = 1000,
                               inception_path: Optional[str] = None) -> Dict[str, float]:
    """
    评估生成质量的完整函数
    
    Args:
        model: 生成模型
        real_dataloader: 真实数据加载器
        device: 设备
        num_samples: 评估样本数量
        inception_path: Inception-v3模型路径
        
    Returns:
        metrics: 包含FID和IS的评估指标
    """
    logger.info(f"开始生成质量评估 (样本数: {num_samples})...")
    
    evaluator = FIDISEvaluator(device, inception_path)
    
    # 收集真实样本
    real_samples = []
    for batch in real_dataloader:
        real_samples.append(batch)
        if len(real_samples) * batch.size(0) >= num_samples:
            break
    
    real_images = torch.cat(real_samples, dim=0)[:num_samples].to(device)
    
    # 生成假样本
    model.eval()
    with torch.no_grad():
        fake_images = model.sample(num_samples, device)
    
    # 计算指标
    fid_score = evaluator.compute_fid(real_images, fake_images)
    is_mean, is_std = evaluator.compute_is(fake_images)
    
    metrics = {
        'fid': fid_score,
        'is_mean': is_mean,
        'is_std': is_std,
        'num_samples': num_samples
    }
    
    logger.info(f"生成质量评估完成:")
    logger.info(f"  FID: {fid_score:.4f} (目标: <40)")
    logger.info(f"  IS: {is_mean:.4f} ± {is_std:.4f} (目标: >5)")
    
    return metrics
