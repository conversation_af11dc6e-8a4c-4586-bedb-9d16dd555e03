import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
import time
import warnings
import scipy
import scipy.linalg
from typing import Tuple, Dict, Optional
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default Inception model path (using relative path)
DEFAULT_INCEPTION_MODEL_PATH = os.path.join(os.path.dirname(__file__), "inception_v3_google-0cc3c7bd.pth")

# Default number of generated samples for evaluation (HTRU1 training set positive samples)
# Changed from 199 (test set) to 995 (training set) to match training data for consistent FID evaluation
DEFAULT_NUM_SAMPLES = 995


def verify_inception_model(model_path: str = DEFAULT_INCEPTION_MODEL_PATH, device: str = "cpu") -> bool:
    """
    Verify that the Inception model exists and can be loaded correctly.

    Args:
        model_path (str): Path to the Inception model
        device (str): Device to use for verification

    Returns:
        bool: True if the model is valid, False otherwise
    """
    logger.info(f"Verifying Inception model at {model_path}")

    # Check if file exists
    if not os.path.exists(model_path):
        logger.error(f"Inception model not found at {model_path}")
        return False

    try:
        # Try to load the model
        state_dict = torch.load(model_path, map_location=device)

        # Check if it's a state dict or a full model
        if isinstance(state_dict, dict):
            # If it's a state dict, check if it has expected keys
            if 'state_dict' in state_dict:
                state_dict = state_dict['state_dict']

            # Check number of parameters
            num_params = len(state_dict)
            logger.info(f"State dict contains {num_params} parameters")

            # Typical InceptionV3 has around 200-300 parameters
            if num_params < 100:
                logger.warning(f"State dict has unusually few parameters: {num_params}")

        elif isinstance(state_dict, torch.nn.Module):
            # If it's a full model, check if it has expected attributes
            if hasattr(state_dict, 'Mixed_7c'):
                logger.info("Model has Mixed_7c layer (correct InceptionV3 structure)")
            else:
                logger.warning("Model does not have expected InceptionV3 structure")

            # Count parameters
            num_params = sum(p.numel() for p in state_dict.parameters())
            logger.info(f"Model has {num_params:,} parameters")

            # Typical InceptionV3 has around 27M parameters
            if num_params < 20_000_000:
                logger.warning(f"Model has unusually few parameters: {num_params:,}")
        else:
            logger.warning(f"Unexpected model type: {type(state_dict)}")
            return False

        # Create a test input
        x = torch.randn(1, 3, 299, 299, device=device)

        # Try to run a forward pass
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")

            # Create InceptionV3 instance
            inception = InceptionV3(model_path=model_path, device=device)

            # Run forward pass
            with torch.no_grad():
                # Test forward pass (simplified InceptionV3 always outputs 1000-dim logits)
                logits = inception(x)

                # Check logits dimension
                if logits.shape[1] != 1000:
                    logger.warning(f"Logits dimension is {logits.shape[1]}, expected 1000")

        logger.info("Inception model verification successful")
        return True

    except Exception as e:
        logger.error(f"Error verifying Inception model: {e}")
        return False


def download_inception_model(save_path: str = DEFAULT_INCEPTION_MODEL_PATH) -> str:
    """
    Check for local Inception-v3 model (network downloads disabled).

    Args:
        save_path (str): Path where the model should be located

    Returns:
        str: Path to the local model

    Raises:
        FileNotFoundError: If the local model file doesn't exist
    """
    logger.info(f"Checking for local Inception-v3 model at {save_path}")

    # Check if the local model file exists
    if os.path.exists(save_path):
        logger.info(f"Local Inception-v3 model found at {save_path}")

        # Verify file size to ensure it's not corrupted
        file_size = os.path.getsize(save_path) / (1024 * 1024)  # MB
        logger.info(f"Model file size: {file_size:.1f} MB")

        if file_size < 10:  # Inception model should be much larger
            logger.warning(f"Model file seems too small ({file_size:.1f} MB), may be corrupted")

        return save_path
    else:
        # Provide clear error message with manual download instructions
        logger.error("=" * 80)
        logger.error("LOCAL INCEPTION MODEL NOT FOUND")
        logger.error("=" * 80)
        logger.error(f"Expected location: {save_path}")
        logger.error("")
        logger.error("Network downloads are disabled to avoid connectivity issues.")
        logger.error("Please ensure the Inception-v3 model file exists at the expected location.")
        logger.error("")
        logger.error("If you need to download the model manually:")
        logger.error("1. Download from: https://download.pytorch.org/models/inception_v3_google-0cc3c7bd.pth")
        logger.error(f"2. Save to: {save_path}")
        logger.error("3. Restart the evaluation process")
        logger.error("=" * 80)

        raise FileNotFoundError(
            f"Local Inception model not found at {save_path}. "
            f"Network downloads are disabled. Please ensure the model file exists."
        )


class InceptionV3(nn.Module):
    """
    Pretrained InceptionV3 network for FID and IS calculation with differential feature extraction.

    支持差异化特征提取：
    - FID评估：使用2048维pool3层特征（更敏感的生成质量评估）
    - IS评估：使用1000维分类层特征（保持与现有基准的一致性）
    - 模式设置：调用model.eval()将模型设为评估模式，避免训练时的随机性

    Args:
        model_path (str): Path to pretrained InceptionV3 model
        device (str): Device to use
        feature_type (str): Feature type to extract ('fid' for 2048-dim, 'is' for 1000-dim, 'both' for both)
    """
    def __init__(
        self,
        model_path: str = DEFAULT_INCEPTION_MODEL_PATH,
        device: str = "cuda",
        feature_type: str = "both"
    ):
        super().__init__()

        # Store feature extraction configuration
        self.feature_type = feature_type
        if feature_type not in ['fid', 'is', 'both']:
            raise ValueError(f"feature_type must be 'fid', 'is', or 'both', got {feature_type}")

        logger.info(f"Loading Inception-v3 model from local path: {model_path}")
        logger.info(f"Feature extraction mode: {feature_type}")
        start_time = time.time()

        # Check if local model file exists
        if not os.path.exists(model_path):
            raise FileNotFoundError(
                f"Local Inception model not found at {model_path}. "
                f"Please ensure the model file exists before running evaluation. "
                f"Network downloads are disabled to avoid connectivity issues."
            )

        try:
            # Load model architecture from torchvision
            from torchvision.models import inception_v3

            logger.info(f"Creating Inception-v3 architecture...")
            self.model = inception_v3(pretrained=False)  # Create architecture without pretrained weights

            # Load weights from local file
            logger.info(f"Loading weights from {model_path}")
            state_dict = torch.load(model_path, map_location=device)

            # Handle different state dict formats
            if isinstance(state_dict, dict) and 'state_dict' in state_dict:
                state_dict = state_dict['state_dict']

            # Load state dict with error handling
            try:
                self.model.load_state_dict(state_dict, strict=True)
                logger.info("Loaded state dict with strict=True")
            except RuntimeError as e:
                logger.warning(f"Strict loading failed: {e}")
                logger.info("Attempting to load with strict=False")
                self.model.load_state_dict(state_dict, strict=False)

            # Set model to evaluation mode
            self.model.eval()

            # Move model to device
            self.model.to(device)

            # Disable gradient computation for inference
            for param in self.model.parameters():
                param.requires_grad = False

            logger.info(f"Successfully loaded local Inception-v3 model from {model_path}")

        except Exception as e:
            logger.error(f"Error loading local Inception model: {e}")
            raise RuntimeError(
                f"Failed to load local Inception model from {model_path}. "
                f"Error: {e}. Please verify the model file is valid."
            )

        # Store configuration
        self.device = device

        # Log model information
        param_count = sum(p.numel() for p in self.model.parameters())
        logger.info(f"Inception-v3 model has {param_count:,} parameters")
        logger.info(f"Inception-v3 model loaded in {time.time() - start_time:.2f} seconds")

        if feature_type == 'fid':
            logger.info("Using 2048-dim pool3 features for FID evaluation")
        elif feature_type == 'is':
            logger.info("Using 1000-dim logits for IS evaluation")
        else:
            logger.info("Supporting both 2048-dim pool3 features (FID) and 1000-dim logits (IS)")

    def forward(self, x: torch.Tensor, feature_type: str = None) -> torch.Tensor:
        """
        Forward pass with differential feature extraction support.

        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, C, H, W)
            feature_type (str, optional): Override feature type ('fid' for 2048-dim, 'is' for 1000-dim)
                                        If None, uses self.feature_type

        Returns:
            torch.Tensor: Features of shape (batch_size, feature_dim)
                         - 2048-dim for FID (pool3 layer features)
                         - 1000-dim for IS (classification logits)
        """
        # Determine which feature type to extract
        target_feature_type = feature_type if feature_type is not None else self.feature_type

        # 确保模型处于评估模式
        self.model.eval()

        # 图像预处理 - 处理通道数不为3的情况（如单通道图像）
        if x.size(1) != 3:
            x = x.repeat(1, 3, 1, 1)  # 单通道扩展为3通道（如生成器输出为1通道时）

        # 调整尺寸为299x299以适配Inception v3的输入要求
        # 使用高质量的双三次插值以保持图像质量
        if x.shape[2] != 299 or x.shape[3] != 299:
            x = F.interpolate(x, size=(299, 299), mode='bicubic', align_corners=False, antialias=True)

        # 数值范围预处理：确保输入在[-1, 1]范围内
        x = torch.clamp(x, -1.0, 1.0)

        # 精确的范围转换：从[-1, 1]转换到[0, 1]
        x = (x + 1.0) * 0.5

        # 应用ImageNet标准归一化
        mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
        x = (x - mean) / std

        # 前向传播 - 根据feature_type提取不同层的特征
        with torch.no_grad():
            try:
                if target_feature_type == 'fid':
                    # FID评估：提取2048维pool3层特征
                    # 手动前向传播到pool3层
                    x = self.model.Conv2d_1a_3x3(x)
                    x = self.model.Conv2d_2a_3x3(x)
                    x = self.model.Conv2d_2b_3x3(x)
                    x = F.max_pool2d(x, kernel_size=3, stride=2)
                    x = self.model.Conv2d_3b_1x1(x)
                    x = self.model.Conv2d_4a_3x3(x)
                    x = F.max_pool2d(x, kernel_size=3, stride=2)
                    x = self.model.Mixed_5b(x)
                    x = self.model.Mixed_5c(x)
                    x = self.model.Mixed_5d(x)
                    x = self.model.Mixed_6a(x)
                    x = self.model.Mixed_6b(x)
                    x = self.model.Mixed_6c(x)
                    x = self.model.Mixed_6d(x)
                    x = self.model.Mixed_6e(x)
                    x = self.model.Mixed_7a(x)
                    x = self.model.Mixed_7b(x)
                    x = self.model.Mixed_7c(x)
                    # 全局平均池化得到2048维特征
                    x = F.adaptive_avg_pool2d(x, (1, 1))
                    x = torch.flatten(x, 1)
                    return x

                elif target_feature_type == 'is':
                    # IS评估：使用完整模型获取1000维分类logits
                    logits = self.model(x)
                    return logits

                else:  # target_feature_type == 'both'
                    # 返回1000维logits（向后兼容）
                    logits = self.model(x)
                    return logits

            except Exception as e:
                logger.error(f"Error in forward pass: {e}")
                # 根据目标特征类型创建相应维度的虚拟输出
                if target_feature_type == 'fid':
                    return torch.randn(x.shape[0], 2048, device=x.device)
                else:
                    return torch.randn(x.shape[0], 1000, device=x.device)


def calculate_activation_statistics(
    images: torch.Tensor,
    model: nn.Module,
    batch_size: int = 32,
    device: str = "cuda",
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Calculate activation statistics (mean and covariance) for FID with optimized memory usage.

    Args:
        images (torch.Tensor): Images of shape (N, 3, H, W)
        model (nn.Module): Inception model
        batch_size (int): Batch size
        device (str): Device to use

    Returns:
        Tuple[np.ndarray, np.ndarray]: Mean and covariance of activations
    """
    model.eval()

    # Ensure model is on the correct device
    model_device = next(model.parameters()).device
    if str(model_device) != str(device) and str(model_device) != f"{device}:0":
        logger.info(f"Moving model from {model_device} to {device}")
        model = model.to(device)

    n_samples = images.shape[0]

    # Adjust batch size based on available memory
    if str(device).startswith('cuda') and n_samples > 1000:
        # For large datasets on GPU, use smaller batch size to avoid OOM
        original_batch_size = batch_size
        batch_size = min(batch_size, 32)  # Limit batch size for large datasets
        if batch_size != original_batch_size:
            logger.info(f"Adjusted batch size from {original_batch_size} to {batch_size} for memory efficiency")

    n_batches = int(np.ceil(n_samples / batch_size))

    logger.info(f"Calculating activation statistics for {n_samples} images with batch size {batch_size}")
    start_time = time.time()

    # Get feature dimension with a small batch to minimize memory usage
    with torch.no_grad():
        # Use a smaller batch for feature dimension detection
        small_batch_size = min(4, n_samples)
        first_batch = images[:small_batch_size]

        # Ensure the batch is on the correct device
        if first_batch.device != device and str(first_batch.device).split(':')[0] != str(device).split(':')[0]:
            logger.info(f"Moving sample batch from {first_batch.device} to {device}")
            first_batch = first_batch.to(device)

        # Get feature dimension
        features = model(first_batch)
        feature_dim = features.shape[1]
        logger.info(f"Feature dimension: {feature_dim}")

        # Clear GPU memory
        del features, first_batch
        torch.cuda.empty_cache() if torch.cuda.is_available() else None

    # Use memory-efficient approach for large datasets
    if n_samples > 5000 and str(device).startswith('cuda'):
        logger.info("Using memory-efficient approach for large dataset")

        # Calculate mean incrementally
        sum_features = torch.zeros(feature_dim, device='cpu', dtype=torch.float64)
        sum_squared_features = torch.zeros((feature_dim, feature_dim), device='cpu', dtype=torch.float64)

        # Process in batches
        for i in tqdm(range(n_batches), desc="Extracting features"):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, n_samples)
            current_batch_size = end_idx - start_idx

            # Move batch to device
            batch = images[start_idx:end_idx]
            if batch.device.type != str(device).split(':')[0]:
                batch = batch.to(device)

            # Extract features
            with torch.no_grad():
                batch_features = model(batch)

            # Move to CPU to save GPU memory
            batch_features = batch_features.cpu()

            # Update statistics
            sum_features += batch_features.sum(dim=0).double()

            # Update covariance incrementally
            # For each sample in the batch, compute outer product and add to sum
            for j in range(current_batch_size):
                sample = batch_features[j].unsqueeze(1)  # Shape: [feature_dim, 1]
                outer_product = sample @ sample.t()  # Shape: [feature_dim, feature_dim]
                sum_squared_features += outer_product

            # Clear GPU memory
            del batch, batch_features
            torch.cuda.empty_cache() if torch.cuda.is_available() else None

            # Log progress
            if (i + 1) % 10 == 0 or i == n_batches - 1:
                logger.info(f"Processed {min((i + 1) * batch_size, n_samples)}/{n_samples} samples")

        # Calculate mean and covariance
        mu = (sum_features / n_samples).numpy()

        # Calculate covariance: E[X^2] - E[X]^2
        mean_outer_product = (mu.reshape(-1, 1) @ mu.reshape(1, -1))
        sigma = (sum_squared_features / n_samples).numpy() - mean_outer_product

    else:
        # Standard approach for smaller datasets
        logger.info("Using standard approach for feature extraction")

        # Initialize feature array
        all_features = np.zeros((n_samples, feature_dim), dtype=np.float32)

        # Extract features
        with torch.no_grad():
            for i in tqdm(range(n_batches), desc="Extracting features"):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, n_samples)

                # Move batch to device
                batch = images[start_idx:end_idx]
                if batch.device.type != str(device).split(':')[0]:
                    batch = batch.to(device)

                # Extract features
                features = model(batch)

                # Move to CPU and convert to numpy
                features_np = features.cpu().numpy()
                all_features[start_idx:end_idx] = features_np

                # Clear GPU memory
                del batch, features
                torch.cuda.empty_cache() if torch.cuda.is_available() else None

        # Calculate statistics
        mu = np.mean(all_features, axis=0)
        sigma = np.cov(all_features, rowvar=False)

    # Verify statistics
    if np.isnan(mu).any() or np.isnan(sigma).any():
        logger.warning("NaN values detected in statistics, applying correction")
        mu = np.nan_to_num(mu)
        sigma = np.nan_to_num(sigma)

    if np.isinf(mu).any() or np.isinf(sigma).any():
        logger.warning("Inf values detected in statistics, applying correction")
        mu = np.nan_to_num(mu, posinf=1e10, neginf=-1e10)
        sigma = np.nan_to_num(sigma, posinf=1e10, neginf=-1e10)

    logger.info(f"Activation statistics calculated in {time.time() - start_time:.2f} seconds")
    logger.info(f"Mean shape: {mu.shape}, Covariance shape: {sigma.shape}")
    logger.info(f"Mean range: [{mu.min():.4f}, {mu.max():.4f}], Covariance range: [{sigma.min():.4f}, {sigma.max():.4f}]")

    return mu, sigma


# 删除旧的calculate_frechet_distance函数，现在使用简化的calculate_fid函数


def calculate_fid_with_baseline(
    generated_images: torch.Tensor,
    baseline_path: str,
    inception_model: nn.Module = None,
    batch_size: int = 32,
    device: str = "cuda",
    inception_model_path: str = DEFAULT_INCEPTION_MODEL_PATH,
) -> float:
    """
    Calculate FID using precomputed baseline statistics.

    Args:
        generated_images (torch.Tensor): Generated images of shape (N, C, H, W)
        baseline_path (str): Path to precomputed baseline statistics
        inception_model (nn.Module, optional): Inception model. If None, a new one will be created.
        batch_size (int): Batch size
        device (str): Device to use
        inception_model_path (str): Path to pretrained InceptionV3 model

    Returns:
        float: FID score
    """
    import pickle

    logger.info(f"Calculating FID using precomputed baseline from {baseline_path}")
    start_time = time.time()

    # Load baseline statistics
    try:
        with open(baseline_path, 'rb') as f:
            baseline_stats = pickle.load(f)
        logger.info(f"Loaded baseline: {baseline_stats['num_samples']} samples, {baseline_stats['feature_dim']}D features")
    except Exception as e:
        logger.error(f"Failed to load baseline statistics: {e}")
        raise

    mu_real = baseline_stats['mu']
    sigma_real = baseline_stats['sigma']

    # Create inception model if not provided (configured for FID)
    if inception_model is None:
        inception_model = InceptionV3(model_path=inception_model_path, device=device, feature_type='fid')

    inception_model.eval()

    # Ensure images are on the correct device
    if generated_images.device != device:
        generated_images = generated_images.to(device)

    # Extract features from generated images
    logger.info("Extracting 2048-dim pool3 features from generated images...")
    fake_features = []
    with torch.no_grad():
        for i in range(0, len(generated_images), batch_size):
            batch = generated_images[i:i+batch_size]
            batch_features = inception_model(batch, feature_type='fid').detach().cpu().numpy()
            fake_features.append(batch_features)
    fake_features = np.concatenate(fake_features, axis=0)

    # Calculate statistics for generated images
    mu_fake = np.mean(fake_features, axis=0)
    sigma_fake = np.cov(fake_features, rowvar=False)

    # Avoid singular covariance matrices
    sigma_real += np.eye(sigma_real.shape[0]) * 1e-6
    sigma_fake += np.eye(sigma_fake.shape[0]) * 1e-6

    # FID calculation
    logger.info("Computing FID score with baseline...")

    # Mean difference squared
    ssdiff = np.sum((mu_real - mu_fake) ** 2)

    # Covariance matrix square root
    try:
        covmean, _ = scipy.linalg.sqrtm(sigma_real.dot(sigma_fake), disp=False)

        # Handle complex numbers
        if np.iscomplexobj(covmean):
            covmean = covmean.real

        # Final score
        fid = ssdiff + np.trace(sigma_real + sigma_fake - 2 * covmean)

    except Exception as e:
        logger.error(f"Error calculating matrix square root: {e}")
        # Use diagonal approximation as fallback
        sqrt_diag1 = np.sqrt(np.diag(sigma_real))
        sqrt_diag2 = np.sqrt(np.diag(sigma_fake))
        tr_covmean = np.sum(sqrt_diag1 * sqrt_diag2)
        fid = ssdiff + np.trace(sigma_real) + np.trace(sigma_fake) - 2 * tr_covmean

    # Ensure FID is non-negative
    if fid < 0:
        logger.warning(f"Computed negative FID: {fid:.4f}, setting to 0")
        fid = 0.0

    logger.info(f"FID calculation completed in {time.time() - start_time:.2f} seconds")
    logger.info(f"FID score: {fid:.4f}")

    return float(fid)


def calculate_fid(
    real_images: torch.Tensor,
    generated_images: torch.Tensor,
    inception_model: nn.Module = None,
    batch_size: int = 32,
    device: str = "cuda",
    inception_model_path: str = DEFAULT_INCEPTION_MODEL_PATH,
) -> float:
    """
    Calculate Frechet Inception Distance (FID) using 2048-dim pool3 features.

    使用Inception v3模型提取2048维pool3层特征向量，计算真实图像和生成图像分布的FID距离。
    这种方法比1000维分类特征更敏感，能更准确地评估生成质量。

    Args:
        real_images (torch.Tensor): Real images of shape (N, C, H, W)
        generated_images (torch.Tensor): Generated images of shape (N, C, H, W)
        inception_model (nn.Module, optional): Inception model. If None, a new one will be created.
        batch_size (int): Batch size
        device (str): Device to use
        inception_model_path (str): Path to pretrained InceptionV3 model

    Returns:
        float: FID score
    """
    logger.info(f"Calculating FID between {len(real_images)} real images and {len(generated_images)} generated images")
    start_time = time.time()

    # Create inception model if not provided (configured for FID)
    if inception_model is None:
        inception_model = InceptionV3(model_path=inception_model_path, device=device, feature_type='fid')

    # 确保模型处于评估模式
    inception_model.eval()

    # 优化设备一致性检查：使用torch.device对象进行精确比较
    target_device = torch.device(device)
    if real_images.device != target_device:
        logger.info(f"Moving real images from {real_images.device} to {target_device}")
        real_images = real_images.to(target_device)
    if generated_images.device != target_device:
        logger.info(f"Moving generated images from {generated_images.device} to {target_device}")
        generated_images = generated_images.to(target_device)

    # 特征提取（真实与生成图像）- 分批处理以避免内存问题，使用2048维FID特征
    logger.info("Extracting 2048-dim pool3 features from real images...")
    real_features = []
    with torch.no_grad():
        for i in range(0, len(real_images), batch_size):
            batch = real_images[i:i+batch_size]
            batch_features = inception_model(batch, feature_type='fid').detach().cpu().numpy()
            real_features.append(batch_features)
    real_features = np.concatenate(real_features, axis=0)

    logger.info("Extracting 2048-dim pool3 features from generated images...")
    fake_features = []
    with torch.no_grad():
        for i in range(0, len(generated_images), batch_size):
            batch = generated_images[i:i+batch_size]
            batch_features = inception_model(batch, feature_type='fid').detach().cpu().numpy()
            fake_features.append(batch_features)
    fake_features = np.concatenate(fake_features, axis=0)

    # 计算均值与协方差
    mu_real, sigma_real = real_features.mean(axis=0), np.cov(real_features, rowvar=False)
    mu_fake, sigma_fake = fake_features.mean(axis=0), np.cov(fake_features, rowvar=False)

    # 避免协方差矩阵奇异
    sigma_real += np.eye(sigma_real.shape[0]) * 1e-6
    sigma_fake += np.eye(sigma_fake.shape[0]) * 1e-6

    # FID计算
    logger.info("Computing FID score...")

    # 均值差的平方和
    ssdiff = np.sum((mu_real - mu_fake) ** 2)

    # 协方差矩阵平方根
    try:
        covmean, _ = scipy.linalg.sqrtm(sigma_real.dot(sigma_fake), disp=False)

        # 处理复数部分，取实数
        if np.iscomplexobj(covmean):
            covmean = covmean.real

        # 最终分数
        fid = ssdiff + np.trace(sigma_real + sigma_fake - 2 * covmean)

    except Exception as e:
        logger.error(f"Error calculating matrix square root: {e}")
        # 使用对角近似作为备选方案
        sqrt_diag1 = np.sqrt(np.diag(sigma_real))
        sqrt_diag2 = np.sqrt(np.diag(sigma_fake))
        tr_covmean = np.sum(sqrt_diag1 * sqrt_diag2)
        fid = ssdiff + np.trace(sigma_real) + np.trace(sigma_fake) - 2 * tr_covmean

    # 确保FID非负
    if fid < 0:
        logger.warning(f"Computed negative FID: {fid:.4f}, setting to 0")
        fid = 0.0

    logger.info(f"FID calculation completed in {time.time() - start_time:.2f} seconds")
    logger.info(f"FID score: {fid:.4f}")

    return float(fid)


def calculate_inception_score(
    images: torch.Tensor,
    inception_model: nn.Module = None,
    batch_size: int = 32,
    device: str = "cuda",
    splits: int = 10,
    inception_model_path: str = DEFAULT_INCEPTION_MODEL_PATH,
) -> Tuple[float, float]:
    """
    Calculate Inception Score (IS) using 1000-dim classification features.

    使用Inception v3对生成图像进行分类，输出1000维的概率分布（经softmax归一化）。
    保持使用1000维分类特征以确保与现有IS基准的一致性和可比性。

    Args:
        images (torch.Tensor): Images of shape (N, C, H, W)
        inception_model (nn.Module, optional): Inception model. If None, a new one will be created.
        batch_size (int): Batch size
        device (str): Device to use
        splits (int): Number of splits for IS calculation (default: 10)
        inception_model_path (str): Path to pretrained InceptionV3 model

    Returns:
        Tuple[float, float]: Mean and standard deviation of IS
    """
    logger.info(f"Calculating Inception Score for {len(images)} images with {splits} splits")
    start_time = time.time()

    # Create inception model if not provided (configured for IS)
    if inception_model is None:
        inception_model = InceptionV3(model_path=inception_model_path, device=device, feature_type='is')

    # 确保模型处于评估模式
    inception_model.eval()

    # Ensure images are on the correct device
    if images.device != device:
        images = images.to(device)

    # 分类预测（生成图像）- 分批处理以避免内存问题，使用1000维分类特征
    logger.info("Extracting 1000-dim classification predictions...")
    all_preds = []
    with torch.no_grad():
        for i in range(0, len(images), batch_size):
            batch = images[i:i+batch_size]
            logits = inception_model(batch, feature_type='is')
            preds = F.softmax(logits, dim=1)
            all_preds.append(preds.cpu().numpy())
    preds = np.concatenate(all_preds, axis=0)

    # 将预测结果划分为多个子集（默认splits=10）
    logger.info(f"Calculating IS scores for {splits} splits...")
    scores = []

    for i in range(splits):
        # 获取当前子集的预测结果
        part = preds[(i * preds.shape[0] // splits):((i + 1) * preds.shape[0] // splits), :]

        # 计算KL散度
        py = np.mean(part, axis=0)  # 边际分布 p(y)

        # 确保没有零值（避免log(0)）
        py = np.maximum(py, 1e-12)
        part = np.maximum(part, 1e-12)

        # KL散度公式：KL(p(y|x)||p(y))
        kl = part * (np.log(part) - np.log(np.expand_dims(py, 0)))
        kl = np.mean(np.sum(kl, 1))  # 对每个样本求和，然后求均值

        # 指数化KL散度得到IS分数
        scores.append(np.exp(kl))

    # 最终IS分数：输出所有子集分数的均值和标准差
    is_mean = np.mean(scores)  # 均值（主分数）
    is_std = np.std(scores)    # 标准差（稳定性）

    logger.info(f"Inception Score calculation completed in {time.time() - start_time:.2f} seconds")
    logger.info(f"Inception Score: {is_mean:.4f} ± {is_std:.4f}")

    return float(is_mean), float(is_std)


def preprocess_images_for_evaluation(
    images: torch.Tensor,
    name: str = "images",
    device: str = "cuda",
) -> torch.Tensor:
    """
    Preprocess images for evaluation to ensure consistency and numerical stability.

    This function performs several checks and preprocessing steps:
    1. Validates image format and dimensions
    2. Ensures images are on the correct device
    3. Checks for NaN or Inf values
    4. Ensures images are in the correct value range [-1, 1]
    5. Checks for sufficient variance in the images

    Args:
        images (torch.Tensor): Images to preprocess
        name (str): Name for logging (e.g., "real" or "generated")
        device (str): Device to use

    Returns:
        torch.Tensor: Preprocessed images
    """
    logger.info(f"Preprocessing {name} for evaluation...")

    # Validate input type
    if not isinstance(images, torch.Tensor):
        logger.error(f"{name} is not a torch.Tensor")
        raise TypeError(f"{name} must be a torch.Tensor")

    # Validate dimensions
    if images.dim() != 4:
        logger.error(f"{name} has {images.dim()} dimensions, expected 4")
        raise ValueError(f"{name} must be 4-dimensional (N, C, H, W)")

    # Validate channels
    if images.shape[1] != 3:
        logger.error(f"{name} has {images.shape[1]} channels, expected 3")
        raise ValueError(f"{name} must have 3 channels")

    # Move to device if needed
    if images.device != torch.device(device):
        logger.info(f"Moving {name} from {images.device} to {device}")
        images = images.to(device)

    # Check for NaN or Inf values
    if torch.isnan(images).any() or torch.isinf(images).any():
        logger.warning(f"{name} contains NaN or Inf values, replacing with zeros")
        images = torch.nan_to_num(images, nan=0.0, posinf=1.0, neginf=-1.0)

    # Check value range
    min_val = images.min().item()
    max_val = images.max().item()
    logger.info(f"{name} value range: [{min_val:.4f}, {max_val:.4f}]")

    # Ensure values are in [-1, 1] range
    if min_val < -1 or max_val > 1:
        logger.warning(f"{name} values are outside [-1, 1] range, clamping")
        images = torch.clamp(images, -1, 1)

    # Check variance
    channel_vars = [torch.var(images[:, c]).item() for c in range(images.shape[1])]
    logger.info(f"{name} channel variances: {channel_vars}")

    # Check for low variance
    if any(var < 1e-4 for var in channel_vars):
        logger.warning(f"{name} has very low variance in some channels, this may affect evaluation")

        # Add small noise to channels with low variance
        for c in range(images.shape[1]):
            if channel_vars[c] < 1e-4:
                logger.info(f"Adding small noise to {name} channel {c} to increase variance")
                noise = torch.randn_like(images[:, c:c+1]) * 0.01
                images[:, c:c+1] = images[:, c:c+1] + noise

    # Final check
    logger.info(f"Preprocessed {name}: shape={images.shape}, range=[{images.min().item():.4f}, {images.max().item():.4f}]")

    return images


def evaluate_model(
    real_images: torch.Tensor,
    generated_images: torch.Tensor,
    inception_model_path: str = DEFAULT_INCEPTION_MODEL_PATH,
    batch_size: int = 32,
    device: str = "cuda",
    num_samples: int = DEFAULT_NUM_SAMPLES,
    seed: Optional[int] = None,
) -> Dict[str, float]:
    """
    Evaluate model using FID and IS metrics.

    This function calculates both FID and IS metrics for the generated images.
    - FID measures the similarity between real and generated image distributions
    - IS measures the quality and diversity of generated images

    Args:
        real_images (torch.Tensor): Real images of shape (N, 3, H, W)
        generated_images (torch.Tensor): Generated images of shape (M, 3, H, W)
        inception_model_path (str): Path to pretrained InceptionV3 model
        batch_size (int): Batch size for feature extraction
        device (str): Device to use for computation
        num_samples (int): Number of samples to use for evaluation (if generated_images has more)

    Returns:
        Dict[str, float]: Evaluation metrics including FID and IS
    """
    logger.info("Starting model evaluation...")
    start_time = time.time()

    # Set random seed if provided for reproducible evaluation
    if seed is not None:
        try:
            from utils.seed_utils import set_seed_for_evaluation
            actual_seed = set_seed_for_evaluation(seed)
            logger.info(f"Random seed set to {actual_seed} for reproducible evaluation")
        except ImportError:
            logger.warning("Cannot import seed_utils, using basic random seed setting")
            import torch
            import random

            torch.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
            np.random.seed(seed)  # 使用全局导入的np
            random.seed(seed)
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
            logger.info(f"Basic random seed set to {seed}")

    # Preprocess images to ensure consistency and numerical stability
    real_images = preprocess_images_for_evaluation(real_images, "real images", device)
    generated_images = preprocess_images_for_evaluation(generated_images, "generated images", device)

    # Verify local Inception model exists
    if not os.path.exists(inception_model_path):
        logger.error(f"Local Inception model not found at {inception_model_path}")
        try:
            # This will raise a clear error message about the missing local model
            download_inception_model(inception_model_path)
        except Exception as e:
            logger.error(f"Local model check failed: {e}")
            raise

    # Verify the local model is valid
    logger.info(f"Verifying local Inception model at {inception_model_path}")
    if not verify_inception_model(inception_model_path, device):
        logger.error("Local Inception model verification failed")
        logger.error("=" * 60)
        logger.error("INVALID LOCAL INCEPTION MODEL")
        logger.error("=" * 60)
        logger.error(f"Model path: {inception_model_path}")
        logger.error("The local model file exists but failed verification.")
        logger.error("This could indicate:")
        logger.error("1. Corrupted model file")
        logger.error("2. Incompatible model format")
        logger.error("3. Missing model components")
        logger.error("")
        logger.error("Please re-download the correct Inception-v3 model:")
        logger.error("1. Download from: https://download.pytorch.org/models/inception_v3_google-0cc3c7bd.pth")
        logger.error(f"2. Replace the file at: {inception_model_path}")
        logger.error("3. Restart the evaluation process")
        logger.error("=" * 60)
        raise RuntimeError(f"Local Inception model at {inception_model_path} failed verification")

    # Ensure we have enough real images
    if len(real_images) < 10:
        raise ValueError(f"Need at least 10 real images, got {len(real_images)}")

    # Ensure we have enough generated images
    if len(generated_images) < num_samples:
        logger.warning(f"Requested {num_samples} samples but only have {len(generated_images)}")
        num_samples = len(generated_images)

    # Limit the number of samples if needed
    if len(generated_images) > num_samples:
        logger.info(f"Limiting generated images from {len(generated_images)} to {num_samples} for evaluation")
        # Randomly select samples to ensure diversity
        indices = torch.randperm(len(generated_images))[:num_samples]
        generated_images = generated_images[indices]

    logger.info(f"Using {len(real_images)} real images and {len(generated_images)} generated images")

    # Load inception models with differential configuration
    logger.info("Loading Inception model for FID calculation (2048-dim features)...")
    inception_model_fid = InceptionV3(model_path=inception_model_path, device=device, feature_type='fid')

    logger.info("Loading Inception model for IS calculation (1000-dim features)...")
    inception_model_is = InceptionV3(model_path=inception_model_path, device=device, feature_type='is')

    # Calculate FID using 2048-dim pool3 features (prefer precomputed baseline)
    logger.info("Calculating FID with 2048-dim pool3 features...")

    # Check for precomputed baseline
    baseline_path = "evaluation/fid_baseline_2048dim.pkl"
    if os.path.exists(baseline_path):
        logger.info(f"Using precomputed FID baseline from {baseline_path}")
        try:
            fid = calculate_fid_with_baseline(
                generated_images, baseline_path, inception_model_fid, batch_size, device
            )
        except Exception as e:
            logger.error(f"Error calculating FID with baseline: {e}")
            logger.info("Falling back to real-time FID calculation...")
            try:
                fid = calculate_fid(
                    real_images, generated_images, inception_model_fid, batch_size, device
                )
            except Exception as e2:
                logger.error(f"Error calculating FID (fallback): {e2}")
                fid = float('nan')
    else:
        logger.info(f"Precomputed baseline not found at {baseline_path}")
        logger.info("Computing FID with real-time calculation and saving baseline for future use...")
        try:
            # Calculate FID normally
            fid = calculate_fid(
                real_images, generated_images, inception_model_fid, batch_size, device
            )

            # Try to save baseline for future use
            try:
                logger.info("Attempting to save FID baseline for future evaluations...")
                import pickle

                # Extract features from real images
                real_features = []
                with torch.no_grad():
                    for i in range(0, len(real_images), batch_size):
                        batch = real_images[i:i+batch_size]
                        batch_features = inception_model_fid(batch, feature_type='fid').detach().cpu().numpy()
                        real_features.append(batch_features)
                real_features = np.concatenate(real_features, axis=0)

                # Calculate baseline statistics
                mu_real = np.mean(real_features, axis=0)
                sigma_real = np.cov(real_features, rowvar=False)

                baseline_stats = {
                    'mu': mu_real,
                    'sigma': sigma_real,
                    'num_samples': len(real_features),
                    'feature_dim': real_features.shape[1],
                    'feature_type': '2048-dim pool3',
                    'dataset': 'HTRU1 test set (pulsar samples only)'
                }

                # Ensure directory exists
                os.makedirs(os.path.dirname(baseline_path), exist_ok=True)

                # Save baseline
                with open(baseline_path, 'wb') as f:
                    pickle.dump(baseline_stats, f)

                logger.info(f"Successfully saved FID baseline to {baseline_path}")
                logger.info(f"Future evaluations will use this precomputed baseline")

            except Exception as save_e:
                logger.warning(f"Failed to save FID baseline: {save_e}")
                logger.warning("Future evaluations will continue to use real-time calculation")

        except Exception as e:
            logger.error(f"Error calculating FID: {e}")
            fid = float('nan')

    # Calculate IS using 1000-dim classification features
    logger.info("Calculating IS with 1000-dim classification features...")
    try:
        is_mean, is_std = calculate_inception_score(
            generated_images,
            inception_model_is,
            batch_size,
            device,
            splits=10
        )
    except Exception as e:
        logger.error(f"Error calculating IS: {e}")
        is_mean, is_std = 1.0, 0.0

    # Compile results
    results = {
        "fid": fid,
        "is_mean": is_mean,
        "is_std": is_std,
    }

    # Validate results
    if np.isnan(fid) or np.isinf(fid) or fid < 0 or fid > 1000:
        logger.warning(f"FID value {fid} is outside expected range [0, 300]")
        if fid > 1000:
            logger.warning("FID > 1000 often indicates numerical instability in matrix square root calculation")

    if np.isnan(is_mean) or np.isinf(is_mean) or is_mean < 1.0 or is_mean > 10.0:
        logger.warning(f"IS value {is_mean} is outside expected range [1.0, 10.0]")

    logger.info(f"Evaluation completed in {time.time() - start_time:.2f} seconds")
    logger.info(f"Results: FID = {fid:.4f}, IS = {is_mean:.4f} ± {is_std:.4f}")

    return results


def evaluate_channels(
    real_images: torch.Tensor,
    generated_images: torch.Tensor,
    inception_model_path: str = DEFAULT_INCEPTION_MODEL_PATH,
    batch_size: int = 32,
    device: str = "cuda",
    num_samples: int = DEFAULT_NUM_SAMPLES,  # Default to 995 samples (HTRU1 training set positive samples)
) -> Dict[str, Dict[str, float]]:
    """
    Evaluate model for each channel separately with simplified approach.

    This implementation is based on the approach used in pulsar_generate_main project,
    with additional numerical stability enhancements.

    This function performs channel-specific evaluation by isolating each channel
    and calculating FID and IS metrics for that channel only. This is particularly
    useful for multi-channel data where different channels have different characteristics.

    For HTRU1 dataset:
    - Channel 0: Period-DM surface
    - Channel 1: Phase-Subband surface
    - Channel 2: Phase-Subintegration surface

    Args:
        real_images (torch.Tensor): Real images of shape (N, 3, H, W)
        generated_images (torch.Tensor): Generated images of shape (M, 3, H, W)
        inception_model_path (str): Path to pretrained InceptionV3 model
        batch_size (int): Batch size
        device (str): Device to use
        num_samples (int): Number of samples to use for evaluation (default: 995 for HTRU1 training set)

    Returns:
        Dict[str, Dict[str, float]]: Evaluation metrics for each channel
    """
    logger.info("Starting channel-specific evaluation with simplified approach...")
    start_time = time.time()

    # Validate inputs
    if not isinstance(real_images, torch.Tensor) or not isinstance(generated_images, torch.Tensor):
        raise TypeError("Images must be torch.Tensor objects")

    if real_images.dim() != 4 or generated_images.dim() != 4:
        raise ValueError("Images must be 4-dimensional (N, C, H, W)")

    # 检查通道数，但不强制要求3通道
    if real_images.shape[1] < 1 or generated_images.shape[1] < 1:
        raise ValueError("Images must have at least 1 channel")

    # 确定要评估的通道数
    num_channels = min(real_images.shape[1], generated_images.shape[1], 3)
    logger.info(f"Will evaluate {num_channels} channels")

    # Verify local Inception model exists
    if not os.path.exists(inception_model_path):
        logger.error(f"Local Inception model not found at {inception_model_path}")
        try:
            # This will raise a clear error message about the missing local model
            download_inception_model(inception_model_path)
        except Exception as e:
            logger.error(f"Local model check failed: {e}")
            raise

    # Ensure we have enough real images
    if len(real_images) < 10:
        raise ValueError(f"Need at least 10 real images, got {len(real_images)}")

    # Match the number of generated samples to real samples if possible
    target_num_samples = min(num_samples, len(real_images))

    # Ensure we have enough generated images
    if len(generated_images) < target_num_samples:
        logger.warning(f"Requested {target_num_samples} samples but only have {len(generated_images)}")
        target_num_samples = len(generated_images)

    # Limit the number of real samples if needed
    if len(real_images) > target_num_samples:
        logger.info(f"Limiting real images from {len(real_images)} to {target_num_samples} for evaluation")
        # Randomly select samples to ensure diversity
        indices = torch.randperm(len(real_images))[:target_num_samples]
        real_images = real_images[indices]

    # Limit the number of generated samples if needed
    if len(generated_images) > target_num_samples:
        logger.info(f"Limiting generated images from {len(generated_images)} to {target_num_samples} for evaluation")
        # Randomly select samples to ensure diversity
        indices = torch.randperm(len(generated_images))[:target_num_samples]
        generated_images = generated_images[indices]

    logger.info(f"Using {len(real_images)} real images and {len(generated_images)} generated images")

    # Channel names for HTRU1 dataset
    channel_names = {
        0: "Period-DM surface",
        1: "Phase-Subband surface",
        2: "Phase-Subintegration surface"
    }

    # Initialize results
    results = {}

    # Load inception models with differential configuration (reuse for all channels)
    logger.info("Loading Inception models with differential configuration...")
    inception_model_fid = InceptionV3(model_path=inception_model_path, device=device, feature_type='fid')
    inception_model_is = InceptionV3(model_path=inception_model_path, device=device, feature_type='is')

    # 确保模型处于评估模式
    inception_model_fid.eval()
    inception_model_is.eval()
    if hasattr(inception_model_fid, 'model'):
        inception_model_fid.model.eval()
    if hasattr(inception_model_is, 'model'):
        inception_model_is.model.eval()

    # Track overall metrics
    overall_fids = []
    overall_is_means = []

    # Evaluate each channel
    for channel in range(num_channels):
        channel_name = channel_names.get(channel, f"Channel {channel}")
        logger.info(f"Evaluating {channel_name} (channel {channel})...")

        # Extract the target channel
        target_real_channel = real_images[:, channel:channel+1, :, :]  # Shape: [N, 1, H, W]
        target_gen_channel = generated_images[:, channel:channel+1, :, :]  # Shape: [N, 1, H, W]

        # Log channel value ranges
        real_min = target_real_channel.min().item()
        real_max = target_real_channel.max().item()
        gen_min = target_gen_channel.min().item()
        gen_max = target_gen_channel.max().item()
        logger.info(f"Channel {channel} value ranges - Real: [{real_min:.4f}, {real_max:.4f}], Generated: [{gen_min:.4f}, {gen_max:.4f}]")

        # Handle NaN or Inf values if present
        if torch.isnan(target_real_channel).any() or torch.isinf(target_real_channel).any():
            logger.warning(f"Real images channel {channel} contains NaN or Inf values, replacing with zeros")
            target_real_channel = torch.nan_to_num(target_real_channel, nan=0.0, posinf=1.0, neginf=-1.0)

        if torch.isnan(target_gen_channel).any() or torch.isinf(target_gen_channel).any():
            logger.warning(f"Generated images channel {channel} contains NaN or Inf values, replacing with zeros")
            target_gen_channel = torch.nan_to_num(target_gen_channel, nan=0.0, posinf=1.0, neginf=-1.0)

        # Replicate the single channel to all three channels (Inception v3需要3通道输入)
        real_channel = target_real_channel.repeat(1, 3, 1, 1)  # Shape: [N, 3, H, W]
        gen_channel = target_gen_channel.repeat(1, 3, 1, 1)  # Shape: [N, 3, H, W]

        # Ensure values are in [-1, 1] range
        real_channel = torch.clamp(real_channel, -1, 1)
        gen_channel = torch.clamp(gen_channel, -1, 1)

        logger.info(f"Evaluating {channel_name} using channel replication method")
        logger.info(f"Channel shapes - Real: {real_channel.shape}, Generated: {gen_channel.shape}")

        # Calculate FID using 2048-dim features
        logger.info(f"Calculating FID for {channel_name} with 2048-dim features...")
        try:
            fid = calculate_fid(
                real_channel, gen_channel, inception_model_fid, batch_size, device
            )
            overall_fids.append(fid)
        except Exception as e:
            logger.error(f"Error calculating FID for {channel_name}: {e}")
            fid = float('nan')

        # Calculate IS using 1000-dim features
        logger.info(f"Calculating IS for {channel_name} with 1000-dim features...")
        try:
            is_mean, is_std = calculate_inception_score(
                gen_channel,
                inception_model_is,
                batch_size,
                device,
                splits=10
            )
            overall_is_means.append(is_mean)
        except Exception as e:
            logger.error(f"Error calculating IS for {channel_name}: {e}")
            is_mean, is_std = 1.0, 0.0

        # Store results
        results[f"channel_{channel}"] = {
            "name": channel_name,
            "fid": fid,
            "is_mean": is_mean,
            "is_std": is_std,
        }

        logger.info(f"{channel_name} results: FID = {fid:.4f}, IS = {is_mean:.4f} ± {is_std:.4f}")

    # Calculate average metrics across channels
    if overall_fids:
        avg_fid = sum(overall_fids) / len(overall_fids)
        results["average"] = {
            "name": "Average across channels",
            "fid": avg_fid,
            "is_mean": sum(overall_is_means) / len(overall_is_means) if overall_is_means else 1.0,
            "is_std": 0.0
        }
        logger.info(f"Average results: FID = {avg_fid:.4f}, IS = {results['average']['is_mean']:.4f}")

    logger.info(f"Channel-specific evaluation completed in {time.time() - start_time:.2f} seconds")
    return results


def generate_samples_for_evaluation(
    model: torch.nn.Module,
    num_samples: int = DEFAULT_NUM_SAMPLES,
    batch_size: int = 32,
    device: str = "cuda",
    sampler: Optional[torch.nn.Module] = None,
    num_inference_steps: int = 20,
) -> torch.Tensor:
    """
    Generate samples for evaluation with enhanced memory efficiency and error handling.

    Updated to support both traditional DDPM models and PulsarAdaptiveSampler.

    This function generates samples from a diffusion model in batches,
    which is more memory-efficient than generating all samples at once.
    It's designed to work with the DDPM model and PulsarAdaptiveSampler in this project.

    Args:
        model (torch.nn.Module): Diffusion model with a sample method
        num_samples (int): Number of samples to generate
        batch_size (int): Batch size for generation
        device (str): Device to use
        sampler (torch.nn.Module, optional): External sampler (e.g., PulsarAdaptiveSampler)
        num_inference_steps (int): Number of inference steps for sampling

    Returns:
        torch.Tensor: Generated samples of shape (num_samples, 3, H, W)
    """
    logger.info(f"Generating {num_samples} samples for evaluation with batch size {batch_size}")
    start_time = time.time()

    # Determine which sampler to use
    if sampler is not None:
        logger.info(f"Using external sampler: {type(sampler).__name__}")
        sampling_method = "external_sampler"
        # Validate external sampler
        if not hasattr(sampler, 'sample'):
            raise AttributeError("External sampler must have a 'sample' method")
    else:
        logger.info("Using model's built-in sample method")
        sampling_method = "model_builtin"
        # Validate model
        if not hasattr(model, 'sample'):
            raise AttributeError("Model must have a 'sample' method")

    # Ensure model is on the correct device
    model_device = next(model.parameters()).device
    if str(model_device) != str(device) and str(model_device) != f"{device}:0":
        logger.info(f"Moving model from {model_device} to {device}")
        model = model.to(device)

    # Adjust batch size based on available memory and number of samples
    if str(device).startswith('cuda'):
        # For large sample counts, use smaller batch size to avoid OOM
        if num_samples > 1000:
            original_batch_size = batch_size
            # Adjust batch size based on GPU memory
            try:
                # Get available GPU memory
                if torch.cuda.is_available():
                    free_memory = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)
                    free_memory_gb = free_memory / (1024**3)
                    logger.info(f"Available GPU memory: {free_memory_gb:.2f} GB")

                    # Adjust batch size based on available memory
                    if free_memory_gb < 2:
                        batch_size = min(batch_size, 8)
                    elif free_memory_gb < 4:
                        batch_size = min(batch_size, 16)
                    elif free_memory_gb < 8:
                        batch_size = min(batch_size, 24)

                    if batch_size != original_batch_size:
                        logger.info(f"Adjusted batch size from {original_batch_size} to {batch_size} based on available GPU memory")
            except Exception as e:
                logger.warning(f"Error checking GPU memory: {e}, using default batch size")

    # Ensure batch size is not larger than number of samples
    if batch_size > num_samples:
        logger.warning(f"Batch size {batch_size} is larger than number of samples {num_samples}, reducing batch size")
        batch_size = num_samples

    # Calculate number of batches
    num_batches = int(np.ceil(num_samples / batch_size))
    logger.info(f"Will generate {num_samples} samples in {num_batches} batches of size {batch_size}")

    # Generate samples with memory-efficient approach
    samples = []
    total_generated = 0
    max_retries = 3

    try:
        with torch.no_grad():
            for i in tqdm(range(num_batches), desc="Generating samples"):
                # Calculate batch size for this batch
                batch_size_i = min(batch_size, num_samples - total_generated)

                # Generate samples with retry mechanism
                batch_samples = None
                current_retry = 0

                while batch_samples is None and current_retry < max_retries:
                    try:
                        # Use external sampler if provided (e.g., PulsarAdaptiveSampler)
                        if sampler is not None:
                            logger.info(f"Using external sampler for batch {i+1}/{num_batches}")
                            batch_samples = sampler.sample(
                                batch_size=batch_size_i,
                                shape=(3, 32, 32),
                                num_inference_steps=num_inference_steps,
                                verbose=False
                            )
                        else:
                            # Use model's built-in sampling method
                            # Try to use EMA model if available (preferred for evaluation)
                            if hasattr(model, 'ema_model') and model.ema_model is not None:
                                logger.info(f"Using EMA model for batch {i+1}/{num_batches}")

                                # 简化EMA模型使用逻辑，确保稳定性和可重现性
                                ema_available = False
                                ema_type = "unknown"

                                # 检查EMA模型的可用性和类型
                                if hasattr(model, 'ema_model') and model.ema_model is not None:
                                    if isinstance(model.ema_model, nn.Module):
                                        ema_available = True
                                        ema_type = "module"
                                    elif isinstance(model.ema_model, dict):
                                        ema_available = True
                                        ema_type = "state_dict"
                                elif hasattr(model, '_ema_state_dict') and model._ema_state_dict is not None:
                                    ema_available = True
                                    ema_type = "private_state_dict"

                                logger.info(f"EMA model status: available={ema_available}, type={ema_type}")

                                if ema_available:
                                    try:
                                        if ema_type == "module":
                                            # 直接使用EMA模型实例
                                            logger.info("Using EMA model instance for sampling")
                                            batch_samples = model.sample(batch_size_i, use_ema=True)
                                        elif ema_type == "state_dict":
                                            # 临时加载EMA状态字典（确保原子性操作）
                                            logger.info("Temporarily loading EMA state dict for sampling")
                                            if hasattr(model, 'model') and model.model is not None:
                                                # 保存当前权重
                                                current_state = {k: v.clone().detach() for k, v in model.model.state_dict().items()}
                                                try:
                                                    # 加载EMA权重
                                                    model.model.load_state_dict(model.ema_model, strict=False)
                                                    # 生成样本
                                                    batch_samples = model.sample(batch_size_i, use_ema=False)  # 已经加载了EMA权重
                                                finally:
                                                    # 确保恢复原始权重（即使出现异常）
                                                    model.model.load_state_dict(current_state)
                                            else:
                                                logger.warning("EMA state dict available but no model.model found")
                                                batch_samples = model.sample(batch_size_i, use_ema=False)
                                        else:
                                            # 使用私有EMA状态字典
                                            logger.info("Using private EMA state dict for sampling")
                                            batch_samples = model.sample(batch_size_i, use_ema=True)
                                    except Exception as e:
                                        logger.error(f"Error using EMA model: {e}, falling back to regular model")
                                        batch_samples = model.sample(batch_size_i, use_ema=False)
                                else:
                                    # 尝试使用 average_parameters 上下文管理器
                                    try:
                                        logger.info("Trying to use average_parameters context manager")
                                        with model.ema_model.average_parameters():
                                            batch_samples = model.sample(batch_size_i)
                                    except Exception as e:
                                        logger.warning(f"Failed to use average_parameters: {e}")
                                        # 尝试直接使用 use_ema 参数
                                        try:
                                            batch_samples = model.sample(batch_size_i, use_ema=True)
                                        except Exception as e2:
                                            logger.warning(f"Failed to use use_ema parameter: {e2}")
                                            # 回退到普通模型
                                            batch_samples = model.sample(batch_size_i)
                            elif hasattr(model, 'use_ema') and callable(getattr(model, 'use_ema', None)):
                                logger.info(f"Using use_ema method for batch {i+1}/{num_batches}")
                                # Some models have a use_ema method
                                batch_samples = model.sample(batch_size_i, use_ema=True)
                            else:
                                # Otherwise just call sample
                                logger.info(f"Using regular model for batch {i+1}/{num_batches}")
                                batch_samples = model.sample(batch_size_i)

                        # Validate samples
                        if batch_samples is None:
                            raise ValueError("Model returned None instead of samples")

                        if not isinstance(batch_samples, torch.Tensor):
                            raise TypeError(f"Model returned {type(batch_samples)} instead of torch.Tensor")

                        if batch_samples.shape[0] != batch_size_i:
                            logger.warning(f"Model returned {batch_samples.shape[0]} samples instead of {batch_size_i}")
                            # If we got fewer samples than requested but still some, we can use them
                            if batch_samples.shape[0] > 0:
                                logger.info(f"Using {batch_samples.shape[0]} samples instead of {batch_size_i}")
                            else:
                                raise ValueError(f"Model returned 0 samples instead of {batch_size_i}")

                        # Check for NaN or Inf values
                        if torch.isnan(batch_samples).any() or torch.isinf(batch_samples).any():
                            logger.warning(f"Batch {i+1}/{num_batches} contains NaN or Inf values, retrying with smaller batch")
                            raise ValueError("Samples contain NaN or Inf values")

                    except Exception as e:
                        current_retry += 1
                        logger.warning(f"Error generating batch {i+1}/{num_batches} (attempt {current_retry}/{max_retries}): {e}")

                        if current_retry >= max_retries:
                            logger.error(f"Failed to generate batch {i+1}/{num_batches} after {max_retries} attempts")
                            # Try with a smaller batch size as last resort
                            try:
                                smaller_batch = max(1, batch_size_i // 2)
                                logger.info(f"Trying with smaller batch size: {smaller_batch}")

                                sub_samples_list = []
                                for j in range(0, batch_size_i, smaller_batch):
                                    sub_batch_size = min(smaller_batch, batch_size_i - j)

                                    if hasattr(model, 'ema_model') and model.ema_model is not None:
                                        with model.ema_model.average_parameters():
                                            sub_samples = model.sample(sub_batch_size)
                                    elif hasattr(model, 'use_ema') and callable(getattr(model, 'use_ema', None)):
                                        sub_samples = model.sample(sub_batch_size, use_ema=True)
                                    else:
                                        sub_samples = model.sample(sub_batch_size)

                                    if sub_samples is not None and not torch.isnan(sub_samples).any() and not torch.isinf(sub_samples).any():
                                        sub_samples_list.append(sub_samples)

                                if sub_samples_list:
                                    batch_samples = torch.cat(sub_samples_list, dim=0)
                                    logger.info(f"Successfully generated {batch_samples.shape[0]} samples with smaller batches")
                                else:
                                    logger.error("Failed to generate any valid samples with smaller batches")
                                    # Create random noise as fallback for this batch
                                    batch_samples = torch.randn(batch_size_i, 3, 32, 32, device=device)
                                    logger.warning("Using random noise as fallback for this batch")
                            except Exception as e2:
                                logger.error(f"Error with smaller batches: {e2}")
                                # Create random noise as fallback
                                batch_samples = torch.randn(batch_size_i, 3, 32, 32, device=device)
                                logger.warning("Using random noise as fallback for this batch")
                        else:
                            # Reduce batch size for retry
                            batch_size_i = max(1, int(batch_size_i * 0.8))
                            logger.info(f"Reducing batch size to {batch_size_i} for retry")
                            # Clear GPU memory before retry
                            torch.cuda.empty_cache() if torch.cuda.is_available() else None

                # If we have valid samples, process them
                if batch_samples is not None:
                    # Move to CPU to save GPU memory
                    batch_samples = batch_samples.cpu()

                    # 严格确保样本在[-1, 1]范围内
                    min_val = batch_samples.min().item()
                    max_val = batch_samples.max().item()

                    if min_val < -1.0001 or max_val > 1.0001:  # 允许微小的浮点误差
                        logger.warning(f"Batch {i+1}/{num_batches} has values outside [-1, 1] range: [{min_val:.6f}, {max_val:.6f}]")

                        # 使用更智能的归一化策略
                        if max_val - min_val > 1e-8:
                            # 保持相对关系的归一化
                            batch_samples = 2.0 * (batch_samples - min_val) / (max_val - min_val) - 1.0

                        # 最终硬裁剪确保严格在[-1,1]范围内
                        batch_samples = torch.clamp(batch_samples, -1.0, 1.0)

                        # 验证修复效果
                        new_min = batch_samples.min().item()
                        new_max = batch_samples.max().item()
                        logger.info(f"Batch {i+1} normalized to range: [{new_min:.6f}, {new_max:.6f}]")

                    # Append to list
                    samples.append(batch_samples)
                    total_generated += batch_samples.shape[0]

                    # Log progress
                    if (i + 1) % 5 == 0 or i == num_batches - 1:
                        logger.info(f"Generated {total_generated}/{num_samples} samples")

                    # Clear GPU memory
                    del batch_samples
                    torch.cuda.empty_cache() if torch.cuda.is_available() else None

        # Concatenate all batches
        if samples:
            samples = torch.cat(samples, dim=0)
            logger.info(f"Successfully generated {len(samples)}/{num_samples} samples")
        else:
            logger.error("Failed to generate any samples")
            # Create dummy samples as fallback
            samples = torch.randn(num_samples, 3, 32, 32)
            logger.warning("Using random noise as fallback samples")

    except Exception as e:
        logger.error(f"Unexpected error during sample generation: {e}")
        # Create dummy samples as fallback
        samples = torch.randn(num_samples, 3, 32, 32)
        logger.warning("Using random noise as fallback samples due to unexpected error")

    # Ensure we have exactly num_samples samples
    if len(samples) > num_samples:
        logger.info(f"Generated {len(samples)} samples, trimming to {num_samples}")
        samples = samples[:num_samples]
    elif len(samples) < num_samples:
        # Pad with copies if we don't have enough
        logger.warning(f"Only generated {len(samples)} samples, padding to {num_samples}")

        if len(samples) == 0:
            # If we have no samples at all, create random noise
            samples = torch.randn(num_samples, 3, 32, 32)
            logger.warning("Using random noise as all samples")
        else:
            # Pad with copies of existing samples plus small noise
            padding = []
            for i in range(num_samples - len(samples)):
                # Use existing samples with small noise
                idx = i % len(samples)
                sample = samples[idx].clone()
                # Add small noise to avoid identical samples
                noise = torch.randn_like(sample) * 0.01
                sample = sample + noise
                padding.append(sample.unsqueeze(0))

            padding = torch.cat(padding, dim=0)
            samples = torch.cat([samples, padding], dim=0)
            logger.info(f"Padded to {len(samples)} samples")

    # 最终严格验证和修复
    min_val = samples.min().item()
    max_val = samples.max().item()

    if min_val < -1.0001 or max_val > 1.0001:  # 允许微小的浮点误差
        logger.warning(f"Final samples out of range: min={min_val:.6f}, max={max_val:.6f}")

        # 使用智能归一化策略保持数据分布
        if max_val - min_val > 1e-8:
            logger.info("Applying intelligent normalization to preserve data distribution")
            samples = 2.0 * (samples - min_val) / (max_val - min_val) - 1.0

        # 最终硬裁剪确保严格在[-1,1]范围内
        samples = torch.clamp(samples, -1.0, 1.0)

        # 验证修复效果
        final_min = samples.min().item()
        final_max = samples.max().item()
        logger.info(f"Final samples normalized to range: [{final_min:.6f}, {final_max:.6f}]")
    else:
        logger.info(f"Final samples already in valid range: [{min_val:.6f}, {max_val:.6f}]")

    # Check for NaN or Inf values
    if torch.isnan(samples).any() or torch.isinf(samples).any():
        logger.warning("Final samples contain NaN or Inf values, replacing with zeros")
        samples = torch.nan_to_num(samples, nan=0.0, posinf=1.0, neginf=-1.0)

    logger.info(f"Generated {len(samples)} samples in {time.time() - start_time:.2f} seconds")
    logger.info(f"Sample range: [{samples.min().item():.4f}, {samples.max().item():.4f}]")

    return samples


def calculate_fid_is(
    real_images: torch.Tensor,
    generated_images: torch.Tensor,
    inception_model: nn.Module = None,
    batch_size: int = 32,
    device: str = "cuda",
    inception_model_path: str = DEFAULT_INCEPTION_MODEL_PATH,
) -> Tuple[float, float]:
    """
    Calculate both FID and IS metrics using the same Inception model.

    Args:
        real_images (torch.Tensor): Real images of shape (N, C, H, W)
        generated_images (torch.Tensor): Generated images of shape (N, C, H, W)
        inception_model (nn.Module, optional): Inception model. If None, a new one will be created.
        batch_size (int): Batch size for processing
        device (str): Device to use
        inception_model_path (str): Path to pretrained InceptionV3 model

    Returns:
        Tuple[float, float]: (FID score, IS score)
    """
    logger.info("Calculating FID and IS metrics...")
    start_time = time.time()

    # Create inception model if not provided
    if inception_model is None:
        inception_model = InceptionV3(model_path=inception_model_path, device=device)

    # Calculate FID
    fid_score = calculate_fid(
        real_images=real_images,
        generated_images=generated_images,
        inception_model=inception_model,
        batch_size=batch_size,
        device=device,
        inception_model_path=inception_model_path
    )

    # Calculate IS
    is_score = calculate_inception_score(
        images=generated_images,
        inception_model=inception_model,
        batch_size=batch_size,
        device=device,
        inception_model_path=inception_model_path
    )

    total_time = time.time() - start_time
    logger.info(f"FID and IS calculation completed in {total_time:.2f} seconds")
    logger.info(f"Results: FID = {fid_score:.4f}, IS = {is_score:.4f}")

    return fid_score, is_score
