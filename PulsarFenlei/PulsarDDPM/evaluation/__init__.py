"""
Evaluation metrics for PulsarDDPM.
"""

# 导出主要模块和函数，使它们可以通过包导入
from .metrics import (
    verify_inception_model,
    DEFAULT_INCEPTION_MODEL_PATH,
    DEFAULT_NUM_SAMPLES
)

# 确保evaluate_model模块可以被导入
import os
import sys
import importlib.util

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 动态导入evaluate_model模块
try:
    spec = importlib.util.spec_from_file_location(
        "evaluate_model",
        os.path.join(current_dir, "evaluate_model.py")
    )
    evaluate_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(evaluate_module)

    # 导出main函数
    main = evaluate_module.main
except Exception as e:
    print(f"Warning: Failed to import evaluate_model module: {e}")
    main = None
