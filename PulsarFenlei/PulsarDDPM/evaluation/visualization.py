#!/usr/bin/env python3
"""
可视化模块
用于训练过程监控和生成样本可视化

基于WGAN-GP+VAE实施计划的可视化需求：
- 训练曲线绘制
- 生成样本网格显示
- 真实vs生成样本对比
- 三通道脉冲星数据可视化
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from typing import List, Dict, Optional, Tuple
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def visualize_samples(samples: torch.Tensor, title: str = "Generated Samples",
                     save_path: Optional[str] = None, nrow: int = 8) -> None:
    """
    可视化脉冲星样本
    
    Args:
        samples: 样本张量 [N, 3, 32, 32]
        title: 图像标题
        save_path: 保存路径
        nrow: 每行样本数
    """
    # 转换为numpy并调整范围
    if isinstance(samples, torch.Tensor):
        samples = samples.detach().cpu().numpy()
    
    # 归一化到[0, 1]
    samples = (samples + 1.0) / 2.0
    samples = np.clip(samples, 0, 1)
    
    N = samples.shape[0]
    ncol = (N + nrow - 1) // nrow
    
    # 创建图像网格
    fig = plt.figure(figsize=(nrow * 3, ncol * 3))
    gs = gridspec.GridSpec(ncol, nrow, figure=fig)
    
    for i in range(N):
        row = i // nrow
        col = i % nrow
        
        ax = fig.add_subplot(gs[row, col])
        
        # 显示三通道合成图像
        # Channel 0: Red, Channel 1: Green, Channel 2: Blue
        rgb_image = np.transpose(samples[i], (1, 2, 0))
        ax.imshow(rgb_image)
        ax.set_title(f'Sample {i+1}')
        ax.axis('off')
    
    plt.suptitle(title, fontsize=16)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        logger.info(f"样本可视化已保存: {save_path}")
    
    plt.show()

def save_sample_grid(samples: torch.Tensor, save_path: str, 
                    nrow: int = 8, title: str = "Samples") -> None:
    """
    保存样本网格图像
    
    Args:
        samples: 样本张量 [N, 3, 32, 32]
        save_path: 保存路径
        nrow: 每行样本数
        title: 图像标题
    """
    visualize_samples(samples, title, save_path, nrow)

def compare_real_fake(real_samples: torch.Tensor, fake_samples: torch.Tensor,
                     save_path: Optional[str] = None, num_pairs: int = 4) -> None:
    """
    对比真实和生成样本
    
    Args:
        real_samples: 真实样本 [N, 3, 32, 32]
        fake_samples: 生成样本 [N, 3, 32, 32]
        save_path: 保存路径
        num_pairs: 对比对数
    """
    # 转换为numpy
    if isinstance(real_samples, torch.Tensor):
        real_samples = real_samples.detach().cpu().numpy()
    if isinstance(fake_samples, torch.Tensor):
        fake_samples = fake_samples.detach().cpu().numpy()
    
    # 归一化
    real_samples = (real_samples + 1.0) / 2.0
    fake_samples = (fake_samples + 1.0) / 2.0
    real_samples = np.clip(real_samples, 0, 1)
    fake_samples = np.clip(fake_samples, 0, 1)
    
    # 创建对比图
    fig, axes = plt.subplots(2, num_pairs, figsize=(num_pairs * 3, 6))
    
    for i in range(num_pairs):
        # 真实样本
        real_rgb = np.transpose(real_samples[i], (1, 2, 0))
        axes[0, i].imshow(real_rgb)
        axes[0, i].set_title(f'Real {i+1}')
        axes[0, i].axis('off')
        
        # 生成样本
        fake_rgb = np.transpose(fake_samples[i], (1, 2, 0))
        axes[1, i].imshow(fake_rgb)
        axes[1, i].set_title(f'Generated {i+1}')
        axes[1, i].axis('off')
    
    plt.suptitle('Real vs Generated Samples', fontsize=16)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        logger.info(f"对比图已保存: {save_path}")
    
    plt.show()

def plot_training_curves(training_history: Dict[str, List[Dict[str, float]]],
                        save_path: Optional[str] = None) -> None:
    """
    绘制训练曲线
    
    Args:
        training_history: 训练历史数据
        save_path: 保存路径
    """
    # 提取关键指标
    stages = ['stage1', 'stage2', 'stage3']
    stage_names = ['VAE预训练', 'WGAN-GP集成', '联合优化']
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 损失曲线
    loss_keys = [
        'weighted_vae_loss', 'weighted_g_adversarial_loss', 'weighted_physics_loss',
        'discriminator_total_loss', 'generator_total_loss', 'epoch_time'
    ]
    
    loss_titles = [
        'VAE Loss', 'Generator Adversarial Loss', 'Physics Loss',
        'Discriminator Loss', 'Generator Total Loss', 'Epoch Time (s)'
    ]
    
    for idx, (loss_key, loss_title) in enumerate(zip(loss_keys, loss_titles)):
        row = idx // 3
        col = idx % 3
        ax = axes[row, col]
        
        epoch_offset = 0
        for stage_idx, stage in enumerate(stages):
            if stage not in training_history or not training_history[stage]:
                continue
                
            stage_data = training_history[stage]
            epochs = [epoch_offset + d['epoch'] for d in stage_data]
            
            # 提取损失值
            if loss_key in stage_data[0]:
                values = [d[loss_key] for d in stage_data]
                ax.plot(epochs, values, label=stage_names[stage_idx], linewidth=2)
            
            epoch_offset += len(stage_data)
        
        ax.set_title(loss_title, fontsize=12)
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Value')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.suptitle('Training Curves - WGAN-GP+VAE', fontsize=16)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        logger.info(f"训练曲线已保存: {save_path}")
    
    plt.show()

def visualize_channel_analysis(samples: torch.Tensor, save_path: Optional[str] = None,
                              num_samples: int = 4) -> None:
    """
    可视化脉冲星三通道分析
    
    Args:
        samples: 样本张量 [N, 3, 32, 32]
        save_path: 保存路径
        num_samples: 分析样本数
    """
    # 转换为numpy
    if isinstance(samples, torch.Tensor):
        samples = samples.detach().cpu().numpy()
    
    # 归一化
    samples = (samples + 1.0) / 2.0
    samples = np.clip(samples, 0, 1)
    
    # 通道名称
    channel_names = ['Period-DM', 'Phase-Subband', 'Phase-Subintegration']
    
    # 创建图像
    fig, axes = plt.subplots(num_samples, 4, figsize=(16, num_samples * 4))
    
    for i in range(num_samples):
        sample = samples[i]
        
        # 显示各个通道
        for c in range(3):
            ax = axes[i, c] if num_samples > 1 else axes[c]
            ax.imshow(sample[c], cmap='viridis')
            ax.set_title(f'Sample {i+1} - {channel_names[c]}')
            ax.axis('off')
        
        # 显示RGB合成
        ax = axes[i, 3] if num_samples > 1 else axes[3]
        rgb_image = np.transpose(sample, (1, 2, 0))
        ax.imshow(rgb_image)
        ax.set_title(f'Sample {i+1} - RGB Composite')
        ax.axis('off')
    
    plt.suptitle('Pulsar Channel Analysis', fontsize=16)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        logger.info(f"通道分析图已保存: {save_path}")
    
    plt.show()

def plot_evaluation_metrics(metrics_history: List[Dict[str, float]],
                           save_path: Optional[str] = None) -> None:
    """
    绘制评估指标曲线
    
    Args:
        metrics_history: 评估指标历史
        save_path: 保存路径
    """
    if not metrics_history:
        logger.warning("没有评估指标数据")
        return
    
    # 提取数据
    epochs = list(range(len(metrics_history)))
    fid_scores = [m.get('fid', 0) for m in metrics_history]
    is_means = [m.get('is_mean', 0) for m in metrics_history]
    is_stds = [m.get('is_std', 0) for m in metrics_history]
    
    # 创建图像
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # FID曲线
    ax1.plot(epochs, fid_scores, 'b-', linewidth=2, label='FID')
    ax1.axhline(y=40, color='r', linestyle='--', label='Target (FID < 40)')
    ax1.set_title('FID Score Over Time')
    ax1.set_xlabel('Evaluation Point')
    ax1.set_ylabel('FID Score')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # IS曲线
    ax2.errorbar(epochs, is_means, yerr=is_stds, fmt='g-', linewidth=2, 
                capsize=5, label='IS')
    ax2.axhline(y=5, color='r', linestyle='--', label='Target (IS > 5)')
    ax2.set_title('Inception Score Over Time')
    ax2.set_xlabel('Evaluation Point')
    ax2.set_ylabel('IS Score')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.suptitle('Generation Quality Metrics', fontsize=16)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        logger.info(f"评估指标图已保存: {save_path}")
    
    plt.show()

def create_training_summary_report(training_history: Dict[str, List[Dict[str, float]]],
                                  final_metrics: Dict[str, float],
                                  save_dir: str) -> None:
    """
    创建训练总结报告
    
    Args:
        training_history: 训练历史
        final_metrics: 最终评估指标
        save_dir: 保存目录
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 绘制训练曲线
    plot_training_curves(
        training_history, 
        os.path.join(save_dir, 'training_curves.png')
    )
    
    # 创建文本报告
    report_path = os.path.join(save_dir, 'training_report.txt')
    with open(report_path, 'w') as f:
        f.write("WGAN-GP+VAE Training Summary Report\n")
        f.write("=" * 50 + "\n\n")
        
        # 训练配置
        f.write("Training Configuration:\n")
        f.write(f"  Total Epochs: {sum(len(h) for h in training_history.values())}\n")
        f.write(f"  Stage 1 (VAE): {len(training_history.get('stage1', []))} epochs\n")
        f.write(f"  Stage 2 (WGAN-GP): {len(training_history.get('stage2', []))} epochs\n")
        f.write(f"  Stage 3 (Joint): {len(training_history.get('stage3', []))} epochs\n\n")
        
        # 最终指标
        f.write("Final Evaluation Metrics:\n")
        for key, value in final_metrics.items():
            f.write(f"  {key}: {value:.4f}\n")
        
        # 目标达成情况
        f.write("\nTarget Achievement:\n")
        if 'fid' in final_metrics:
            fid_target = final_metrics['fid'] < 40
            f.write(f"  FID < 40: {'✓' if fid_target else '✗'} ({final_metrics['fid']:.2f})\n")
        
        if 'is_mean' in final_metrics:
            is_target = final_metrics['is_mean'] > 5
            f.write(f"  IS > 5: {'✓' if is_target else '✗'} ({final_metrics['is_mean']:.2f})\n")
    
    logger.info(f"训练总结报告已保存: {report_path}")
