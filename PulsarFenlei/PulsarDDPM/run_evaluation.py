#!/usr/bin/env python3
"""
WGAN-GP+VAE模型评估启动脚本

使用现有的evaluation模块进行完整评估：
- 调用 fid_is_evaluator.py 进行FID/IS计算
- 调用 visualization.py 进行样本可视化
- 生成通道级别的详细分析报告
"""

import sys
import os
sys.path.insert(0, os.getcwd())

import torch
import argparse
import json
from datetime import datetime
from pathlib import Path

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='WGAN-GP+VAE模型评估')
    
    parser.add_argument('--checkpoint', type=str, required=True, 
                       help='模型检查点路径')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results',
                       help='评估结果输出目录')
    parser.add_argument('--num_samples', type=int, default=1000,
                       help='生成样本数量')
    parser.add_argument('--batch_size', type=int, default=32,
                       help='评估批次大小')
    parser.add_argument('--device', type=str, default='auto',
                       help='计算设备')
    parser.add_argument('--save_samples', action='store_true',
                       help='保存生成的样本')
    parser.add_argument('--channel_analysis', action='store_true',
                       help='进行通道级别分析')
    
    return parser.parse_args()

def load_model_and_generate(checkpoint_path, num_samples, batch_size, device):
    """加载模型并生成样本"""
    print(f"🔄 加载模型: {checkpoint_path}")
    
    try:
        from models.wgan_vae import PulsarWGANVAE
        
        # 创建并加载模型
        model = PulsarWGANVAE(latent_dim=64).to(device)
        checkpoint = torch.load(checkpoint_path, map_location=device)
        
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model.eval()
        print(f"✅ 模型加载成功: {model.total_params/1e6:.2f}M 参数")
        
        # 生成样本
        print(f"🎨 生成 {num_samples} 个样本...")
        generated_samples = []
        num_batches = (num_samples + batch_size - 1) // batch_size
        
        with torch.no_grad():
            for i in range(num_batches):
                current_batch_size = min(batch_size, num_samples - i * batch_size)
                z = torch.randn(current_batch_size, model.latent_dim).to(device)
                fake_samples = model.vae.decode(z)
                generated_samples.append(fake_samples.cpu())
        
        generated_data = torch.cat(generated_samples, dim=0)[:num_samples]
        print(f"✅ 样本生成完成: {generated_data.shape}")
        
        return model, generated_data
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None

def load_real_data(batch_size, device):
    """加载真实数据"""
    print("📊 加载真实HTRU1数据...")
    
    try:
        from utils.data_loader import create_htru1_dataloader
        
        dataloader = create_htru1_dataloader(
            batch_size=batch_size,
            augment=False,
            num_workers=0
        )
        
        # 收集真实样本
        real_samples = []
        for batch in dataloader:
            real_samples.append(batch.to(device))
        
        real_data = torch.cat(real_samples, dim=0)
        print(f"✅ 真实数据加载完成: {real_data.shape}")
        
        return real_data
        
    except Exception as e:
        print(f"❌ 真实数据加载失败: {e}")
        return None

def run_fid_is_evaluation(real_data, fake_data, device, channel_analysis=False):
    """运行FID/IS评估"""
    print("📈 计算FID/IS指标...")
    
    try:
        from evaluation.fid_is_evaluator import compute_fid, compute_is
        
        # 整体评估
        fid_score = compute_fid(real_data, fake_data, device)
        is_mean, is_std = compute_is(fake_data, device)
        
        metrics = {
            'overall': {
                'fid': fid_score,
                'is_mean': is_mean,
                'is_std': is_std
            }
        }
        
        print(f"✅ 整体FID: {fid_score:.2f}")
        print(f"✅ 整体IS: {is_mean:.2f} ± {is_std:.2f}")
        
        # 通道级别评估（如果启用）
        if channel_analysis:
            print("🔬 进行通道级别分析...")
            channel_names = ["Period-DM", "Phase-Subband", "Phase-Subintegration"]
            channel_metrics = {}
            
            for ch_idx, ch_name in enumerate(channel_names):
                try:
                    # 提取单通道并复制为3通道
                    real_ch = real_data[:, ch_idx:ch_idx+1].repeat(1, 3, 1, 1)
                    fake_ch = fake_data[:, ch_idx:ch_idx+1].repeat(1, 3, 1, 1)
                    
                    ch_fid = compute_fid(real_ch, fake_ch, device)
                    ch_is_mean, ch_is_std = compute_is(fake_ch, device)
                    
                    channel_metrics[f'channel_{ch_idx}'] = {
                        'name': ch_name,
                        'fid': ch_fid,
                        'is_mean': ch_is_mean,
                        'is_std': ch_is_std
                    }
                    
                    print(f"  ✅ {ch_name}: FID={ch_fid:.2f}, IS={ch_is_mean:.2f}±{ch_is_std:.2f}")
                    
                except Exception as e:
                    print(f"  ❌ {ch_name} 分析失败: {e}")
            
            metrics['channels'] = channel_metrics
        
        return metrics
        
    except Exception as e:
        print(f"❌ FID/IS计算失败: {e}")
        return None

def run_visualization(real_data, fake_data, output_dir, channel_analysis=False):
    """运行可视化"""
    print("🖼️ 生成可视化...")
    
    try:
        from evaluation.visualization import (
            save_sample_grid, compare_real_fake, 
            visualize_channel_analysis
        )
        
        # 基础可视化
        save_sample_grid(fake_data[:64], 
                        os.path.join(output_dir, 'generated_samples.png'),
                        title="Generated Pulsar Samples")
        
        save_sample_grid(real_data[:64], 
                        os.path.join(output_dir, 'real_samples.png'),
                        title="Real Pulsar Samples")
        
        compare_real_fake(real_data[:8], fake_data[:8],
                         os.path.join(output_dir, 'comparison.png'))
        
        print("✅ 基础可视化完成")
        
        # 通道分析可视化（如果启用）
        if channel_analysis:
            visualize_channel_analysis(fake_data[:4],
                                     os.path.join(output_dir, 'channel_analysis.png'))
            print("✅ 通道分析可视化完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        return False

def generate_report(metrics, output_dir):
    """生成评估报告"""
    print("📋 生成评估报告...")
    
    try:
        # JSON报告
        report_data = {
            'evaluation_time': datetime.now().isoformat(),
            'metrics': metrics,
            'performance_assessment': {
                'fid_target': 40,
                'is_target': 5,
                'fid_achieved': metrics['overall']['fid'] < 40,
                'is_achieved': metrics['overall']['is_mean'] > 5
            }
        }
        
        json_path = os.path.join(output_dir, 'evaluation_report.json')
        with open(json_path, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        # Markdown报告
        md_path = os.path.join(output_dir, 'evaluation_report.md')
        with open(md_path, 'w') as f:
            f.write("# WGAN-GP+VAE模型评估报告\n\n")
            f.write(f"**评估时间**: {report_data['evaluation_time']}\n\n")
            
            f.write("## 生成质量指标\n\n")
            overall = metrics['overall']
            f.write(f"- **FID**: {overall['fid']:.2f} (目标: <40)\n")
            f.write(f"- **IS**: {overall['is_mean']:.2f} ± {overall['is_std']:.2f} (目标: >5)\n\n")
            
            if 'channels' in metrics:
                f.write("### 通道级别指标\n\n")
                f.write("| 通道 | FID | IS |\n")
                f.write("|------|-----|----|\n")
                for ch_key, ch_data in metrics['channels'].items():
                    name = ch_data['name']
                    fid = ch_data['fid']
                    is_mean = ch_data['is_mean']
                    is_std = ch_data['is_std']
                    f.write(f"| {name} | {fid:.2f} | {is_mean:.2f}±{is_std:.2f} |\n")
        
        print(f"✅ 报告保存到: {json_path}, {md_path}")
        return True
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    print("🚀 WGAN-GP+VAE模型评估")
    print(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    print(f"使用设备: {device}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载模型和生成样本
    model, fake_data = load_model_and_generate(
        args.checkpoint, args.num_samples, args.batch_size, device
    )
    if model is None:
        return 1
    
    # 加载真实数据
    real_data = load_real_data(args.batch_size, device)
    if real_data is None:
        return 1
    
    # 运行评估
    metrics = run_fid_is_evaluation(real_data, fake_data, device, args.channel_analysis)
    if metrics is None:
        return 1
    
    # 生成可视化
    run_visualization(real_data, fake_data, args.output_dir, args.channel_analysis)
    
    # 生成报告
    generate_report(metrics, args.output_dir)
    
    # 保存样本（如果需要）
    if args.save_samples:
        torch.save(fake_data, os.path.join(args.output_dir, 'generated_samples.pt'))
        torch.save(real_data, os.path.join(args.output_dir, 'real_samples.pt'))
        print(f"✅ 样本数据保存到: {args.output_dir}")
    
    # 输出结果
    print("\n" + "=" * 60)
    print("🎉 评估完成！")
    overall = metrics['overall']
    print(f"📊 FID: {overall['fid']:.2f} (目标: <40)")
    print(f"📊 IS: {overall['is_mean']:.2f} ± {overall['is_std']:.2f} (目标: >5)")
    print(f"📁 结果保存在: {args.output_dir}")
    
    # 返回状态
    if overall['fid'] < 40 and overall['is_mean'] > 5:
        print("🎯 所有性能目标均已达成！")
        return 0
    else:
        print("⚠️ 部分性能目标未达成")
        return 1

if __name__ == "__main__":
    exit(main())
