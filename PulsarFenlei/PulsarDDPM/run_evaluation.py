#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行DDPM模型评估的脚本
"""

import os
import sys
import argparse
import logging
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, "evaluation.log"))
    ]
)
logger = logging.getLogger("evaluation")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行DDPM模型评估")

    # 基本参数
    parser.add_argument("--checkpoint_path", type=str,
                        default=os.path.join(project_root, "outputs/test_run/checkpoints/best_checkpoint.pt"),
                        help="模型检查点路径")
    parser.add_argument("--data_path", type=str,
                        default="/Pulsar/PulsarFenlei/data/htru1-batches-py/data_batch_1",
                        help="训练数据集路径（改为使用训练集以保持FID评估一致性）")
    parser.add_argument("--inception_model_path", type=str,
                        default="/Pulsar/PulsarFenlei/PulsarDDPM/evaluation/inception_v3_google-0cc3c7bd.pth",
                        help="Inception模型路径")
    parser.add_argument("--num_samples", type=int,
                        default=995,
                        help="生成样本数量，默认为995（HTRU1训练集中的正样本数量）")
    parser.add_argument("--batch_size", type=int,
                        default=16,
                        help="批处理大小")
    parser.add_argument("--device", type=str,
                        default="cuda" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "cpu",
                        help="使用设备 (cuda, cpu)")
    parser.add_argument("--max_real_samples", type=int,
                        default=None,
                        help="使用的最大真实样本数量")
    parser.add_argument("--verbose", action="store_true",
                        help="启用详细日志")
    parser.add_argument("--seed", type=int,
                        default=None,
                        help="随机种子，用于确保结果可重现。如果不指定，将使用随机种子")

    args = parser.parse_args()

    # 设置随机种子（在所有其他操作之前）
    if args.seed is not None:
        try:
            from utils.seed_utils import set_seed_for_evaluation
            actual_seed = set_seed_for_evaluation(args.seed)
            logger.info(f"随机种子已设置为 {actual_seed}，确保结果可重现")
        except ImportError:
            logger.warning("无法导入seed_utils，使用基本的随机种子设置")
            import torch
            import numpy as np
            import random

            torch.manual_seed(args.seed)
            torch.cuda.manual_seed_all(args.seed)
            np.random.seed(args.seed)
            random.seed(args.seed)
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
            logger.info(f"基本随机种子已设置为 {args.seed}")

    # 设置日志级别
    if args.verbose:
        logger.setLevel(logging.DEBUG)
        logger.info("详细日志已启用")

    # 打印参数
    logger.info("评估参数:")
    for arg in vars(args):
        logger.info(f"  {arg}: {getattr(args, arg)}")

    # 导入评估模块
    # 确保evaluation目录在路径中
    eval_dir = os.path.join(project_root, "evaluation")
    if eval_dir not in sys.path:
        sys.path.insert(0, eval_dir)

    # 确保当前目录也在路径中
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    try:
        # 尝试直接从evaluation包导入
        from evaluation.evaluate_model import main as evaluate_main
        logger.info("成功从evaluation包导入评估模块")
    except ImportError as e:
        logger.error(f"从evaluation包导入失败: {e}")

        try:
            # 尝试直接导入模块
            import evaluate_model
            evaluate_main = evaluate_model.main
            logger.info("成功直接导入evaluate_model模块")
        except ImportError as e2:
            logger.error(f"直接导入evaluate_model模块失败: {e2}")

            try:
                # 尝试使用importlib动态导入
                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "evaluate_model",
                    os.path.join(eval_dir, "evaluate_model.py")
                )
                evaluate_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(evaluate_module)
                evaluate_main = evaluate_module.main
                logger.info("成功使用importlib动态导入evaluate_model模块")
            except Exception as e3:
                logger.error(f"所有导入方法都失败: {e3}")
                return

    # 运行评估
    logger.info("开始评估...")
    start_time = time.time()

    try:
        evaluate_main(args)
        logger.info(f"评估完成，总耗时: {time.time() - start_time:.2f} 秒")
    except Exception as e:
        logger.error(f"评估过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
