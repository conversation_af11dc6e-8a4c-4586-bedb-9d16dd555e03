#!/usr/bin/env python3
"""
物理约束损失函数模块
实现脉冲星三通道的物理约束：周期性、相位一致性、幅度保持

基于WGAN-GP+VAE实施计划的技术规格：
- 周期性约束 (40%权重): FFT功率谱密度匹配
- 相位一致性约束 (35%权重): 通道间相关性保持
- 幅度保持约束 (25%权重): 统计特征匹配
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple

def compute_periodicity_loss(original: torch.Tensor, reconstructed: torch.Tensor) -> torch.Tensor:
    """
    计算周期性约束损失 (主要针对Channel 0: Period-DM surface)
    
    Args:
        original: 原始图像 [batch_size, 3, 32, 32]
        reconstructed: 重建图像 [batch_size, 3, 32, 32]
        
    Returns:
        periodicity_loss: 周期性损失
    """
    # 提取Channel 0 (Period-DM surface)
    orig_period_dm = original[:, 0:1, :, :]  # [batch_size, 1, 32, 32]
    recon_period_dm = reconstructed[:, 0:1, :, :]
    
    # FFT变换计算功率谱密度 (添加数值稳定性)
    orig_fft = torch.fft.fft2(orig_period_dm)
    recon_fft = torch.fft.fft2(recon_period_dm)

    # 功率谱密度 (添加小常数防止数值问题)
    orig_psd = torch.abs(orig_fft) ** 2 + 1e-8
    recon_psd = torch.abs(recon_fft) ** 2 + 1e-8

    # 归一化功率谱密度
    orig_psd = orig_psd / (orig_psd.sum(dim=[2, 3], keepdim=True) + 1e-8)
    recon_psd = recon_psd / (recon_psd.sum(dim=[2, 3], keepdim=True) + 1e-8)

    # 功率谱密度匹配损失
    psd_loss = F.mse_loss(recon_psd, orig_psd)

    # 简化的周期性损失 (使用标准差而非自相关)
    orig_std = torch.std(orig_period_dm, dim=[2, 3])
    recon_std = torch.std(recon_period_dm, dim=[2, 3])
    std_loss = F.mse_loss(recon_std, orig_std)
    
    # 组合周期性损失
    periodicity_loss = 0.7 * psd_loss + 0.3 * std_loss
    
    return periodicity_loss

def compute_phase_consistency_loss(original: torch.Tensor, reconstructed: torch.Tensor) -> torch.Tensor:
    """
    计算相位一致性约束损失 (主要针对Channel 1&2: Phase surfaces)
    
    Args:
        original: 原始图像 [batch_size, 3, 32, 32]
        reconstructed: 重建图像 [batch_size, 3, 32, 32]
        
    Returns:
        phase_consistency_loss: 相位一致性损失
    """
    # 提取相位通道 (Channel 1: Phase-Subband, Channel 2: Phase-Subintegration)
    orig_phase_subband = original[:, 1, :, :]  # [batch_size, 32, 32]
    orig_phase_subint = original[:, 2, :, :]
    recon_phase_subband = reconstructed[:, 1, :, :]
    recon_phase_subint = reconstructed[:, 2, :, :]
    
    # 通道间相关性计算 (添加数值稳定性)
    def compute_correlation(x, y):
        """计算两个通道间的相关性"""
        x_flat = x.flatten(1)  # [batch_size, 32*32]
        y_flat = y.flatten(1)

        # 标准化 (添加小常数防止除零)
        x_norm = F.normalize(x_flat + 1e-8, dim=1, eps=1e-8)
        y_norm = F.normalize(y_flat + 1e-8, dim=1, eps=1e-8)

        # 相关性
        correlation = torch.sum(x_norm * y_norm, dim=1)  # [batch_size]
        return torch.clamp(correlation.mean(), -1.0, 1.0)  # 限制范围
    
    # 原始和重建的通道间相关性
    orig_correlation = compute_correlation(orig_phase_subband, orig_phase_subint)
    recon_correlation = compute_correlation(recon_phase_subband, recon_phase_subint)
    
    # 相关性保持损失
    correlation_loss = F.mse_loss(recon_correlation, orig_correlation)
    
    # 相位连续性损失 (梯度一致性)
    def compute_gradient_consistency(orig, recon):
        """计算梯度一致性"""
        # 计算梯度
        orig_grad_x = torch.diff(orig, dim=2)  # x方向梯度
        orig_grad_y = torch.diff(orig, dim=1)  # y方向梯度
        recon_grad_x = torch.diff(recon, dim=2)
        recon_grad_y = torch.diff(recon, dim=1)
        
        # 梯度匹配损失
        grad_loss_x = F.mse_loss(recon_grad_x, orig_grad_x)
        grad_loss_y = F.mse_loss(recon_grad_y, orig_grad_y)
        
        return grad_loss_x + grad_loss_y
    
    # 各相位通道的梯度一致性
    subband_grad_loss = compute_gradient_consistency(orig_phase_subband, recon_phase_subband)
    subint_grad_loss = compute_gradient_consistency(orig_phase_subint, recon_phase_subint)
    
    # 组合相位一致性损失
    phase_consistency_loss = 0.4 * correlation_loss + 0.3 * subband_grad_loss + 0.3 * subint_grad_loss
    
    return phase_consistency_loss

def compute_amplitude_preservation_loss(original: torch.Tensor, reconstructed: torch.Tensor) -> torch.Tensor:
    """
    计算幅度保持约束损失 (所有通道的统计特征匹配)
    
    Args:
        original: 原始图像 [batch_size, 3, 32, 32]
        reconstructed: 重建图像 [batch_size, 3, 32, 32]
        
    Returns:
        amplitude_loss: 幅度保持损失
    """
    # 统计特征计算
    def compute_statistics(x):
        """计算统计特征"""
        # 按通道计算统计量
        mean = torch.mean(x, dim=[2, 3])  # [batch_size, 3]
        std = torch.std(x, dim=[2, 3])    # [batch_size, 3]
        
        # 计算峰度 (四阶矩) - 添加数值稳定性
        x_centered = x - mean.unsqueeze(-1).unsqueeze(-1)
        fourth_moment = torch.mean(x_centered ** 4, dim=[2, 3])
        kurtosis = fourth_moment / (std ** 4 + 1e-6)
        kurtosis = torch.clamp(kurtosis, 0.0, 100.0)  # 限制峰度范围
        
        return mean, std, kurtosis
    
    # 原始和重建图像的统计特征
    orig_mean, orig_std, orig_kurtosis = compute_statistics(original)
    recon_mean, recon_std, recon_kurtosis = compute_statistics(reconstructed)
    
    # 统计特征匹配损失
    mean_loss = F.mse_loss(recon_mean, orig_mean)
    std_loss = F.mse_loss(recon_std, orig_std)
    kurtosis_loss = F.mse_loss(recon_kurtosis, orig_kurtosis)
    
    # 简化的幅度分布匹配损失 (使用MSE而非KL散度)
    def compute_histogram_loss(orig, recon, bins=16):
        """计算直方图匹配损失 - 数值稳定版本"""
        # 将数据展平
        orig_flat = orig.flatten()
        recon_flat = recon.flatten()

        # 计算直方图
        orig_hist = torch.histc(orig_flat, bins=bins, min=-1.0, max=1.0)
        recon_hist = torch.histc(recon_flat, bins=bins, min=-1.0, max=1.0)

        # 归一化 (添加更大的稳定常数)
        orig_hist = orig_hist / (orig_hist.sum() + 1e-6) + 1e-8
        recon_hist = recon_hist / (recon_hist.sum() + 1e-6) + 1e-8

        # 使用MSE而非KL散度 (更稳定)
        hist_loss = F.mse_loss(recon_hist, orig_hist)

        # 限制损失范围
        hist_loss = torch.clamp(hist_loss, 0.0, 10.0)

        return hist_loss
    
    # 各通道的直方图匹配损失
    hist_losses = []
    for c in range(3):
        hist_loss = compute_histogram_loss(original[:, c, :, :], reconstructed[:, c, :, :])
        hist_losses.append(hist_loss)
    
    avg_hist_loss = torch.stack(hist_losses).mean()
    
    # 组合幅度保持损失
    amplitude_loss = 0.3 * mean_loss + 0.3 * std_loss + 0.2 * kurtosis_loss + 0.2 * avg_hist_loss
    
    return amplitude_loss

class PulsarPhysicsLoss(nn.Module):
    """
    脉冲星物理约束损失函数
    
    集成三种物理约束：
    - 周期性约束 (40%权重)
    - 相位一致性约束 (35%权重)  
    - 幅度保持约束 (25%权重)
    """
    
    def __init__(self, periodicity_weight: float = 0.4, 
                 phase_weight: float = 0.35, amplitude_weight: float = 0.25):
        super().__init__()
        self.periodicity_weight = periodicity_weight
        self.phase_weight = phase_weight
        self.amplitude_weight = amplitude_weight
        
        # 验证权重和为1
        total_weight = periodicity_weight + phase_weight + amplitude_weight
        assert abs(total_weight - 1.0) < 1e-6, f"权重和应为1.0，当前为{total_weight}"
    
    def forward(self, original: torch.Tensor, reconstructed: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算物理约束损失
        
        Args:
            original: 原始图像 [batch_size, 3, 32, 32]
            reconstructed: 重建图像 [batch_size, 3, 32, 32]
            
        Returns:
            loss_dict: 包含各项物理损失的字典
        """
        # 计算各项物理损失
        periodicity_loss = compute_periodicity_loss(original, reconstructed)
        phase_consistency_loss = compute_phase_consistency_loss(original, reconstructed)
        amplitude_loss = compute_amplitude_preservation_loss(original, reconstructed)
        
        # 严格检查各项损失的有效性
        def validate_loss(loss_tensor, name):
            """严格验证损失值，发现问题立即报错"""
            if torch.isnan(loss_tensor).any():
                raise ValueError(f"{name}损失包含NaN值: {loss_tensor}")
            if torch.isinf(loss_tensor).any():
                raise ValueError(f"{name}损失包含Inf值: {loss_tensor}")
            if (loss_tensor < 0).any():
                raise ValueError(f"{name}损失包含负值: {loss_tensor}")
            return loss_tensor

        # 严格验证各项损失
        periodicity_loss = validate_loss(periodicity_loss, "周期性")
        phase_consistency_loss = validate_loss(phase_consistency_loss, "相位一致性")
        amplitude_loss = validate_loss(amplitude_loss, "幅度保持")

        # 加权组合
        total_physics_loss = (
            self.periodicity_weight * periodicity_loss +
            self.phase_weight * phase_consistency_loss +
            self.amplitude_weight * amplitude_loss
        )

        # 最终检查
        total_physics_loss = validate_loss(total_physics_loss, "总物理")

        return {
            'total_physics_loss': total_physics_loss,
            'periodicity_loss': periodicity_loss,
            'phase_consistency_loss': phase_consistency_loss,
            'amplitude_loss': amplitude_loss
        }
