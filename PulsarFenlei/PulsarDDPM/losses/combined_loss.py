#!/usr/bin/env python3
"""
组合损失函数模块
实现WGAN-GP+VAE混合架构的统一损失函数

基于WGAN-GP+VAE实施计划的损失权重配置：
- VAE损失 (40%权重): 重建损失 + KL散度
- WGAN-GP损失 (35%权重): 对抗损失 + 梯度惩罚
- 物理约束损失 (25%权重): 周期性 + 相位一致性 + 幅度保持
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional
from .physics_loss import PulsarPhysicsLoss
from models.vae import compute_vae_loss
from models.discriminator import compute_wgan_gp_loss

class PulsarCombinedLoss(nn.Module):
    """
    脉冲星混合架构组合损失函数
    
    集成三部分损失：
    - VAE损失 (40%): 重建 + KL散度
    - WGAN-GP损失 (35%): 对抗 + 梯度惩罚
    - 物理约束损失 (25%): 物理特征保持
    """
    
    def __init__(self, 
                 vae_weight: float = 0.4,
                 wgan_weight: float = 0.35, 
                 physics_weight: float = 0.25,
                 beta_vae: float = 1.0,
                 lambda_gp: float = 10.0):
        super().__init__()
        
        # 损失权重
        self.vae_weight = vae_weight
        self.wgan_weight = wgan_weight
        self.physics_weight = physics_weight
        
        # VAE和WGAN-GP参数
        self.beta_vae = beta_vae
        self.lambda_gp = lambda_gp
        
        # 验证权重和为1
        total_weight = vae_weight + wgan_weight + physics_weight
        assert abs(total_weight - 1.0) < 1e-6, f"权重和应为1.0，当前为{total_weight}"
        
        # 物理约束损失模块
        self.physics_loss = PulsarPhysicsLoss()
        
        print(f"PulsarCombinedLoss初始化:")
        print(f"  VAE权重: {vae_weight} (β={beta_vae})")
        print(f"  WGAN-GP权重: {wgan_weight} (λ={lambda_gp})")
        print(f"  物理约束权重: {physics_weight}")
    
    def compute_vae_loss_component(self, x_recon: torch.Tensor, x_original: torch.Tensor,
                                  mu: torch.Tensor, logvar: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算VAE损失组件"""
        vae_loss_dict = compute_vae_loss(x_recon, x_original, mu, logvar, self.beta_vae)
        
        # 添加权重
        weighted_vae_loss = self.vae_weight * vae_loss_dict['total_loss']
        
        return {
            'weighted_vae_loss': weighted_vae_loss,
            'vae_recon_loss': vae_loss_dict['recon_loss'],
            'vae_kl_loss': vae_loss_dict['kl_loss'],
            'vae_total_loss': vae_loss_dict['total_loss']
        }
    
    def compute_wgan_loss_component(self, discriminator: nn.Module, 
                                   real_samples: torch.Tensor, fake_samples: torch.Tensor,
                                   device: torch.device) -> Dict[str, torch.Tensor]:
        """计算WGAN-GP损失组件"""
        wgan_loss_dict = compute_wgan_gp_loss(
            discriminator, real_samples, fake_samples, device, self.lambda_gp
        )
        
        # 添加权重
        weighted_d_loss = self.wgan_weight * wgan_loss_dict['d_loss']
        weighted_g_loss = self.wgan_weight * wgan_loss_dict['g_loss']
        
        return {
            'weighted_d_loss': weighted_d_loss,
            'weighted_g_loss': weighted_g_loss,
            'wgan_wasserstein_distance': wgan_loss_dict['wasserstein_distance'],
            'wgan_gradient_penalty': wgan_loss_dict['gradient_penalty'],
            'wgan_d_real_score': wgan_loss_dict['d_real_score'],
            'wgan_d_fake_score': wgan_loss_dict['d_fake_score']
        }
    
    def compute_physics_loss_component(self, original: torch.Tensor, 
                                     reconstructed: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算物理约束损失组件"""
        physics_loss_dict = self.physics_loss(original, reconstructed)
        
        # 添加权重
        weighted_physics_loss = self.physics_weight * physics_loss_dict['total_physics_loss']
        
        return {
            'weighted_physics_loss': weighted_physics_loss,
            'physics_periodicity_loss': physics_loss_dict['periodicity_loss'],
            'physics_phase_consistency_loss': physics_loss_dict['phase_consistency_loss'],
            'physics_amplitude_loss': physics_loss_dict['amplitude_loss'],
            'physics_total_loss': physics_loss_dict['total_physics_loss']
        }
    
    def compute_generator_loss(self, x_original: torch.Tensor, x_recon: torch.Tensor,
                              mu: torch.Tensor, logvar: torch.Tensor,
                              discriminator: nn.Module, device: torch.device) -> Dict[str, torch.Tensor]:
        """
        计算生成器总损失 (VAE + 生成器对抗损失 + 物理约束)

        Args:
            x_original: 原始图像 [batch_size, 3, 32, 32]
            x_recon: 重建图像 [batch_size, 3, 32, 32]
            mu: VAE均值 [batch_size, latent_dim]
            logvar: VAE对数方差 [batch_size, latent_dim]
            discriminator: 判别器模型
            device: 设备

        Returns:
            loss_dict: 包含所有损失项的字典
        """
        # VAE损失
        vae_losses = self.compute_vae_loss_component(x_recon, x_original, mu, logvar)

        # 生成器对抗损失 (只计算生成器部分) - 添加数值稳定性
        if self.wgan_weight > 0:
            d_fake_score = discriminator(x_recon)
            # 检查判别器输出是否有效
            if torch.isnan(d_fake_score).any() or torch.isinf(d_fake_score).any():
                g_adversarial_loss = torch.tensor(0.0, device=device)
            else:
                g_adversarial_loss = -torch.mean(d_fake_score)
                # 限制对抗损失范围
                g_adversarial_loss = torch.clamp(g_adversarial_loss, -100.0, 100.0)
            weighted_g_adversarial_loss = self.wgan_weight * g_adversarial_loss
        else:
            g_adversarial_loss = torch.tensor(0.0, device=device)
            weighted_g_adversarial_loss = torch.tensor(0.0, device=device)

        # 物理约束损失
        if self.physics_weight > 0:
            physics_losses = self.compute_physics_loss_component(x_original, x_recon)
        else:
            physics_losses = {
                'weighted_physics_loss': torch.tensor(0.0, device=device),
                'physics_periodicity_loss': torch.tensor(0.0, device=device),
                'physics_phase_consistency_loss': torch.tensor(0.0, device=device),
                'physics_amplitude_loss': torch.tensor(0.0, device=device),
                'physics_total_loss': torch.tensor(0.0, device=device)
            }

        # 生成器总损失 - 添加NaN检查
        loss_components = [
            vae_losses['weighted_vae_loss'],
            weighted_g_adversarial_loss,
            physics_losses['weighted_physics_loss']
        ]

        # 检查每个组件是否有效
        valid_components = []
        for component in loss_components:
            if not (torch.isnan(component).any() or torch.isinf(component).any()):
                valid_components.append(component)
            else:
                valid_components.append(torch.tensor(0.0, device=device))

        generator_total_loss = sum(valid_components)

        # 最终检查
        if torch.isnan(generator_total_loss).any() or torch.isinf(generator_total_loss).any():
            generator_total_loss = torch.tensor(1.0, device=device)  # 回退值

        # 组合所有损失信息
        loss_dict = {
            'generator_total_loss': generator_total_loss,
            'g_adversarial_loss': g_adversarial_loss,
            'weighted_g_adversarial_loss': weighted_g_adversarial_loss,
            **vae_losses,
            **physics_losses
        }

        return loss_dict
    
    def compute_discriminator_loss(self, discriminator: nn.Module,
                                  real_samples: torch.Tensor, fake_samples: torch.Tensor,
                                  device: torch.device) -> Dict[str, torch.Tensor]:
        """
        计算判别器损失 (纯WGAN-GP损失)
        
        Args:
            discriminator: 判别器模型
            real_samples: 真实样本 [batch_size, 3, 32, 32]
            fake_samples: 生成样本 [batch_size, 3, 32, 32]
            device: 设备
            
        Returns:
            loss_dict: 包含判别器损失项的字典
        """
        wgan_losses = self.compute_wgan_loss_component(
            discriminator, real_samples, fake_samples, device
        )
        
        # 判别器总损失就是WGAN-GP损失
        discriminator_total_loss = wgan_losses['weighted_d_loss']
        
        loss_dict = {
            'discriminator_total_loss': discriminator_total_loss,
            **wgan_losses
        }
        
        return loss_dict

class ProgressiveLossScheduler:
    """
    渐进式损失权重调度器
    
    实现三阶段训练的损失权重调整：
    - 阶段1: VAE预训练 (VAE 100%, WGAN 0%, Physics 0%)
    - 阶段2: WGAN-GP集成 (VAE 60%, WGAN 40%, Physics 0%)
    - 阶段3: 联合优化 (VAE 40%, WGAN 35%, Physics 25%)
    """
    
    def __init__(self):
        self.stage_configs = {
            1: {'vae': 1.0, 'wgan': 0.0, 'physics': 0.0},      # VAE预训练
            2: {'vae': 0.6, 'wgan': 0.4, 'physics': 0.0},      # WGAN-GP集成
            3: {'vae': 0.4, 'wgan': 0.35, 'physics': 0.25}     # 联合优化
        }
        self.current_stage = 1
    
    def set_stage(self, stage: int):
        """设置当前训练阶段"""
        assert stage in self.stage_configs, f"无效阶段: {stage}"
        self.current_stage = stage
        print(f"切换到训练阶段{stage}: {self.stage_configs[stage]}")
    
    def get_current_weights(self) -> Dict[str, float]:
        """获取当前阶段的损失权重"""
        return self.stage_configs[self.current_stage]
    
    def update_loss_function(self, loss_function: PulsarCombinedLoss):
        """更新损失函数的权重"""
        weights = self.get_current_weights()
        loss_function.vae_weight = weights['vae']
        loss_function.wgan_weight = weights['wgan']
        loss_function.physics_weight = weights['physics']
        
        print(f"损失权重已更新: VAE={weights['vae']}, WGAN={weights['wgan']}, Physics={weights['physics']}")

def create_loss_function(stage: int = 3, beta_vae: float = 1.0, 
                        lambda_gp: float = 10.0) -> Tuple[PulsarCombinedLoss, ProgressiveLossScheduler]:
    """
    创建损失函数和调度器
    
    Args:
        stage: 初始训练阶段 (1, 2, 3)
        beta_vae: VAE的KL散度权重
        lambda_gp: WGAN-GP梯度惩罚系数
        
    Returns:
        loss_function: 组合损失函数
        scheduler: 渐进式调度器
    """
    # 创建调度器并设置阶段
    scheduler = ProgressiveLossScheduler()
    scheduler.set_stage(stage)
    
    # 获取当前阶段权重
    weights = scheduler.get_current_weights()
    
    # 创建损失函数
    loss_function = PulsarCombinedLoss(
        vae_weight=weights['vae'],
        wgan_weight=weights['wgan'],
        physics_weight=weights['physics'],
        beta_vae=beta_vae,
        lambda_gp=lambda_gp
    )
    
    return loss_function, scheduler
